
# Flutter模块API接口添加任务模板

## 使用方式
只需提供以下参数即可执行任务：

### 必需参数
1. **目标模块名**：如 `lib_base`、`yyb_auth`、`yyb_basic_training` 等
2. **Java接口定义**：完整的Java接口定义代码

### 可选参数
3. **Android Response Model**：如果提供，将自动生成对应的Dart model并添加到Response泛型中

### 使用示例

#### 基础使用（无model）
```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：lib_base
接口定义：
/**
 * 根据一级单元id查询视频资源
 */
@Headers({"url_name:newhost"})
@GET("/api/math/v50/mathknowledgecontrollerapi/selectvideoresourcelist")
Call<JSONObject> selectvideoresourcelist(@Query("bookId") String bookId, @Query("oneUnitId") String oneUnitId, @Query("userId") String userId);
```

#### 高级使用（包含model）
```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：yyb_auth
接口定义：
/**
 * 用户登录接口
 */
@Headers({"url_name:newhost"})
@POST("/api/auth/v1/login")
Call<LoginResponse> userLogin(@Field("username") String username, @Field("password") String password);

Android Response Model：
public class LoginResponse {
    private String token;
    private String userId;
    private long expireTime;

    // getters and setters...
}
```

---

## 自动化实现逻辑

### 1. 路径自动识别
根据提供的目标模块名自动确定工作路径：

- **lib_base模块** → `package\lib_base\lib\api`
- **其他模块** → `package\{模块名}\lib\src\api`

### 2. Java接口解析
自动从Java接口定义中提取：
- **接口名称**：方法名
- **请求类型**：@GET 或 @POST
- **API路径**：注解中的URL路径
- **参数列表**：@Query 或 @Field 参数
- **接口描述**：注释内容
- **返回类型**：Call<T> 中的泛型类型

### 3. Android Model解析（可选）
如果提供了Android Response Model，自动：
- **解析Java类结构**：字段名、类型、注释
- **转换为Dart语法**：类型映射、命名规范
- **生成完整Dart model**：包含fromJson、toJson方法
- **确定model文件名**：根据Java类名转换

### 4. 自动生成代码

#### 4.1 Dart Model生成（可选，仅当提供Android Model时）
生成路径：`package\{模块名}\lib\model\http\{model_name}.dart`
```dart
import 'package:json_annotation/json_annotation.dart';

part '{model_name}.g.dart';

@JsonSerializable()
class {ModelName} {
  final String? [字段名];
  // ... 其他字段

  const {ModelName}({
    this.[字段名],
    // ... 其他字段
  });

  factory {ModelName}.fromJson(Map<String, dynamic> json) =>
      _${ModelName}FromJson(json);

  Map<String, dynamic> toJson() => _${ModelName}ToJson(this);
}
```

#### 4.2 api.dart 文件（末尾添加）
```dart
/**
 * [从Java注释自动提取]
 *
 * @param [参数名] [参数描述]
 * @return
 */
static const String [接口名称] = "[API路径]";
```

#### 4.3 app_api.dart 文件（末尾添加）
自动根据模块类型和是否提供model选择正确的返回类型：
```dart
/**
 * [从Java注释自动提取]
 */
@[GET/POST]([BaseApi/Api].[接口名称])  // 根据模块自动选择
@Extra(<String, bool>{
  HttpExtraKey.autoLoading: false,
  HttpExtraKey.withoutLogin: false,
  HttpExtraKey.needErrorToast: true,
})
Future<BaseResponse<{ModelName}>> [接口名称](  // 如果提供了model
// Future<BaseResponse> [接口名称](              // 如果没有提供model
  @[Query/Field]("[参数名]") String [参数名],  // 根据请求类型自动选择
);
```

#### 4.4 app_api.dart 导入语句（如果生成了model）
在文件顶部自动添加model导入：
```dart
import 'package:{模块名}/model/http/{model_name}.dart';
```

#### 4.5 api_repository.dart 文件（末尾添加）
自动根据模块类型和是否提供model选择正确的返回类型：
```dart
/**
 * [从Java注释自动提取]
 */
static Future<BaseResponse<{ModelName}>> [接口名称]({  // 如果提供了model
// static Future<BaseResponse> [接口名称]({           // 如果没有提供model
  required String [参数名],
}) {
  return [BaseAppApi/AppApi].instance().[接口名称]([参数列表]);  // 根据模块自动选择
}
```

---

## 自动化规则

### 模块类型识别
- **lib_base** → 使用 `BaseAppApi` 类和 `BaseApi` 常量
- **其他模块** → 使用 `AppApi` 类和 `Api` 常量

### 请求类型识别
- **@GET** → 参数使用 `@Query` 注解
- **@POST** → 参数使用 `@Field` 注解

### 返回类型规则
- **无model**：使用 `Future<BaseResponse>`（无泛型）
- **有model**：使用 `Future<BaseResponse<ModelName>>`（带泛型）

### 固定规则
- ✅ 忽略 `@Headers` 注解
- ✅ 所有代码添加在对应文件的**末尾**
- ✅ 不处理 `.g.dart` 文件
- ✅ 参考目标模块已有接口的实现方式
- ✅ Model生成路径：`package\{模块名}\lib\model\http\`

### Java到Dart类型映射
- `String` → `String?`
- `int` → `int?`
- `long` → `int?`
- `boolean` → `bool?`
- `double` → `double?`
- `List<T>` → `List<T>?`
- 自定义类 → `CustomClass?`

---

## 快速使用模板

### 基础模板（无model）
```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：[模块名]
接口定义：
[完整的Java接口定义代码]
```

### 完整模板（包含model）
```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：[模块名]
接口定义：
[完整的Java接口定义代码]

Android Response Model：
[完整的Java Model类代码]
```

## 执行流程总结
1. **解析目标模块** → 确定工作路径和类名规则
2. **解析Java接口** → 提取接口信息和参数
3. **解析Android Model**（可选）→ 生成Dart model文件
4. **生成API常量** → 在api.dart末尾添加
5. **生成接口定义** → 在app_api.dart末尾添加（含model导入）
6. **生成Repository方法** → 在api_repository.dart末尾添加