
# Flutter模块API接口添加任务模板

## 使用方式
只需提供以下参数即可执行任务：

### 必需参数
1. **目标模块名**：如 `lib_base`、`yyb_auth`、`yyb_basic_training` 等
2. **Java接口定义**：完整的Java接口定义代码

### 使用示例
```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：lib_base
接口定义：
/**
 * 根据一级单元id查询视频资源
 */
@Headers({"url_name:newhost"})
@GET("/api/math/v50/mathknowledgecontrollerapi/selectvideoresourcelist")
Call<JSONObject> selectvideoresourcelist(@Query("bookId") String bookId, @Query("oneUnitId") String oneUnitId, @Query("userId") String userId);
```

---

## 自动化实现逻辑

### 1. 路径自动识别
根据提供的目标模块名自动确定工作路径：

- **lib_base模块** → `package\lib_base\lib\api`
- **其他模块** → `package\{模块名}\lib\src\api`

### 2. Java接口解析
自动从Java接口定义中提取：
- **接口名称**：方法名
- **请求类型**：@GET 或 @POST
- **API路径**：注解中的URL路径
- **参数列表**：@Query 或 @Field 参数
- **接口描述**：注释内容

### 3. 自动生成代码

#### 3.1 api.dart 文件（末尾添加）
```dart
/**
 * [从Java注释自动提取]
 *
 * @param [参数名] [参数描述]
 * @return
 */
static const String [接口名称] = "[API路径]";
```

#### 3.2 app_api.dart 文件（末尾添加）
自动根据模块类型选择正确的类名和常量引用：
```dart
/**
 * [从Java注释自动提取]
 */
@[GET/POST]([BaseApi/Api].[接口名称])  // 根据模块自动选择
@Extra(<String, bool>{
  HttpExtraKey.autoLoading: false,
  HttpExtraKey.withoutLogin: false,
  HttpExtraKey.needErrorToast: true,
})
Future<BaseResponse> [接口名称](
  @[Query/Field]("[参数名]") String [参数名],  // 根据请求类型自动选择
);
```

#### 3.3 api_repository.dart 文件（末尾添加）
自动根据模块类型选择正确的AppApi实例：
```dart
/**
 * [从Java注释自动提取]
 */
static Future<BaseResponse> [接口名称]({
  required String [参数名],
}) {
  return [BaseAppApi/AppApi].instance().[接口名称]([参数列表]);  // 根据模块自动选择
}
```

---

## 自动化规则

### 模块类型识别
- **lib_base** → 使用 `BaseAppApi` 类和 `BaseApi` 常量
- **其他模块** → 使用 `AppApi` 类和 `Api` 常量

### 请求类型识别
- **@GET** → 参数使用 `@Query` 注解
- **@POST** → 参数使用 `@Field` 注解

### 固定规则
- ✅ 忽略 `@Headers` 注解
- ✅ 返回类型统一使用 `Future<BaseResponse>`（无泛型）
- ✅ 所有代码添加在对应文件的**末尾**
- ✅ 不处理 `.g.dart` 文件
- ✅ 参考目标模块已有接口的实现方式

---

## 快速使用
复制以下模板，填入参数即可：

```
请根据ai_task/api_task.md里面的任务描述执行任务

目标模块：[模块名]
接口定义：
[完整的Java接口定义代码]
```