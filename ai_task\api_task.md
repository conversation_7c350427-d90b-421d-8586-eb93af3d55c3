
# 在Flutter模块中添加API接口任务

## 任务描述
在指定的Flutter模块中添加新的API接口，支持GET和POST两种请求类型。

## 支持的模块
- **lib_base模块**：`package\lib_base\lib\api`
- **其他业务模块**：`package\{模块名}\lib\src\api`（如：yyb_auth、yyb_basic_training等）

## 接口示例（GET请求）
```java
/**
 * 根据一级单元id查询视频资源
 */
@Headers({"url_name:newhost"})
@GET("/api/math/v50/mathknowledgecontrollerapi/selectvideoresourcelist")
Call<JSONObject> selectvideoresourcelist(@Query("bookId") String bookId, @Query("oneUnitId") String oneUnitId, @Query("userId") String userId);
```

## 接口示例（POST请求）
```java
/**
 * 提交用户数据
 */
@Headers({"url_name:newhost"})
@POST("/api/user/v50/userapi/submitdata")
Call<JSONObject> submitUserData(@Field("userId") String userId, @Field("data") String data);
```

## 实现要求

### 1. 工作路径
根据目标模块确定工作路径：

#### lib_base模块
- 模块路径：`package\lib_base`
- API工作路径：`package\lib_base\lib\api`

#### 其他业务模块（如yyb_auth、yyb_basic_training等）
- 模块路径：`package\{模块名}`
- API工作路径：`package\{模块名}\lib\src\api`

**注意**：参考目标模块中已有接口的实现方式

### 2. 文件修改顺序和规范
按以下顺序修改文件：

#### 2.1 api.dart 文件
- **位置要求**：新的API常量必须添加在文件**末尾**，不要添加在中间
- 添加API路径常量，格式如下：
```dart
/**
 * 接口功能描述
 *
 * @param 参数1 参数1描述
 * @param 参数2 参数2描述
 * @return
 */
static const String 接口名称 = "API路径";
```

#### 2.2 app_api.dart 文件
- 在对应的AppApi类的末尾添加接口定义
  - **lib_base模块**：在 `BaseAppApi` 类中添加
  - **其他模块**：在 `AppApi` 类中添加
- GET请求使用 `@Query` 注解
- POST请求使用 `@Field` 注解
- **返回类型**：使用 `Future<BaseResponse>` （不要添加泛型，除非明确指定了具体类型）
- 格式如下：
```dart
/**
 * 接口功能描述
 */
@GET(Api.接口名称)  // 或 @POST(Api.接口名称)，注意：lib_base用BaseApi，其他模块用Api
@Extra(<String, bool>{
  HttpExtraKey.autoLoading: false,
  HttpExtraKey.withoutLogin: false,
  HttpExtraKey.needErrorToast: true,
})
Future<BaseResponse> 接口名称(
  @Query("参数1") String 参数1,  // GET请求用@Query
  @Field("参数1") String 参数1,  // POST请求用@Field
);
```

#### 2.3 api_repository.dart 文件
- 在文件末尾添加静态方法
- 返回类型与app_api.dart保持一致
- 调用对应的AppApi实例：
  - **lib_base模块**：调用 `BaseAppApi.instance()`
  - **其他模块**：调用 `AppApi.instance()`
- 格式如下：
```dart
/**
 * 接口功能描述
 */
static Future<BaseResponse> 接口名称({
  required String 参数1,
  required String 参数2,
}) {
  return BaseAppApi.instance().接口名称(参数1, 参数2);  // lib_base模块
  // return AppApi.instance().接口名称(参数1, 参数2);     // 其他模块
}
```

### 3. 注意事项
- **忽略headers**：不需要关注@Headers注解，只关注参数和请求方法
- **不处理.g.dart文件**：不需要执行build_runner，不关心代码生成文件
- **返回类型**：默认使用BaseResponse，不添加泛型（后续会自行添加具体model）
- **参数类型**：GET请求用@Query，POST请求用@Field
- **常量位置**：API常量必须添加在api.dart文件末尾

### 4. GET vs POST 区别
- **GET请求**：参数使用 `@Query` 注解，数据通过URL查询参数传递
- **POST请求**：参数使用 `@Field` 注解，数据通过请求体传递，通常需要 `@FormUrlEncoded` 注解

## 完成标准
1. 在api.dart末尾添加API常量
2. 在app_api.dart末尾添加接口定义（注意类名和常量引用的区别）
3. 在api_repository.dart末尾添加对应方法（注意AppApi实例调用的区别）
4. 所有返回类型使用BaseResponse（无泛型）
5. 参数注解根据请求类型选择（GET用@Query，POST用@Field）

## 模块差异总结
| 项目 | lib_base模块 | 其他业务模块 |
|------|-------------|-------------|
| API路径 | `package\lib_base\lib\api` | `package\{模块名}\lib\src\api` |
| 类名 | `BaseAppApi` | `AppApi` |
| 常量引用 | `BaseApi.接口名称` | `Api.接口名称` |
| 实例调用 | `BaseAppApi.instance()` | `AppApi.instance()` |