
# 在lib_base模块添加API接口任务

## 任务描述
在lib_base模块中添加新的API接口，支持GET和POST两种请求类型。

## 接口示例（GET请求）
```java
/**
 * 根据一级单元id查询视频资源
 */
@Headers({"url_name:newhost"})
@GET("/api/math/v50/mathknowledgecontrollerapi/selectvideoresourcelist")
Call<JSONObject> selectvideoresourcelist(@Query("bookId") String bookId, @Query("oneUnitId") String oneUnitId, @Query("userId") String userId);
```

## 接口示例（POST请求）
```java
/**
 * 提交用户数据
 */
@Headers({"url_name:newhost"})
@POST("/api/user/v50/userapi/submitdata")
Call<JSONObject> submitUserData(@Field("userId") String userId, @Field("data") String data);
```

## 实现要求

### 1. 工作路径
- lib_base模块路径：`package\lib_base`
- 具体工作路径：`package\lib_base\lib\api`
- 参考已有接口的实现方式

### 2. 文件修改顺序和规范
按以下顺序修改文件：

#### 2.1 api.dart 文件
- **位置要求**：新的API常量必须添加在文件**末尾**，不要添加在中间
- 添加API路径常量，格式如下：
```dart
/**
 * 接口功能描述
 *
 * @param 参数1 参数1描述
 * @param 参数2 参数2描述
 * @return
 */
static const String 接口名称 = "API路径";
```

#### 2.2 app_api.dart 文件
- 在BaseAppApi类的末尾添加接口定义
- GET请求使用 `@Query` 注解
- POST请求使用 `@Field` 注解
- **返回类型**：使用 `Future<BaseResponse>` （不要添加泛型，除非明确指定了具体类型）
- 格式如下：
```dart
/**
 * 接口功能描述
 */
@GET(BaseApi.接口名称)  // 或 @POST(BaseApi.接口名称)
@Extra(<String, bool>{
  HttpExtraKey.autoLoading: false,
  HttpExtraKey.withoutLogin: false,
  HttpExtraKey.needErrorToast: true,
})
Future<BaseResponse> 接口名称(
  @Query("参数1") String 参数1,  // GET请求用@Query
  @Field("参数1") String 参数1,  // POST请求用@Field
);
```

#### 2.3 api_repository.dart 文件
- 在文件末尾添加静态方法
- 返回类型与app_api.dart保持一致
- 格式如下：
```dart
/**
 * 接口功能描述
 */
static Future<BaseResponse> 接口名称({
  required String 参数1,
  required String 参数2,
}) {
  return BaseAppApi.instance().接口名称(参数1, 参数2);
}
```

### 3. 注意事项
- **忽略headers**：不需要关注@Headers注解，只关注参数和请求方法
- **不处理.g.dart文件**：不需要执行build_runner，不关心代码生成文件
- **返回类型**：默认使用BaseResponse，不添加泛型（后续会自行添加具体model）
- **参数类型**：GET请求用@Query，POST请求用@Field
- **常量位置**：API常量必须添加在api.dart文件末尾

### 4. GET vs POST 区别
- **GET请求**：参数使用 `@Query` 注解，数据通过URL查询参数传递
- **POST请求**：参数使用 `@Field` 注解，数据通过请求体传递，通常需要 `@FormUrlEncoded` 注解

## 完成标准
1. 在api.dart末尾添加API常量
2. 在app_api.dart末尾添加接口定义
3. 在api_repository.dart末尾添加对应方法
4. 所有返回类型使用BaseResponse（无泛型）
5. 参数注解根据请求类型选择（GET用@Query，POST用@Field）