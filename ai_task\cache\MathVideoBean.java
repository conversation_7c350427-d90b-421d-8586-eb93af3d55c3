package com.dinoenglish.framework.bean.book;

import android.os.Parcel;
import android.os.Parcelable;

import com.dinoenglish.framework.base.Common;

import java.util.List;

/**
 * Created by linxi on 2022\12\27 0027.
 */
public class MathVideoBean implements Parcelable {

    public MathVideoBean(){

    }
    private List<ResultVoListBean> resultVoList;
    private MathKnowLedgeBean mathKnowLedgeViewQo;

    public List<ResultVoListBean> getResultVoList() {
        return resultVoList;
    }

    public void setResultVoList(List<ResultVoListBean> resultVoList) {
        this.resultVoList = resultVoList;
    }

    public MathKnowLedgeBean getMathKnowLedgeViewQo() {
        return mathKnowLedgeViewQo;
    }

    public void setMathKnowLedgeViewQo(MathKnowLedgeBean mathKnowLedgeViewQo) {
        this.mathKnowLedgeViewQo = mathKnowLedgeViewQo;
    }

    public static class ResultVoListBean implements Parcelable{
        public ResultVoListBean(){

        }
        private String id;
        private String name;
        private List<InfoVoList> infoVoList;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<InfoVoList> getInfoVoList() {
            return infoVoList;
        }

        public void setInfoVoList(List<InfoVoList> infoVoList) {
            this.infoVoList = infoVoList;
        }

        public static class InfoVoList implements Parcelable{
            public InfoVoList(){

            }
            private String id;
            private int viewRate;
            private String unitId;
            private String unitName;
            private String name;
            private String bookId;
            private String oneUnitId;
            private String difficulty;
            private int playCount;
            private int practiceCount;
            private String courseName;
            private String labelName;
            private int duration;
            private String isFree;
            private String courseId;
            private String courseImage;
            private String courseUrl;
            private int freeTimes;
            private List<GearList> gearList;
            private List<OptionList> optionList;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public int getViewRate() {
                return viewRate;
            }

            public void setViewRate(int viewRate) {
                this.viewRate = viewRate;
            }

            public String getUnitId() {
                return unitId;
            }

            public void setUnitId(String unitId) {
                this.unitId = unitId;
            }

            public String getBookId() {
                return bookId;
            }

            public void setBookId(String bookId) {
                this.bookId = bookId;
            }

            public String getOneUnitId() {
                return oneUnitId;
            }

            public void setOneUnitId(String oneUnitId) {
                this.oneUnitId = oneUnitId;
            }

            public String getUnitName() {
                return unitName;
            }

            public void setUnitName(String unitName) {
                this.unitName = unitName;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getDifficulty() {
                return difficulty;
            }

            public void setDifficulty(String difficulty) {
                this.difficulty = difficulty;
            }

            public int getPlayCount() {
                return playCount;
            }

            public void setPlayCount(int playCount) {
                this.playCount = playCount;
            }

            public int getPracticeCount() {
                return practiceCount;
            }

            public void setPracticeCount(int practiceCount) {
                this.practiceCount = practiceCount;
            }

            public String getCourseName() {
                return courseName;
            }

            public void setCourseName(String courseName) {
                this.courseName = courseName;
            }

            public String getLabelName() {
                return labelName;
            }

            public void setLabelName(String labelName) {
                this.labelName = labelName;
            }

            public int getDuration() {
                return duration;
            }

            public void setDuration(int duration) {
                this.duration = duration;
            }

            public String getIsFree() {
                return isFree;
            }

            public void setIsFree(String isFree) {
                this.isFree = isFree;
            }

            public String getCourseId() {
                return courseId;
            }

            public void setCourseId(String courseId) {
                this.courseId = courseId;
            }

            public String getCourseImage() {
                return courseImage;
            }

            public void setCourseImage(String courseImage) {
                this.courseImage = Common.getOssUrl(courseImage);
            }

            public String getCourseUrl() {
                return courseUrl;
            }

            public void setCourseUrl(String courseUrl) {
                this.courseUrl = Common.getOssUrl(courseUrl);
            }

            public int getFreeTimes() {
                return freeTimes <= 0 ? 10 : freeTimes;
            }

            public void setFreeTimes(int freeTimes) {
                this.freeTimes = freeTimes;
            }

            public List<GearList> getGearList() {
                return gearList;
            }

            public void setGearList(List<GearList> gearList) {
                this.gearList = gearList;
            }

            public List<OptionList> getOptionList() {
                return optionList;
            }

            public void setOptionList(List<OptionList> optionList) {
                this.optionList = optionList;
            }

            public static class GearList implements Parcelable{
                public GearList(){

                }
                private String id;
                private String knowledgeId;
                private String name;
                private String level;
                private int questionNum;
                private int sort;

                public String getId() {
                    return id;
                }

                public void setId(String id) {
                    this.id = id;
                }

                public String getKnowledgeId() {
                    return knowledgeId;
                }

                public void setKnowledgeId(String knowledgeId) {
                    this.knowledgeId = knowledgeId;
                }

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public String getLevel() {
                    return level;
                }

                public void setLevel(String level) {
                    this.level = level;
                }

                public int getQuestionNum() {
                    return questionNum;
                }

                public void setQuestionNum(int questionNum) {
                    this.questionNum = questionNum;
                }

                public int getSort() {
                    return sort;
                }

                public void setSort(int sort) {
                    this.sort = sort;
                }

                @Override
                public int describeContents() {
                    return 0;
                }

                @Override
                public void writeToParcel(Parcel dest, int flags) {
                    dest.writeString(this.id);
                    dest.writeString(this.knowledgeId);
                    dest.writeString(this.name);
                    dest.writeString(this.level);
                    dest.writeInt(this.questionNum);
                    dest.writeInt(this.sort);
                }

                public void readFromParcel(Parcel source) {
                    this.id = source.readString();
                    this.knowledgeId = source.readString();
                    this.name = source.readString();
                    this.level = source.readString();
                    this.questionNum = source.readInt();
                    this.sort = source.readInt();
                }

                protected GearList(Parcel in) {
                    this.id = in.readString();
                    this.knowledgeId = in.readString();
                    this.name = in.readString();
                    this.level = in.readString();
                    this.questionNum = in.readInt();
                    this.sort = in.readInt();
                }

                public static final Creator<GearList> CREATOR = new Creator<GearList>() {
                    @Override
                    public GearList createFromParcel(Parcel source) {
                        return new GearList(source);
                    }

                    @Override
                    public GearList[] newArray(int size) {
                        return new GearList[size];
                    }
                };
            }
            public static class OptionList implements Parcelable{
                public OptionList(){

                }
                private String id;
                private int times;
                private String option;
                private String answer;
                private boolean isSelect;

                public String getId() {
                    return id;
                }

                public void setId(String id) {
                    this.id = id;
                }

                public int getTimes() {
                    return times;
                }

                public void setTimes(int times) {
                    this.times = times;
                }

                public String getOption() {
                    return option;
                }

                public void setOption(String option) {
                    this.option = option;
                }

                public String getAnswer() {
                    return answer;
                }

                public void setAnswer(String answer) {
                    this.answer = answer;
                }

                public boolean isSelect() {
                    return isSelect;
                }

                public void setSelect(boolean select) {
                    isSelect = select;
                }

                @Override
                public int describeContents() {
                    return 0;
                }

                @Override
                public void writeToParcel(Parcel dest, int flags) {
                    dest.writeString(this.id);
                    dest.writeInt(this.times);
                    dest.writeString(this.option);
                    dest.writeString(this.answer);
                    dest.writeByte(this.isSelect ? (byte) 1 : (byte) 0);
                }

                public void readFromParcel(Parcel source) {
                    this.id = source.readString();
                    this.times = source.readInt();
                    this.option = source.readString();
                    this.answer = source.readString();
                    this.isSelect = source.readByte() != 0;
                }

                protected OptionList(Parcel in) {
                    this.id = in.readString();
                    this.times = in.readInt();
                    this.option = in.readString();
                    this.answer = in.readString();
                    this.isSelect = in.readByte() != 0;
                }

                public static final Creator<OptionList> CREATOR = new Creator<OptionList>() {
                    @Override
                    public OptionList createFromParcel(Parcel source) {
                        return new OptionList(source);
                    }

                    @Override
                    public OptionList[] newArray(int size) {
                        return new OptionList[size];
                    }
                };
            }

            @Override
            public int describeContents() {
                return 0;
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeString(this.id);
                dest.writeInt(this.viewRate);
                dest.writeString(this.unitId);
                dest.writeString(this.unitName);
                dest.writeString(this.name);
                dest.writeString(this.bookId);
                dest.writeString(this.oneUnitId);
                dest.writeString(this.difficulty);
                dest.writeInt(this.playCount);
                dest.writeInt(this.practiceCount);
                dest.writeString(this.courseName);
                dest.writeString(this.labelName);
                dest.writeInt(this.duration);
                dest.writeString(this.isFree);
                dest.writeString(this.courseId);
                dest.writeString(this.courseImage);
                dest.writeString(this.courseUrl);
                dest.writeInt(this.freeTimes);
                dest.writeTypedList(this.gearList);
                dest.writeTypedList(this.optionList);
            }

            public void readFromParcel(Parcel source) {
                this.id = source.readString();
                this.viewRate = source.readInt();
                this.unitId = source.readString();
                this.unitName = source.readString();
                this.name = source.readString();
                this.bookId = source.readString();
                this.oneUnitId = source.readString();
                this.difficulty = source.readString();
                this.playCount = source.readInt();
                this.practiceCount = source.readInt();
                this.courseName = source.readString();
                this.labelName = source.readString();
                this.duration = source.readInt();
                this.isFree = source.readString();
                this.courseId = source.readString();
                this.courseImage = source.readString();
                this.courseUrl = source.readString();
                this.freeTimes = source.readInt();
                this.gearList = source.createTypedArrayList(GearList.CREATOR);
                this.optionList = source.createTypedArrayList(OptionList.CREATOR);
            }

            protected InfoVoList(Parcel in) {
                this.id = in.readString();
                this.viewRate = in.readInt();
                this.unitId = in.readString();
                this.unitName = in.readString();
                this.name = in.readString();
                this.bookId = in.readString();
                this.oneUnitId = in.readString();
                this.difficulty = in.readString();
                this.playCount = in.readInt();
                this.practiceCount = in.readInt();
                this.courseName = in.readString();
                this.labelName = in.readString();
                this.duration = in.readInt();
                this.isFree = in.readString();
                this.courseId = in.readString();
                this.courseImage = in.readString();
                this.courseUrl = in.readString();
                this.freeTimes = in.readInt();
                this.gearList = in.createTypedArrayList(GearList.CREATOR);
                this.optionList = in.createTypedArrayList(OptionList.CREATOR);
            }

            public static final Creator<InfoVoList> CREATOR = new Creator<InfoVoList>() {
                @Override
                public InfoVoList createFromParcel(Parcel source) {
                    return new InfoVoList(source);
                }

                @Override
                public InfoVoList[] newArray(int size) {
                    return new InfoVoList[size];
                }
            };
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.id);
            dest.writeString(this.name);
            dest.writeTypedList(this.infoVoList);
        }

        public void readFromParcel(Parcel source) {
            this.id = source.readString();
            this.name = source.readString();
            this.infoVoList = source.createTypedArrayList(InfoVoList.CREATOR);
        }

        protected ResultVoListBean(Parcel in) {
            this.id = in.readString();
            this.name = in.readString();
            this.infoVoList = in.createTypedArrayList(InfoVoList.CREATOR);
        }

        public static final Creator<ResultVoListBean> CREATOR = new Creator<ResultVoListBean>() {
            @Override
            public ResultVoListBean createFromParcel(Parcel source) {
                return new ResultVoListBean(source);
            }

            @Override
            public ResultVoListBean[] newArray(int size) {
                return new ResultVoListBean[size];
            }
        };
    }

    public static class MathKnowLedgeBean implements Parcelable{
        public MathKnowLedgeBean(){

        }
        private String knowledgeId;
        private String name;
        private String viewRate;

        public String getKnowledgeId() {
            return knowledgeId;
        }

        public void setKnowledgeId(String knowledgeId) {
            this.knowledgeId = knowledgeId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getViewRate() {
            return viewRate;
        }

        public void setViewRate(String viewRate) {
            this.viewRate = viewRate;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.knowledgeId);
            dest.writeString(this.name);
            dest.writeString(this.viewRate);
        }

        public void readFromParcel(Parcel source) {
            this.knowledgeId = source.readString();
            this.name = source.readString();
            this.viewRate = source.readString();
        }

        protected MathKnowLedgeBean(Parcel in) {
            this.knowledgeId = in.readString();
            this.name = in.readString();
            this.viewRate = in.readString();
        }

        public static final Creator<MathKnowLedgeBean> CREATOR = new Creator<MathKnowLedgeBean>() {
            @Override
            public MathKnowLedgeBean createFromParcel(Parcel source) {
                return new MathKnowLedgeBean(source);
            }

            @Override
            public MathKnowLedgeBean[] newArray(int size) {
                return new MathKnowLedgeBean[size];
            }
        };
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(this.resultVoList);
        dest.writeParcelable(this.mathKnowLedgeViewQo, flags);
    }

    public void readFromParcel(Parcel source) {
        this.resultVoList = source.createTypedArrayList(ResultVoListBean.CREATOR);
        this.mathKnowLedgeViewQo = source.readParcelable(MathKnowLedgeBean.class.getClassLoader());
    }

    protected MathVideoBean(Parcel in) {
        this.resultVoList = in.createTypedArrayList(ResultVoListBean.CREATOR);
        this.mathKnowLedgeViewQo = in.readParcelable(MathKnowLedgeBean.class.getClassLoader());
    }

    public static final Creator<MathVideoBean> CREATOR = new Creator<MathVideoBean>() {
        @Override
        public MathVideoBean createFromParcel(Parcel source) {
            return new MathVideoBean(source);
        }

        @Override
        public MathVideoBean[] newArray(int size) {
            return new MathVideoBean[size];
        }
    };
}
