

1. 帮我在lib_base模块里面添加一个接口， 接口定义如下：
    /**
     * 根据一级单元id查询视频资源
     */
    @Headers({"url_name:newhost"})
    @GET("/api/math/v50/mathknowledgecontrollerapi/selectvideoresourcelist")
    Call<JSONObject> selectvideoresourcelist(@Query("bookId") String bookId, @Query("oneUnitId") String oneUnitId, @Query("userId") String userId);

2. 不需要关注headers， 只需要关注参数和 请求方法
3. response的model暂时使用默认的， 我自己修改
4. lib_base模块的路径是 package\lib_base
5. 你需要的工作在package\lib_base\lib\api路径下， 可以参考里面已有的接口