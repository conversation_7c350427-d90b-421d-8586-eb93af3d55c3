{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": false, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 218131300, "CreateModuleInfo": 4790200, "ConfigureCmake": 552800, "MergeProfile": 196244500, "CreateBuildProfile": 3732000, "PreCheckSyscap": 660100, "ProcessIntegratedHsp": 3020900, "BuildNativeWithCmake": 529800, "MakePackInfo": 8783800, "SyscapTransform": 22174200, "ProcessProfile": 132397900, "ProcessRouterMap": 31541000, "BuildNativeWithNinja": 1598800, "ProcessResource": 20076000, "GenerateLoaderJson": 164616300, "ProcessLibs": 2225773600, "CompileResource": 4240137300, "BuildJS": 10771100, "CompileArkTS": 31035109600, "GeneratePkgModuleJson": 5594700, "PackageHap": 3196514800, "SignHap": 9456951300, "CollectDebugSymbol": 14890600, "assembleHap": 928000}}, "BUILD_ID": "202507220918161170", "TOTAL_TIME": 54077779300}}