{"configPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", "packageName": "com.yyb.hm", "output": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default", "moduleNames": "entry", "ResourceTable": ["D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h"], "applicationResource": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\AppScope\\resources", "moduleResources": ["D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\src\\main\\resources"], "dependencies": ["D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\audioplayers_ohos@r7thutzhmi0kkhuxg1wdlynkw+67mlzgocljgakroqy=\\oh_modules\\audioplayers_ohos\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_inappwebview_ohos@bkqihe+jsvttaicgyvsr2zjdgpgzeb3tle+qb72e2te=\\oh_modules\\flutter_inappwebview_ohos\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\video_player_ohos@u81qy+hfnzwakdag+e8gmtez3if+fbgqagi+71ni2ww=\\oh_modules\\video_player_ohos\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\permission_handler_ohos@5jishfwhiykxomjhupjv1mmqon6zlaf5lkiyjoetctw=\\oh_modules\\permission_handler_ohos\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\package_info_plus@m5lvchlo5awwx1l7q3aa+4smitkmkcrpecaykaz5vim=\\oh_modules\\package_info_plus\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_sound@2kc8wc6nnsoutti4cfvprfhhwckoirlqs+mmxokx6gk=\\oh_modules\\flutter_sound\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\connectivity_plus@kbj8slkcifcym+xcmelqiot0d18sem3f6njemcsp9oo=\\oh_modules\\connectivity_plus\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\mobile_scanner@1cr7auyn3bl6bzonqialdrvkluznohra6ku+u6csc5o=\\oh_modules\\mobile_scanner\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=\\oh_modules\\@pdp\\book\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=\\oh_modules\\singsound\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\device_info_plus@8lo4nbtv1vanjsz+oviy4bfdmt0k6ylf58gddpmwplm=\\oh_modules\\device_info_plus\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\audio_session@w+c+yyiqinmp7inrpmiue+wlj+nivgw73pewzizpxte=\\oh_modules\\audio_session\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+crypto-js@2.0.4\\oh_modules\\@ohos\\crypto-js\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+swiper@1.0.0\\oh_modules\\@pdp\\swiper\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+evaluation@1.1.3\\oh_modules\\@pdp\\evaluation\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\bigdata@mptnw0wkm5spww48ifee6omoheeuqmqsa3pwhpz9qpk=\\oh_modules\\bigdata\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+mp4parser@2.0.3-rc.1\\oh_modules\\@ohos\\mp4parser\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+common@1.1.3\\oh_modules\\@umeng\\common\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+analytics@1.2.4\\oh_modules\\@umeng\\analytics\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default\\resource_str"], "iconCheck": true, "compression": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json", "ids": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default\\ids_map", "definedIds": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "definedSysIds": "D:\\softs\\DevEco\\DevEco Studio\\sdk\\default\\hms\\toolchains\\id_defined.json"}