<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" dir="ltr">
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

<!--    <link rel="stylesheet" href="myeq/mathquill091/mathquill.css" >-->
<!--    <script type="text/javascript" src="jquery183min.js" charset="UTF-8"></script>-->
<!--    <script type="text/javascript" src="myeq/mathquill091/mathquill.js" charset="UTF-8"></script>-->
<!--    <script type="text/javascript" src="./myeq/util.js"  charset="UTF-8"></script>-->
    <style type="text/css">
     .ub
     {
         display: -webkit-box !important;
         display: box !important;
         position:relative;
     }
    .ub-ac
    {
        -webkit-box-align:center;
        box-align:center;
    }
    .ub-ae
    {
        -webkit-box-align:end;
        box-align:end;
    }

    .ub-pc
    {
        -webkit-box-pack:center;
        box-pack:center;
    }
    .ub-pe
    {
        -webkit-box-pack:end;
        box-pack:end;
    }
     .ub-rev
     {
         -webkit-box-direction:reverse;
         box-direction:reverse;
     }

     .ub-fh
     {
         width:100%;
     }

     .ub-fv
     {
         height:100%;
     }

     .ub-ver
     {
         -webkit-box-orient:vertical;
         box-orient:vertical;
     }

     .ub-f1
     {
         position:relative;
         -webkit-box-flex: 1;
         box-flex: 1;
     }
     .uinn
     {
        padding:0.5em;
     }
     .umar-t
     {
        margin-top:1em;
     }
     .color-red
     {
        color:red;
     }
     .ulev1 {
        font-size:1.1em;
     }
     .ulev2 {
        font-size:1.2em;
     }
     .ulev-1 {
        font-size:0.9em;
     }
     .ulev-2 {
        font-size:0.8em;
     }
     .sc-text {
        color:#BABABA;
     }
  </style>

    <script type="text/javascript">
        function showMain(dataStr,url) {
            var str = decodeURIComponent(dataStr);
            str = str.replace(/%20/g," ");
            var hl = parseHTMLToData(str,true);
            $("#content").html(hl);

            $.each($("#content").find("img"),function(){
                 $(this)[0].onload = function (){
                     var width = $(this)[0].style.width;
                     if (!width || width.indexOf("%") == -1) {
                        var w = $(this).width();
                        var h = $(this).height();
                        var windowWidth = window.screen.availWidth - 50;
                        if(w > windowWidth) {
                            $(this).css("width", "100%");
                            $(this).css("height", "auto");
                            //$(this).css('height', h * (windowWidth/w) + 'px');
                        }else {
                            $(this).css('width', w * 0.0625 + 'em');
                            $(this).css('height', h * 0.0625 + 'em');
                        }
                     }
                 };
                 var imageSrc = $(this).attr("src").toLowerCase();
                 var isHttp = imageSrc.indexOf("http://") == 0 || imageSrc.indexOf("https://") == 0;
                 if(!isHttp){
                    $(this).attr("src",url + imageSrc);
                 }
                  $(this).removeAttr("align");
            });

            return true;
        }

        function showMainHtml(dataStr, url) {
            var str = decodeURIComponent(dataStr);
            str = str.replace(/%20/g," ");
            $("#content").html(str);
            $.each($("#content").find("img"),function(){
                 $(this)[0].onload = function (){
                     var width = $(this)[0].style.width;
                     if (!width || width.indexOf("%") == -1) {
                        var w = $(this).width();
                        var h = $(this).height();
                        var windowWidth = window.screen.availWidth - 50;
                        if(w > windowWidth) {
                            $(this).css("width", "100%");
                            $(this).css("height", "auto");
                            //$(this).css('height', h * (windowWidth/w) + 'px');
                        }else {
                            $(this).css('width', w * 0.0625 + 'em');
                            $(this).css('height', h * 0.0625 + 'em');
                        }
                     }
                 };
                 var imageSrc = $(this).attr("src").toLowerCase();
                 var isHttp = imageSrc.indexOf("http://") == 0 || imageSrc.indexOf("https://") == 0;
                 if(!isHttp){
                    $(this).attr("src",url + imageSrc);
                 }
                  $(this).removeAttr("align");
            });
            return true;
        }
  </script>
    <script type="text/javascript">
(function(e,t){function _(e){var t=M[e]={};return v.each(e.split(y),function(e,n){t[n]=!0}),t}function H(e,n,r){if(r===t&&e.nodeType===1){var i="data-"+n.replace(P,"-$1").toLowerCase();r=e.getAttribute(i);if(typeof r=="string"){try{r=r==="true"?!0:r==="false"?!1:r==="null"?null:+r+""===r?+r:D.test(r)?v.parseJSON(r):r}catch(s){}v.data(e,n,r)}else r=t}return r}function B(e){var t;for(t in e){if(t==="data"&&v.isEmptyObject(e[t]))continue;if(t!=="toJSON")return!1}return!0}function et(){return!1}function tt(){return!0}function ut(e){return!e||!e.parentNode||e.parentNode.nodeType===11}function at(e,t){do e=e[t];while(e&&e.nodeType!==1);return e}function ft(e,t,n){t=t||0;if(v.isFunction(t))return v.grep(e,function(e,r){var i=!!t.call(e,r,e);return i===n});if(t.nodeType)return v.grep(e,function(e,r){return e===t===n});if(typeof t=="string"){var r=v.grep(e,function(e){return e.nodeType===1});if(it.test(t))return v.filter(t,r,!n);t=v.filter(t,r)}return v.grep(e,function(e,r){return v.inArray(e,t)>=0===n})}function lt(e){var t=ct.split("|"),n=e.createDocumentFragment();if(n.createElement)while(t.length)n.createElement(t.pop());return n}function Lt(e,t){return e.getElementsByTagName(t)[0]||e.appendChild(e.ownerDocument.createElement(t))}function At(e,t){if(t.nodeType!==1||!v.hasData(e))return;var n,r,i,s=v._data(e),o=v._data(t,s),u=s.events;if(u){delete o.handle,o.events={};for(n in u)for(r=0,i=u[n].length;r<i;r++)v.event.add(t,n,u[n][r])}o.data&&(o.data=v.extend({},o.data))}function Ot(e,t){var n;if(t.nodeType!==1)return;t.clearAttributes&&t.clearAttributes(),t.mergeAttributes&&t.mergeAttributes(e),n=t.nodeName.toLowerCase(),n==="object"?(t.parentNode&&(t.outerHTML=e.outerHTML),v.support.html5Clone&&e.innerHTML&&!v.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):n==="input"&&Et.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):n==="option"?t.selected=e.defaultSelected:n==="input"||n==="textarea"?t.defaultValue=e.defaultValue:n==="script"&&t.text!==e.text&&(t.text=e.text),t.removeAttribute(v.expando)}function Mt(e){return typeof e.getElementsByTagName!="undefined"?e.getElementsByTagName("*"):typeof e.querySelectorAll!="undefined"?e.querySelectorAll("*"):[]}function _t(e){Et.test(e.type)&&(e.defaultChecked=e.checked)}function Qt(e,t){if(t in e)return t;var n=t.charAt(0).toUpperCase()+t.slice(1),r=t,i=Jt.length;while(i--){t=Jt[i]+n;if(t in e)return t}return r}function Gt(e,t){return e=t||e,v.css(e,"display")==="none"||!v.contains(e.ownerDocument,e)}function Yt(e,t){var n,r,i=[],s=0,o=e.length;for(;s<o;s++){n=e[s];if(!n.style)continue;i[s]=v._data(n,"olddisplay"),t?(!i[s]&&n.style.display==="none"&&(n.style.display=""),n.style.display===""&&Gt(n)&&(i[s]=v._data(n,"olddisplay",nn(n.nodeName)))):(r=Dt(n,"display"),!i[s]&&r!=="none"&&v._data(n,"olddisplay",r))}for(s=0;s<o;s++){n=e[s];if(!n.style)continue;if(!t||n.style.display==="none"||n.style.display==="")n.style.display=t?i[s]||"":"none"}return e}function Zt(e,t,n){var r=Rt.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function en(e,t,n,r){var i=n===(r?"border":"content")?4:t==="width"?1:0,s=0;for(;i<4;i+=2)n==="margin"&&(s+=v.css(e,n+$t[i],!0)),r?(n==="content"&&(s-=parseFloat(Dt(e,"padding"+$t[i]))||0),n!=="margin"&&(s-=parseFloat(Dt(e,"border"+$t[i]+"Width"))||0)):(s+=parseFloat(Dt(e,"padding"+$t[i]))||0,n!=="padding"&&(s+=parseFloat(Dt(e,"border"+$t[i]+"Width"))||0));return s}function tn(e,t,n){var r=t==="width"?e.offsetWidth:e.offsetHeight,i=!0,s=v.support.boxSizing&&v.css(e,"boxSizing")==="border-box";if(r<=0||r==null){r=Dt(e,t);if(r<0||r==null)r=e.style[t];if(Ut.test(r))return r;i=s&&(v.support.boxSizingReliable||r===e.style[t]),r=parseFloat(r)||0}return r+en(e,t,n||(s?"border":"content"),i)+"px"}function nn(e){if(Wt[e])return Wt[e];var t=v("<"+e+">").appendTo(i.body),n=t.css("display");t.remove();if(n==="none"||n===""){Pt=i.body.appendChild(Pt||v.extend(i.createElement("iframe"),{frameBorder:0,width:0,height:0}));if(!Ht||!Pt.createElement)Ht=(Pt.contentWindow||Pt.contentDocument).document,Ht.write("<!doctype html><html><body>"),Ht.close();t=Ht.body.appendChild(Ht.createElement(e)),n=Dt(t,"display"),i.body.removeChild(Pt)}return Wt[e]=n,n}function fn(e,t,n,r){var i;if(v.isArray(t))v.each(t,function(t,i){n||sn.test(e)?r(e,i):fn(e+"["+(typeof i=="object"?t:"")+"]",i,n,r)});else if(!n&&v.type(t)==="object")for(i in t)fn(e+"["+i+"]",t[i],n,r);else r(e,t)}function Cn(e){return function(t,n){typeof t!="string"&&(n=t,t="*");var r,i,s,o=t.toLowerCase().split(y),u=0,a=o.length;if(v.isFunction(n))for(;u<a;u++)r=o[u],s=/^\+/.test(r),s&&(r=r.substr(1)||"*"),i=e[r]=e[r]||[],i[s?"unshift":"push"](n)}}function kn(e,n,r,i,s,o){s=s||n.dataTypes[0],o=o||{},o[s]=!0;var u,a=e[s],f=0,l=a?a.length:0,c=e===Sn;for(;f<l&&(c||!u);f++)u=a[f](n,r,i),typeof u=="string"&&(!c||o[u]?u=t:(n.dataTypes.unshift(u),u=kn(e,n,r,i,u,o)));return(c||!u)&&!o["*"]&&(u=kn(e,n,r,i,"*",o)),u}function Ln(e,n){var r,i,s=v.ajaxSettings.flatOptions||{};for(r in n)n[r]!==t&&((s[r]?e:i||(i={}))[r]=n[r]);i&&v.extend(!0,e,i)}function An(e,n,r){var i,s,o,u,a=e.contents,f=e.dataTypes,l=e.responseFields;for(s in l)s in r&&(n[l[s]]=r[s]);while(f[0]==="*")f.shift(),i===t&&(i=e.mimeType||n.getResponseHeader("content-type"));if(i)for(s in a)if(a[s]&&a[s].test(i)){f.unshift(s);break}if(f[0]in r)o=f[0];else{for(s in r){if(!f[0]||e.converters[s+" "+f[0]]){o=s;break}u||(u=s)}o=o||u}if(o)return o!==f[0]&&f.unshift(o),r[o]}function On(e,t){var n,r,i,s,o=e.dataTypes.slice(),u=o[0],a={},f=0;e.dataFilter&&(t=e.dataFilter(t,e.dataType));if(o[1])for(n in e.converters)a[n.toLowerCase()]=e.converters[n];for(;i=o[++f];)if(i!=="*"){if(u!=="*"&&u!==i){n=a[u+" "+i]||a["* "+i];if(!n)for(r in a){s=r.split(" ");if(s[1]===i){n=a[u+" "+s[0]]||a["* "+s[0]];if(n){n===!0?n=a[r]:a[r]!==!0&&(i=s[0],o.splice(f--,0,i));break}}}if(n!==!0)if(n&&e["throws"])t=n(t);else try{t=n(t)}catch(l){return{state:"parsererror",error:n?l:"No conversion from "+u+" to "+i}}}u=i}return{state:"success",data:t}}function Fn(){try{return new e.XMLHttpRequest}catch(t){}}function In(){try{return new e.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}function $n(){return setTimeout(function(){qn=t},0),qn=v.now()}function Jn(e,t){v.each(t,function(t,n){var r=(Vn[t]||[]).concat(Vn["*"]),i=0,s=r.length;for(;i<s;i++)if(r[i].call(e,t,n))return})}function Kn(e,t,n){var r,i=0,s=0,o=Xn.length,u=v.Deferred().always(function(){delete a.elem}),a=function(){var t=qn||$n(),n=Math.max(0,f.startTime+f.duration-t),r=n/f.duration||0,i=1-r,s=0,o=f.tweens.length;for(;s<o;s++)f.tweens[s].run(i);return u.notifyWith(e,[f,i,n]),i<1&&o?n:(u.resolveWith(e,[f]),!1)},f=u.promise({elem:e,props:v.extend({},t),opts:v.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:qn||$n(),duration:n.duration,tweens:[],createTween:function(t,n,r){var i=v.Tween(e,f.opts,t,n,f.opts.specialEasing[t]||f.opts.easing);return f.tweens.push(i),i},stop:function(t){var n=0,r=t?f.tweens.length:0;for(;n<r;n++)f.tweens[n].run(1);return t?u.resolveWith(e,[f,t]):u.rejectWith(e,[f,t]),this}}),l=f.props;Qn(l,f.opts.specialEasing);for(;i<o;i++){r=Xn[i].call(f,e,l,f.opts);if(r)return r}return Jn(f,l),v.isFunction(f.opts.start)&&f.opts.start.call(e,f),v.fx.timer(v.extend(a,{anim:f,queue:f.opts.queue,elem:e})),f.progress(f.opts.progress).done(f.opts.done,f.opts.complete).fail(f.opts.fail).always(f.opts.always)}function Qn(e,t){var n,r,i,s,o;for(n in e){r=v.camelCase(n),i=t[r],s=e[n],v.isArray(s)&&(i=s[1],s=e[n]=s[0]),n!==r&&(e[r]=s,delete e[n]),o=v.cssHooks[r];if(o&&"expand"in o){s=o.expand(s),delete e[r];for(n in s)n in e||(e[n]=s[n],t[n]=i)}else t[r]=i}}function Gn(e,t,n){var r,i,s,o,u,a,f,l,c,h=this,p=e.style,d={},m=[],g=e.nodeType&&Gt(e);n.queue||(l=v._queueHooks(e,"fx"),l.unqueued==null&&(l.unqueued=0,c=l.empty.fire,l.empty.fire=function(){l.unqueued||c()}),l.unqueued++,h.always(function(){h.always(function(){l.unqueued--,v.queue(e,"fx").length||l.empty.fire()})})),e.nodeType===1&&("height"in t||"width"in t)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],v.css(e,"display")==="inline"&&v.css(e,"float")==="none"&&(!v.support.inlineBlockNeedsLayout||nn(e.nodeName)==="inline"?p.display="inline-block":p.zoom=1)),n.overflow&&(p.overflow="hidden",v.support.shrinkWrapBlocks||h.done(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}));for(r in t){s=t[r];if(Un.exec(s)){delete t[r],a=a||s==="toggle";if(s===(g?"hide":"show"))continue;m.push(r)}}o=m.length;if(o){u=v._data(e,"fxshow")||v._data(e,"fxshow",{}),"hidden"in u&&(g=u.hidden),a&&(u.hidden=!g),g?v(e).show():h.done(function(){v(e).hide()}),h.done(function(){var t;v.removeData(e,"fxshow",!0);for(t in d)v.style(e,t,d[t])});for(r=0;r<o;r++)i=m[r],f=h.createTween(i,g?u[i]:0),d[i]=u[i]||v.style(e,i),i in u||(u[i]=f.start,g&&(f.end=f.start,f.start=i==="width"||i==="height"?1:0))}}function Yn(e,t,n,r,i){return new Yn.prototype.init(e,t,n,r,i)}function Zn(e,t){var n,r={height:e},i=0;t=t?1:0;for(;i<4;i+=2-t)n=$t[i],r["margin"+n]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function tr(e){return v.isWindow(e)?e:e.nodeType===9?e.defaultView||e.parentWindow:!1}var n,r,i=e.document,s=e.location,o=e.navigator,u=e.jQuery,a=e.$,f=Array.prototype.push,l=Array.prototype.slice,c=Array.prototype.indexOf,h=Object.prototype.toString,p=Object.prototype.hasOwnProperty,d=String.prototype.trim,v=function(e,t){return new v.fn.init(e,t,n)},m=/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,g=/\S/,y=/\s+/,b=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,w=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,E=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,S=/^[\],:{}\s]*$/,x=/(?:^|:|,)(?:\s*\[)+/g,T=/\\(?:["\\\/bfnrt]|u[\da-fA-F]{4})/g,N=/"[^"\\\r\n]*"|true|false|null|-?(?:\d\d*\.|)\d+(?:[eE][\-+]?\d+|)/g,C=/^-ms-/,k=/-([\da-z])/gi,L=function(e,t){return(t+"").toUpperCase()},A=function(){i.addEventListener?(i.removeEventListener("DOMContentLoaded",A,!1),v.ready()):i.readyState==="complete"&&(i.detachEvent("onreadystatechange",A),v.ready())},O={};v.fn=v.prototype={constructor:v,init:function(e,n,r){var s,o,u,a;if(!e)return this;if(e.nodeType)return this.context=this[0]=e,this.length=1,this;if(typeof e=="string"){e.charAt(0)==="<"&&e.charAt(e.length-1)===">"&&e.length>=3?s=[null,e,null]:s=w.exec(e);if(s&&(s[1]||!n)){if(s[1])return n=n instanceof v?n[0]:n,a=n&&n.nodeType?n.ownerDocument||n:i,e=v.parseHTML(s[1],a,!0),E.test(s[1])&&v.isPlainObject(n)&&this.attr.call(e,n,!0),v.merge(this,e);o=i.getElementById(s[2]);if(o&&o.parentNode){if(o.id!==s[2])return r.find(e);this.length=1,this[0]=o}return this.context=i,this.selector=e,this}return!n||n.jquery?(n||r).find(e):this.constructor(n).find(e)}return v.isFunction(e)?r.ready(e):(e.selector!==t&&(this.selector=e.selector,this.context=e.context),v.makeArray(e,this))},selector:"",jquery:"1.8.3",length:0,size:function(){return this.length},toArray:function(){return l.call(this)},get:function(e){return e==null?this.toArray():e<0?this[this.length+e]:this[e]},pushStack:function(e,t,n){var r=v.merge(this.constructor(),e);return r.prevObject=this,r.context=this.context,t==="find"?r.selector=this.selector+(this.selector?" ":"")+n:t&&(r.selector=this.selector+"."+t+"("+n+")"),r},each:function(e,t){return v.each(this,e,t)},ready:function(e){return v.ready.promise().done(e),this},eq:function(e){return e=+e,e===-1?this.slice(e):this.slice(e,e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},slice:function(){return this.pushStack(l.apply(this,arguments),"slice",l.call(arguments).join(","))},map:function(e){return this.pushStack(v.map(this,function(t,n){return e.call(t,n,t)}))},end:function(){return this.prevObject||this.constructor(null)},push:f,sort:[].sort,splice:[].splice},v.fn.init.prototype=v.fn,v.extend=v.fn.extend=function(){var e,n,r,i,s,o,u=arguments[0]||{},a=1,f=arguments.length,l=!1;typeof u=="boolean"&&(l=u,u=arguments[1]||{},a=2),typeof u!="object"&&!v.isFunction(u)&&(u={}),f===a&&(u=this,--a);for(;a<f;a++)if((e=arguments[a])!=null)for(n in e){r=u[n],i=e[n];if(u===i)continue;l&&i&&(v.isPlainObject(i)||(s=v.isArray(i)))?(s?(s=!1,o=r&&v.isArray(r)?r:[]):o=r&&v.isPlainObject(r)?r:{},u[n]=v.extend(l,o,i)):i!==t&&(u[n]=i)}return u},v.extend({noConflict:function(t){return e.$===v&&(e.$=a),t&&e.jQuery===v&&(e.jQuery=u),v},isReady:!1,readyWait:1,holdReady:function(e){e?v.readyWait++:v.ready(!0)},ready:function(e){if(e===!0?--v.readyWait:v.isReady)return;if(!i.body)return setTimeout(v.ready,1);v.isReady=!0;if(e!==!0&&--v.readyWait>0)return;r.resolveWith(i,[v]),v.fn.trigger&&v(i).trigger("ready").off("ready")},isFunction:function(e){return v.type(e)==="function"},isArray:Array.isArray||function(e){return v.type(e)==="array"},isWindow:function(e){return e!=null&&e==e.window},isNumeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},type:function(e){return e==null?String(e):O[h.call(e)]||"object"},isPlainObject:function(e){if(!e||v.type(e)!=="object"||e.nodeType||v.isWindow(e))return!1;try{if(e.constructor&&!p.call(e,"constructor")&&!p.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(n){return!1}var r;for(r in e);return r===t||p.call(e,r)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},error:function(e){throw new Error(e)},parseHTML:function(e,t,n){var r;return!e||typeof e!="string"?null:(typeof t=="boolean"&&(n=t,t=0),t=t||i,(r=E.exec(e))?[t.createElement(r[1])]:(r=v.buildFragment([e],t,n?null:[]),v.merge([],(r.cacheable?v.clone(r.fragment):r.fragment).childNodes)))},parseJSON:function(t){if(!t||typeof t!="string")return null;t=v.trim(t);if(e.JSON&&e.JSON.parse)return e.JSON.parse(t);if(S.test(t.replace(T,"@").replace(N,"]").replace(x,"")))return(new Function("return "+t))();v.error("Invalid JSON: "+t)},parseXML:function(n){var r,i;if(!n||typeof n!="string")return null;try{e.DOMParser?(i=new DOMParser,r=i.parseFromString(n,"text/xml")):(r=new ActiveXObject("Microsoft.XMLDOM"),r.async="false",r.loadXML(n))}catch(s){r=t}return(!r||!r.documentElement||r.getElementsByTagName("parsererror").length)&&v.error("Invalid XML: "+n),r},noop:function(){},globalEval:function(t){t&&g.test(t)&&(e.execScript||function(t){e.eval.call(e,t)})(t)},camelCase:function(e){return e.replace(C,"ms-").replace(k,L)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,n,r){var i,s=0,o=e.length,u=o===t||v.isFunction(e);if(r){if(u){for(i in e)if(n.apply(e[i],r)===!1)break}else for(;s<o;)if(n.apply(e[s++],r)===!1)break}else if(u){for(i in e)if(n.call(e[i],i,e[i])===!1)break}else for(;s<o;)if(n.call(e[s],s,e[s++])===!1)break;return e},trim:d&&!d.call("\ufeff\u00a0")?function(e){return e==null?"":d.call(e)}:function(e){return e==null?"":(e+"").replace(b,"")},makeArray:function(e,t){var n,r=t||[];return e!=null&&(n=v.type(e),e.length==null||n==="string"||n==="function"||n==="regexp"||v.isWindow(e)?f.call(r,e):v.merge(r,e)),r},inArray:function(e,t,n){var r;if(t){if(c)return c.call(t,e,n);r=t.length,n=n?n<0?Math.max(0,r+n):n:0;for(;n<r;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,n){var r=n.length,i=e.length,s=0;if(typeof r=="number")for(;s<r;s++)e[i++]=n[s];else while(n[s]!==t)e[i++]=n[s++];return e.length=i,e},grep:function(e,t,n){var r,i=[],s=0,o=e.length;n=!!n;for(;s<o;s++)r=!!t(e[s],s),n!==r&&i.push(e[s]);return i},map:function(e,n,r){var i,s,o=[],u=0,a=e.length,f=e instanceof v||a!==t&&typeof a=="number"&&(a>0&&e[0]&&e[a-1]||a===0||v.isArray(e));if(f)for(;u<a;u++)i=n(e[u],u,r),i!=null&&(o[o.length]=i);else for(s in e)i=n(e[s],s,r),i!=null&&(o[o.length]=i);return o.concat.apply([],o)},guid:1,proxy:function(e,n){var r,i,s;return typeof n=="string"&&(r=e[n],n=e,e=r),v.isFunction(e)?(i=l.call(arguments,2),s=function(){return e.apply(n,i.concat(l.call(arguments)))},s.guid=e.guid=e.guid||v.guid++,s):t},access:function(e,n,r,i,s,o,u){var a,f=r==null,l=0,c=e.length;if(r&&typeof r=="object"){for(l in r)v.access(e,n,l,r[l],1,o,i);s=1}else if(i!==t){a=u===t&&v.isFunction(i),f&&(a?(a=n,n=function(e,t,n){return a.call(v(e),n)}):(n.call(e,i),n=null));if(n)for(;l<c;l++)n(e[l],r,a?i.call(e[l],l,n(e[l],r)):i,u);s=1}return s?e:f?n.call(e):c?n(e[0],r):o},now:function(){return(new Date).getTime()}}),v.ready.promise=function(t){if(!r){r=v.Deferred();if(i.readyState==="complete")setTimeout(v.ready,1);else if(i.addEventListener)i.addEventListener("DOMContentLoaded",A,!1),e.addEventListener("load",v.ready,!1);else{i.attachEvent("onreadystatechange",A),e.attachEvent("onload",v.ready);var n=!1;try{n=e.frameElement==null&&i.documentElement}catch(s){}n&&n.doScroll&&function o(){if(!v.isReady){try{n.doScroll("left")}catch(e){return setTimeout(o,50)}v.ready()}}()}}return r.promise(t)},v.each("Boolean Number String Function Array Date RegExp Object".split(" "),function(e,t){O["[object "+t+"]"]=t.toLowerCase()}),n=v(i);var M={};v.Callbacks=function(e){e=typeof e=="string"?M[e]||_(e):v.extend({},e);var n,r,i,s,o,u,a=[],f=!e.once&&[],l=function(t){n=e.memory&&t,r=!0,u=s||0,s=0,o=a.length,i=!0;for(;a&&u<o;u++)if(a[u].apply(t[0],t[1])===!1&&e.stopOnFalse){n=!1;break}i=!1,a&&(f?f.length&&l(f.shift()):n?a=[]:c.disable())},c={add:function(){if(a){var t=a.length;(function r(t){v.each(t,function(t,n){var i=v.type(n);i==="function"?(!e.unique||!c.has(n))&&a.push(n):n&&n.length&&i!=="string"&&r(n)})})(arguments),i?o=a.length:n&&(s=t,l(n))}return this},remove:function(){return a&&v.each(arguments,function(e,t){var n;while((n=v.inArray(t,a,n))>-1)a.splice(n,1),i&&(n<=o&&o--,n<=u&&u--)}),this},has:function(e){return v.inArray(e,a)>-1},empty:function(){return a=[],this},disable:function(){return a=f=n=t,this},disabled:function(){return!a},lock:function(){return f=t,n||c.disable(),this},locked:function(){return!f},fireWith:function(e,t){return t=t||[],t=[e,t.slice?t.slice():t],a&&(!r||f)&&(i?f.push(t):l(t)),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},v.extend({Deferred:function(e){var t=[["resolve","done",v.Callbacks("once memory"),"resolved"],["reject","fail",v.Callbacks("once memory"),"rejected"],["notify","progress",v.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var e=arguments;return v.Deferred(function(n){v.each(t,function(t,r){var s=r[0],o=e[t];i[r[1]](v.isFunction(o)?function(){var e=o.apply(this,arguments);e&&v.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[s+"With"](this===i?n:this,[e])}:n[s])}),e=null}).promise()},promise:function(e){return e!=null?v.extend(e,r):r}},i={};return r.pipe=r.then,v.each(t,function(e,s){var o=s[2],u=s[3];r[s[1]]=o.add,u&&o.add(function(){n=u},t[e^1][2].disable,t[2][2].lock),i[s[0]]=o.fire,i[s[0]+"With"]=o.fireWith}),r.promise(i),e&&e.call(i,i),i},when:function(e){var t=0,n=l.call(arguments),r=n.length,i=r!==1||e&&v.isFunction(e.promise)?r:0,s=i===1?e:v.Deferred(),o=function(e,t,n){return function(r){t[e]=this,n[e]=arguments.length>1?l.call(arguments):r,n===u?s.notifyWith(t,n):--i||s.resolveWith(t,n)}},u,a,f;if(r>1){u=new Array(r),a=new Array(r),f=new Array(r);for(;t<r;t++)n[t]&&v.isFunction(n[t].promise)?n[t].promise().done(o(t,f,n)).fail(s.reject).progress(o(t,a,u)):--i}return i||s.resolveWith(f,n),s.promise()}}),v.support=function(){var t,n,r,s,o,u,a,f,l,c,h,p=i.createElement("div");p.setAttribute("className","t"),p.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",n=p.getElementsByTagName("*"),r=p.getElementsByTagName("a")[0];if(!n||!r||!n.length)return{};s=i.createElement("select"),o=s.appendChild(i.createElement("option")),u=p.getElementsByTagName("input")[0],r.style.cssText="top:1px;float:left;opacity:.5",t={leadingWhitespace:p.firstChild.nodeType===3,tbody:!p.getElementsByTagName("tbody").length,htmlSerialize:!!p.getElementsByTagName("link").length,style:/top/.test(r.getAttribute("style")),hrefNormalized:r.getAttribute("href")==="/a",opacity:/^0.5/.test(r.style.opacity),cssFloat:!!r.style.cssFloat,checkOn:u.value==="on",optSelected:o.selected,getSetAttribute:p.className!=="t",enctype:!!i.createElement("form").enctype,html5Clone:i.createElement("nav").cloneNode(!0).outerHTML!=="<:nav></:nav>",boxModel:i.compatMode==="CSS1Compat",submitBubbles:!0,changeBubbles:!0,focusinBubbles:!1,deleteExpando:!0,noCloneEvent:!0,inlineBlockNeedsLayout:!1,shrinkWrapBlocks:!1,reliableMarginRight:!0,boxSizingReliable:!0,pixelPosition:!1},u.checked=!0,t.noCloneChecked=u.cloneNode(!0).checked,s.disabled=!0,t.optDisabled=!o.disabled;try{delete p.test}catch(d){t.deleteExpando=!1}!p.addEventListener&&p.attachEvent&&p.fireEvent&&(p.attachEvent("onclick",h=function(){t.noCloneEvent=!1}),p.cloneNode(!0).fireEvent("onclick"),p.detachEvent("onclick",h)),u=i.createElement("input"),u.value="t",u.setAttribute("type","radio"),t.radioValue=u.value==="t",u.setAttribute("checked","checked"),u.setAttribute("name","t"),p.appendChild(u),a=i.createDocumentFragment(),a.appendChild(p.lastChild),t.checkClone=a.cloneNode(!0).cloneNode(!0).lastChild.checked,t.appendChecked=u.checked,a.removeChild(u),a.appendChild(p);if(p.attachEvent)for(l in{submit:!0,change:!0,focusin:!0})f="on"+l,c=f in p,c||(p.setAttribute(f,"return;"),c=typeof p[f]=="function"),t[l+"Bubbles"]=c;return v(function(){var n,r,s,o,u="padding:0;margin:0;border:0;display:block;overflow:hidden;",a=i.getElementsByTagName("body")[0];if(!a)return;n=i.createElement("div"),n.style.cssText="visibility:hidden;border:0;width:0;height:0;position:static;top:0;margin-top:1px",a.insertBefore(n,a.firstChild),r=i.createElement("div"),n.appendChild(r),r.innerHTML="<table><tr><td></td><td>t</td></tr></table>",s=r.getElementsByTagName("td"),s[0].style.cssText="padding:0;margin:0;border:0;display:none",c=s[0].offsetHeight===0,s[0].style.display="",s[1].style.display="none",t.reliableHiddenOffsets=c&&s[0].offsetHeight===0,r.innerHTML="",r.style.cssText="box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%;",t.boxSizing=r.offsetWidth===4,t.doesNotIncludeMarginInBodyOffset=a.offsetTop!==1,e.getComputedStyle&&(t.pixelPosition=(e.getComputedStyle(r,null)||{}).top!=="1%",t.boxSizingReliable=(e.getComputedStyle(r,null)||{width:"4px"}).width==="4px",o=i.createElement("div"),o.style.cssText=r.style.cssText=u,o.style.marginRight=o.style.width="0",r.style.width="1px",r.appendChild(o),t.reliableMarginRight=!parseFloat((e.getComputedStyle(o,null)||{}).marginRight)),typeof r.style.zoom!="undefined"&&(r.innerHTML="",r.style.cssText=u+"width:1px;padding:1px;display:inline;zoom:1",t.inlineBlockNeedsLayout=r.offsetWidth===3,r.style.display="block",r.style.overflow="visible",r.innerHTML="<div></div>",r.firstChild.style.width="5px",t.shrinkWrapBlocks=r.offsetWidth!==3,n.style.zoom=1),a.removeChild(n),n=r=s=o=null}),a.removeChild(p),n=r=s=o=u=a=p=null,t}();var D=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,P=/([A-Z])/g;v.extend({cache:{},deletedIds:[],uuid:0,expando:"jQuery"+(v.fn.jquery+Math.random()).replace(/\D/g,""),noData:{embed:!0,object:"clsid:D27CDB6E-AE6D-11cf-96B8-************",applet:!0},hasData:function(e){return e=e.nodeType?v.cache[e[v.expando]]:e[v.expando],!!e&&!B(e)},data:function(e,n,r,i){if(!v.acceptData(e))return;var s,o,u=v.expando,a=typeof n=="string",f=e.nodeType,l=f?v.cache:e,c=f?e[u]:e[u]&&u;if((!c||!l[c]||!i&&!l[c].data)&&a&&r===t)return;c||(f?e[u]=c=v.deletedIds.pop()||v.guid++:c=u),l[c]||(l[c]={},f||(l[c].toJSON=v.noop));if(typeof n=="object"||typeof n=="function")i?l[c]=v.extend(l[c],n):l[c].data=v.extend(l[c].data,n);return s=l[c],i||(s.data||(s.data={}),s=s.data),r!==t&&(s[v.camelCase(n)]=r),a?(o=s[n],o==null&&(o=s[v.camelCase(n)])):o=s,o},removeData:function(e,t,n){if(!v.acceptData(e))return;var r,i,s,o=e.nodeType,u=o?v.cache:e,a=o?e[v.expando]:v.expando;if(!u[a])return;if(t){r=n?u[a]:u[a].data;if(r){v.isArray(t)||(t in r?t=[t]:(t=v.camelCase(t),t in r?t=[t]:t=t.split(" ")));for(i=0,s=t.length;i<s;i++)delete r[t[i]];if(!(n?B:v.isEmptyObject)(r))return}}if(!n){delete u[a].data;if(!B(u[a]))return}o?v.cleanData([e],!0):v.support.deleteExpando||u!=u.window?delete u[a]:u[a]=null},_data:function(e,t,n){return v.data(e,t,n,!0)},acceptData:function(e){var t=e.nodeName&&v.noData[e.nodeName.toLowerCase()];return!t||t!==!0&&e.getAttribute("classid")===t}}),v.fn.extend({data:function(e,n){var r,i,s,o,u,a=this[0],f=0,l=null;if(e===t){if(this.length){l=v.data(a);if(a.nodeType===1&&!v._data(a,"parsedAttrs")){s=a.attributes;for(u=s.length;f<u;f++)o=s[f].name,o.indexOf("data-")||(o=v.camelCase(o.substring(5)),H(a,o,l[o]));v._data(a,"parsedAttrs",!0)}}return l}return typeof e=="object"?this.each(function(){v.data(this,e)}):(r=e.split(".",2),r[1]=r[1]?"."+r[1]:"",i=r[1]+"!",v.access(this,function(n){if(n===t)return l=this.triggerHandler("getData"+i,[r[0]]),l===t&&a&&(l=v.data(a,e),l=H(a,e,l)),l===t&&r[1]?this.data(r[0]):l;r[1]=n,this.each(function(){var t=v(this);t.triggerHandler("setData"+i,r),v.data(this,e,n),t.triggerHandler("changeData"+i,r)})},null,n,arguments.length>1,null,!1))},removeData:function(e){return this.each(function(){v.removeData(this,e)})}}),v.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=v._data(e,t),n&&(!r||v.isArray(n)?r=v._data(e,t,v.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=v.queue(e,t),r=n.length,i=n.shift(),s=v._queueHooks(e,t),o=function(){v.dequeue(e,t)};i==="inprogress"&&(i=n.shift(),r--),i&&(t==="fx"&&n.unshift("inprogress"),delete s.stop,i.call(e,o,s)),!r&&s&&s.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return v._data(e,n)||v._data(e,n,{empty:v.Callbacks("once memory").add(function(){v.removeData(e,t+"queue",!0),v.removeData(e,n,!0)})})}}),v.fn.extend({queue:function(e,n){var r=2;return typeof e!="string"&&(n=e,e="fx",r--),arguments.length<r?v.queue(this[0],e):n===t?this:this.each(function(){var t=v.queue(this,e,n);v._queueHooks(this,e),e==="fx"&&t[0]!=="inprogress"&&v.dequeue(this,e)})},dequeue:function(e){return this.each(function(){v.dequeue(this,e)})},delay:function(e,t){return e=v.fx?v.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,n){var r,i=1,s=v.Deferred(),o=this,u=this.length,a=function(){--i||s.resolveWith(o,[o])};typeof e!="string"&&(n=e,e=t),e=e||"fx";while(u--)r=v._data(o[u],e+"queueHooks"),r&&r.empty&&(i++,r.empty.add(a));return a(),s.promise(n)}});var j,F,I,q=/[\t\r\n]/g,R=/\r/g,U=/^(?:button|input)$/i,z=/^(?:button|input|object|select|textarea)$/i,W=/^a(?:rea|)$/i,X=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,V=v.support.getSetAttribute;v.fn.extend({attr:function(e,t){return v.access(this,v.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){v.removeAttr(this,e)})},prop:function(e,t){return v.access(this,v.prop,e,t,arguments.length>1)},removeProp:function(e){return e=v.propFix[e]||e,this.each(function(){try{this[e]=t,delete this[e]}catch(n){}})},addClass:function(e){var t,n,r,i,s,o,u;if(v.isFunction(e))return this.each(function(t){v(this).addClass(e.call(this,t,this.className))});if(e&&typeof e=="string"){t=e.split(y);for(n=0,r=this.length;n<r;n++){i=this[n];if(i.nodeType===1)if(!i.className&&t.length===1)i.className=e;else{s=" "+i.className+" ";for(o=0,u=t.length;o<u;o++)s.indexOf(" "+t[o]+" ")<0&&(s+=t[o]+" ");i.className=v.trim(s)}}}return this},removeClass:function(e){var n,r,i,s,o,u,a;if(v.isFunction(e))return this.each(function(t){v(this).removeClass(e.call(this,t,this.className))});if(e&&typeof e=="string"||e===t){n=(e||"").split(y);for(u=0,a=this.length;u<a;u++){i=this[u];if(i.nodeType===1&&i.className){r=(" "+i.className+" ").replace(q," ");for(s=0,o=n.length;s<o;s++)while(r.indexOf(" "+n[s]+" ")>=0)r=r.replace(" "+n[s]+" "," ");i.className=e?v.trim(r):""}}}return this},toggleClass:function(e,t){var n=typeof e,r=typeof t=="boolean";return v.isFunction(e)?this.each(function(n){v(this).toggleClass(e.call(this,n,this.className,t),t)}):this.each(function(){if(n==="string"){var i,s=0,o=v(this),u=t,a=e.split(y);while(i=a[s++])u=r?u:!o.hasClass(i),o[u?"addClass":"removeClass"](i)}else if(n==="undefined"||n==="boolean")this.className&&v._data(this,"__className__",this.className),this.className=this.className||e===!1?"":v._data(this,"__className__")||""})},hasClass:function(e){var t=" "+e+" ",n=0,r=this.length;for(;n<r;n++)if(this[n].nodeType===1&&(" "+this[n].className+" ").replace(q," ").indexOf(t)>=0)return!0;return!1},val:function(e){var n,r,i,s=this[0];if(!arguments.length){if(s)return n=v.valHooks[s.type]||v.valHooks[s.nodeName.toLowerCase()],n&&"get"in n&&(r=n.get(s,"value"))!==t?r:(r=s.value,typeof r=="string"?r.replace(R,""):r==null?"":r);return}return i=v.isFunction(e),this.each(function(r){var s,o=v(this);if(this.nodeType!==1)return;i?s=e.call(this,r,o.val()):s=e,s==null?s="":typeof s=="number"?s+="":v.isArray(s)&&(s=v.map(s,function(e){return e==null?"":e+""})),n=v.valHooks[this.type]||v.valHooks[this.nodeName.toLowerCase()];if(!n||!("set"in n)||n.set(this,s,"value")===t)this.value=s})}}),v.extend({valHooks:{option:{get:function(e){var t=e.attributes.value;return!t||t.specified?e.value:e.text}},select:{get:function(e){var t,n,r=e.options,i=e.selectedIndex,s=e.type==="select-one"||i<0,o=s?null:[],u=s?i+1:r.length,a=i<0?u:s?i:0;for(;a<u;a++){n=r[a];if((n.selected||a===i)&&(v.support.optDisabled?!n.disabled:n.getAttribute("disabled")===null)&&(!n.parentNode.disabled||!v.nodeName(n.parentNode,"optgroup"))){t=v(n).val();if(s)return t;o.push(t)}}return o},set:function(e,t){var n=v.makeArray(t);return v(e).find("option").each(function(){this.selected=v.inArray(v(this).val(),n)>=0}),n.length||(e.selectedIndex=-1),n}}},attrFn:{},attr:function(e,n,r,i){var s,o,u,a=e.nodeType;if(!e||a===3||a===8||a===2)return;if(i&&v.isFunction(v.fn[n]))return v(e)[n](r);if(typeof e.getAttribute=="undefined")return v.prop(e,n,r);u=a!==1||!v.isXMLDoc(e),u&&(n=n.toLowerCase(),o=v.attrHooks[n]||(X.test(n)?F:j));if(r!==t){if(r===null){v.removeAttr(e,n);return}return o&&"set"in o&&u&&(s=o.set(e,r,n))!==t?s:(e.setAttribute(n,r+""),r)}return o&&"get"in o&&u&&(s=o.get(e,n))!==null?s:(s=e.getAttribute(n),s===null?t:s)},removeAttr:function(e,t){var n,r,i,s,o=0;if(t&&e.nodeType===1){r=t.split(y);for(;o<r.length;o++)i=r[o],i&&(n=v.propFix[i]||i,s=X.test(i),s||v.attr(e,i,""),e.removeAttribute(V?i:n),s&&n in e&&(e[n]=!1))}},attrHooks:{type:{set:function(e,t){if(U.test(e.nodeName)&&e.parentNode)v.error("type property can't be changed");else if(!v.support.radioValue&&t==="radio"&&v.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}},value:{get:function(e,t){return j&&v.nodeName(e,"button")?j.get(e,t):t in e?e.value:null},set:function(e,t,n){if(j&&v.nodeName(e,"button"))return j.set(e,t,n);e.value=t}}},propFix:{tabindex:"tabIndex",readonly:"readOnly","for":"htmlFor","class":"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},prop:function(e,n,r){var i,s,o,u=e.nodeType;if(!e||u===3||u===8||u===2)return;return o=u!==1||!v.isXMLDoc(e),o&&(n=v.propFix[n]||n,s=v.propHooks[n]),r!==t?s&&"set"in s&&(i=s.set(e,r,n))!==t?i:e[n]=r:s&&"get"in s&&(i=s.get(e,n))!==null?i:e[n]},propHooks:{tabIndex:{get:function(e){var n=e.getAttributeNode("tabindex");return n&&n.specified?parseInt(n.value,10):z.test(e.nodeName)||W.test(e.nodeName)&&e.href?0:t}}}}),F={get:function(e,n){var r,i=v.prop(e,n);return i===!0||typeof i!="boolean"&&(r=e.getAttributeNode(n))&&r.nodeValue!==!1?n.toLowerCase():t},set:function(e,t,n){var r;return t===!1?v.removeAttr(e,n):(r=v.propFix[n]||n,r in e&&(e[r]=!0),e.setAttribute(n,n.toLowerCase())),n}},V||(I={name:!0,id:!0,coords:!0},j=v.valHooks.button={get:function(e,n){var r;return r=e.getAttributeNode(n),r&&(I[n]?r.value!=="":r.specified)?r.value:t},set:function(e,t,n){var r=e.getAttributeNode(n);return r||(r=i.createAttribute(n),e.setAttributeNode(r)),r.value=t+""}},v.each(["width","height"],function(e,t){v.attrHooks[t]=v.extend(v.attrHooks[t],{set:function(e,n){if(n==="")return e.setAttribute(t,"auto"),n}})}),v.attrHooks.contenteditable={get:j.get,set:function(e,t,n){t===""&&(t="false"),j.set(e,t,n)}}),v.support.hrefNormalized||v.each(["href","src","width","height"],function(e,n){v.attrHooks[n]=v.extend(v.attrHooks[n],{get:function(e){var r=e.getAttribute(n,2);return r===null?t:r}})}),v.support.style||(v.attrHooks.style={get:function(e){return e.style.cssText.toLowerCase()||t},set:function(e,t){return e.style.cssText=t+""}}),v.support.optSelected||(v.propHooks.selected=v.extend(v.propHooks.selected,{get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}})),v.support.enctype||(v.propFix.enctype="encoding"),v.support.checkOn||v.each(["radio","checkbox"],function(){v.valHooks[this]={get:function(e){return e.getAttribute("value")===null?"on":e.value}}}),v.each(["radio","checkbox"],function(){v.valHooks[this]=v.extend(v.valHooks[this],{set:function(e,t){if(v.isArray(t))return e.checked=v.inArray(v(e).val(),t)>=0}})});var $=/^(?:textarea|input|select)$/i,J=/^([^\.]*|)(?:\.(.+)|)$/,K=/(?:^|\s)hover(\.\S+|)\b/,Q=/^key/,G=/^(?:mouse|contextmenu)|click/,Y=/^(?:focusinfocus|focusoutblur)$/,Z=function(e){return v.event.special.hover?e:e.replace(K,"mouseenter$1 mouseleave$1")};v.event={add:function(e,n,r,i,s){var o,u,a,f,l,c,h,p,d,m,g;if(e.nodeType===3||e.nodeType===8||!n||!r||!(o=v._data(e)))return;r.handler&&(d=r,r=d.handler,s=d.selector),r.guid||(r.guid=v.guid++),a=o.events,a||(o.events=a={}),u=o.handle,u||(o.handle=u=function(e){return typeof v=="undefined"||!!e&&v.event.triggered===e.type?t:v.event.dispatch.apply(u.elem,arguments)},u.elem=e),n=v.trim(Z(n)).split(" ");for(f=0;f<n.length;f++){l=J.exec(n[f])||[],c=l[1],h=(l[2]||"").split(".").sort(),g=v.event.special[c]||{},c=(s?g.delegateType:g.bindType)||c,g=v.event.special[c]||{},p=v.extend({type:c,origType:l[1],data:i,handler:r,guid:r.guid,selector:s,needsContext:s&&v.expr.match.needsContext.test(s),namespace:h.join(".")},d),m=a[c];if(!m){m=a[c]=[],m.delegateCount=0;if(!g.setup||g.setup.call(e,i,h,u)===!1)e.addEventListener?e.addEventListener(c,u,!1):e.attachEvent&&e.attachEvent("on"+c,u)}g.add&&(g.add.call(e,p),p.handler.guid||(p.handler.guid=r.guid)),s?m.splice(m.delegateCount++,0,p):m.push(p),v.event.global[c]=!0}e=null},global:{},remove:function(e,t,n,r,i){var s,o,u,a,f,l,c,h,p,d,m,g=v.hasData(e)&&v._data(e);if(!g||!(h=g.events))return;t=v.trim(Z(t||"")).split(" ");for(s=0;s<t.length;s++){o=J.exec(t[s])||[],u=a=o[1],f=o[2];if(!u){for(u in h)v.event.remove(e,u+t[s],n,r,!0);continue}p=v.event.special[u]||{},u=(r?p.delegateType:p.bindType)||u,d=h[u]||[],l=d.length,f=f?new RegExp("(^|\\.)"+f.split(".").sort().join("\\.(?:.*\\.|)")+"(\\.|$)"):null;for(c=0;c<d.length;c++)m=d[c],(i||a===m.origType)&&(!n||n.guid===m.guid)&&(!f||f.test(m.namespace))&&(!r||r===m.selector||r==="**"&&m.selector)&&(d.splice(c--,1),m.selector&&d.delegateCount--,p.remove&&p.remove.call(e,m));d.length===0&&l!==d.length&&((!p.teardown||p.teardown.call(e,f,g.handle)===!1)&&v.removeEvent(e,u,g.handle),delete h[u])}v.isEmptyObject(h)&&(delete g.handle,v.removeData(e,"events",!0))},customEvent:{getData:!0,setData:!0,changeData:!0},trigger:function(n,r,s,o){if(!s||s.nodeType!==3&&s.nodeType!==8){var u,a,f,l,c,h,p,d,m,g,y=n.type||n,b=[];if(Y.test(y+v.event.triggered))return;y.indexOf("!")>=0&&(y=y.slice(0,-1),a=!0),y.indexOf(".")>=0&&(b=y.split("."),y=b.shift(),b.sort());if((!s||v.event.customEvent[y])&&!v.event.global[y])return;n=typeof n=="object"?n[v.expando]?n:new v.Event(y,n):new v.Event(y),n.type=y,n.isTrigger=!0,n.exclusive=a,n.namespace=b.join("."),n.namespace_re=n.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,h=y.indexOf(":")<0?"on"+y:"";if(!s){u=v.cache;for(f in u)u[f].events&&u[f].events[y]&&v.event.trigger(n,r,u[f].handle.elem,!0);return}n.result=t,n.target||(n.target=s),r=r!=null?v.makeArray(r):[],r.unshift(n),p=v.event.special[y]||{};if(p.trigger&&p.trigger.apply(s,r)===!1)return;m=[[s,p.bindType||y]];if(!o&&!p.noBubble&&!v.isWindow(s)){g=p.delegateType||y,l=Y.test(g+y)?s:s.parentNode;for(c=s;l;l=l.parentNode)m.push([l,g]),c=l;c===(s.ownerDocument||i)&&m.push([c.defaultView||c.parentWindow||e,g])}for(f=0;f<m.length&&!n.isPropagationStopped();f++)l=m[f][0],n.type=m[f][1],d=(v._data(l,"events")||{})[n.type]&&v._data(l,"handle"),d&&d.apply(l,r),d=h&&l[h],d&&v.acceptData(l)&&d.apply&&d.apply(l,r)===!1&&n.preventDefault();return n.type=y,!o&&!n.isDefaultPrevented()&&(!p._default||p._default.apply(s.ownerDocument,r)===!1)&&(y!=="click"||!v.nodeName(s,"a"))&&v.acceptData(s)&&h&&s[y]&&(y!=="focus"&&y!=="blur"||n.target.offsetWidth!==0)&&!v.isWindow(s)&&(c=s[h],c&&(s[h]=null),v.event.triggered=y,s[y](),v.event.triggered=t,c&&(s[h]=c)),n.result}return},dispatch:function(n){n=v.event.fix(n||e.event);var r,i,s,o,u,a,f,c,h,p,d=(v._data(this,"events")||{})[n.type]||[],m=d.delegateCount,g=l.call(arguments),y=!n.exclusive&&!n.namespace,b=v.event.special[n.type]||{},w=[];g[0]=n,n.delegateTarget=this;if(b.preDispatch&&b.preDispatch.call(this,n)===!1)return;if(m&&(!n.button||n.type!=="click"))for(s=n.target;s!=this;s=s.parentNode||this)if(s.disabled!==!0||n.type!=="click"){u={},f=[];for(r=0;r<m;r++)c=d[r],h=c.selector,u[h]===t&&(u[h]=c.needsContext?v(h,this).index(s)>=0:v.find(h,this,null,[s]).length),u[h]&&f.push(c);f.length&&w.push({elem:s,matches:f})}d.length>m&&w.push({elem:this,matches:d.slice(m)});for(r=0;r<w.length&&!n.isPropagationStopped();r++){a=w[r],n.currentTarget=a.elem;for(i=0;i<a.matches.length&&!n.isImmediatePropagationStopped();i++){c=a.matches[i];if(y||!n.namespace&&!c.namespace||n.namespace_re&&n.namespace_re.test(c.namespace))n.data=c.data,n.handleObj=c,o=((v.event.special[c.origType]||{}).handle||c.handler).apply(a.elem,g),o!==t&&(n.result=o,o===!1&&(n.preventDefault(),n.stopPropagation()))}}return b.postDispatch&&b.postDispatch.call(this,n),n.result},props:"attrChange attrName relatedNode srcElement altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return e.which==null&&(e.which=t.charCode!=null?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,n){var r,s,o,u=n.button,a=n.fromElement;return e.pageX==null&&n.clientX!=null&&(r=e.target.ownerDocument||i,s=r.documentElement,o=r.body,e.pageX=n.clientX+(s&&s.scrollLeft||o&&o.scrollLeft||0)-(s&&s.clientLeft||o&&o.clientLeft||0),e.pageY=n.clientY+(s&&s.scrollTop||o&&o.scrollTop||0)-(s&&s.clientTop||o&&o.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?n.toElement:a),!e.which&&u!==t&&(e.which=u&1?1:u&2?3:u&4?2:0),e}},fix:function(e){if(e[v.expando])return e;var t,n,r=e,s=v.event.fixHooks[e.type]||{},o=s.props?this.props.concat(s.props):this.props;e=v.Event(r);for(t=o.length;t;)n=o[--t],e[n]=r[n];return e.target||(e.target=r.srcElement||i),e.target.nodeType===3&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,s.filter?s.filter(e,r):e},special:{load:{noBubble:!0},focus:{delegateType:"focusin"},blur:{delegateType:"focusout"},beforeunload:{setup:function(e,t,n){v.isWindow(this)&&(this.onbeforeunload=n)},teardown:function(e,t){this.onbeforeunload===t&&(this.onbeforeunload=null)}}},simulate:function(e,t,n,r){var i=v.extend(new v.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?v.event.trigger(i,null,t):v.event.dispatch.call(t,i),i.isDefaultPrevented()&&n.preventDefault()}},v.event.handle=v.event.dispatch,v.removeEvent=i.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var r="on"+t;e.detachEvent&&(typeof e[r]=="undefined"&&(e[r]=null),e.detachEvent(r,n))},v.Event=function(e,t){if(!(this instanceof v.Event))return new v.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||e.returnValue===!1||e.getPreventDefault&&e.getPreventDefault()?tt:et):this.type=e,t&&v.extend(this,t),this.timeStamp=e&&e.timeStamp||v.now(),this[v.expando]=!0},v.Event.prototype={preventDefault:function(){this.isDefaultPrevented=tt;var e=this.originalEvent;if(!e)return;e.preventDefault?e.preventDefault():e.returnValue=!1},stopPropagation:function(){this.isPropagationStopped=tt;var e=this.originalEvent;if(!e)return;e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=tt,this.stopPropagation()},isDefaultPrevented:et,isPropagationStopped:et,isImmediatePropagationStopped:et},v.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(e,t){v.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,s=e.handleObj,o=s.selector;if(!i||i!==r&&!v.contains(r,i))e.type=s.origType,n=s.handler.apply(this,arguments),e.type=t;return n}}}),v.support.submitBubbles||(v.event.special.submit={setup:function(){if(v.nodeName(this,"form"))return!1;v.event.add(this,"click._submit keypress._submit",function(e){var n=e.target,r=v.nodeName(n,"input")||v.nodeName(n,"button")?n.form:t;r&&!v._data(r,"_submit_attached")&&(v.event.add(r,"submit._submit",function(e){e._submit_bubble=!0}),v._data(r,"_submit_attached",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&v.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){if(v.nodeName(this,"form"))return!1;v.event.remove(this,"._submit")}}),v.support.changeBubbles||(v.event.special.change={setup:function(){if($.test(this.nodeName)){if(this.type==="checkbox"||this.type==="radio")v.event.add(this,"propertychange._change",function(e){e.originalEvent.propertyName==="checked"&&(this._just_changed=!0)}),v.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),v.event.simulate("change",this,e,!0)});return!1}v.event.add(this,"beforeactivate._change",function(e){var t=e.target;$.test(t.nodeName)&&!v._data(t,"_change_attached")&&(v.event.add(t,"change._change",function(e){this.parentNode&&!e.isSimulated&&!e.isTrigger&&v.event.simulate("change",this.parentNode,e,!0)}),v._data(t,"_change_attached",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||t.type!=="radio"&&t.type!=="checkbox")return e.handleObj.handler.apply(this,arguments)},teardown:function(){return v.event.remove(this,"._change"),!$.test(this.nodeName)}}),v.support.focusinBubbles||v.each({focus:"focusin",blur:"focusout"},function(e,t){var n=0,r=function(e){v.event.simulate(t,e.target,v.event.fix(e),!0)};v.event.special[t]={setup:function(){n++===0&&i.addEventListener(e,r,!0)},teardown:function(){--n===0&&i.removeEventListener(e,r,!0)}}}),v.fn.extend({on:function(e,n,r,i,s){var o,u;if(typeof e=="object"){typeof n!="string"&&(r=r||n,n=t);for(u in e)this.on(u,n,r,e[u],s);return this}r==null&&i==null?(i=n,r=n=t):i==null&&(typeof n=="string"?(i=r,r=t):(i=r,r=n,n=t));if(i===!1)i=et;else if(!i)return this;return s===1&&(o=i,i=function(e){return v().off(e),o.apply(this,arguments)},i.guid=o.guid||(o.guid=v.guid++)),this.each(function(){v.event.add(this,e,i,r,n)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,n,r){var i,s;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,v(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if(typeof e=="object"){for(s in e)this.off(s,n,e[s]);return this}if(n===!1||typeof n=="function")r=n,n=t;return r===!1&&(r=et),this.each(function(){v.event.remove(this,e,r,n)})},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},live:function(e,t,n){return v(this.context).on(e,this.selector,t,n),this},die:function(e,t){return v(this.context).off(e,this.selector||"**",t),this},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return arguments.length===1?this.off(e,"**"):this.off(t,e||"**",n)},trigger:function(e,t){return this.each(function(){v.event.trigger(e,t,this)})},triggerHandler:function(e,t){if(this[0])return v.event.trigger(e,t,this[0],!0)},toggle:function(e){var t=arguments,n=e.guid||v.guid++,r=0,i=function(n){var i=(v._data(this,"lastToggle"+e.guid)||0)%r;return v._data(this,"lastToggle"+e.guid,i+1),n.preventDefault(),t[i].apply(this,arguments)||!1};i.guid=n;while(r<t.length)t[r++].guid=n;return this.click(i)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),v.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){v.fn[t]=function(e,n){return n==null&&(n=e,e=null),arguments.length>0?this.on(t,null,e,n):this.trigger(t)},Q.test(t)&&(v.event.fixHooks[t]=v.event.keyHooks),G.test(t)&&(v.event.fixHooks[t]=v.event.mouseHooks)}),function(e,t){function nt(e,t,n,r){n=n||[],t=t||g;var i,s,a,f,l=t.nodeType;if(!e||typeof e!="string")return n;if(l!==1&&l!==9)return[];a=o(t);if(!a&&!r)if(i=R.exec(e))if(f=i[1]){if(l===9){s=t.getElementById(f);if(!s||!s.parentNode)return n;if(s.id===f)return n.push(s),n}else if(t.ownerDocument&&(s=t.ownerDocument.getElementById(f))&&u(t,s)&&s.id===f)return n.push(s),n}else{if(i[2])return S.apply(n,x.call(t.getElementsByTagName(e),0)),n;if((f=i[3])&&Z&&t.getElementsByClassName)return S.apply(n,x.call(t.getElementsByClassName(f),0)),n}return vt(e.replace(j,"$1"),t,n,r,a)}function rt(e){return function(t){var n=t.nodeName.toLowerCase();return n==="input"&&t.type===e}}function it(e){return function(t){var n=t.nodeName.toLowerCase();return(n==="input"||n==="button")&&t.type===e}}function st(e){return N(function(t){return t=+t,N(function(n,r){var i,s=e([],n.length,t),o=s.length;while(o--)n[i=s[o]]&&(n[i]=!(r[i]=n[i]))})})}function ot(e,t,n){if(e===t)return n;var r=e.nextSibling;while(r){if(r===t)return-1;r=r.nextSibling}return 1}function ut(e,t){var n,r,s,o,u,a,f,l=L[d][e+" "];if(l)return t?0:l.slice(0);u=e,a=[],f=i.preFilter;while(u){if(!n||(r=F.exec(u)))r&&(u=u.slice(r[0].length)||u),a.push(s=[]);n=!1;if(r=I.exec(u))s.push(n=new m(r.shift())),u=u.slice(n.length),n.type=r[0].replace(j," ");for(o in i.filter)(r=J[o].exec(u))&&(!f[o]||(r=f[o](r)))&&(s.push(n=new m(r.shift())),u=u.slice(n.length),n.type=o,n.matches=r);if(!n)break}return t?u.length:u?nt.error(e):L(e,a).slice(0)}function at(e,t,r){var i=t.dir,s=r&&t.dir==="parentNode",o=w++;return t.first?function(t,n,r){while(t=t[i])if(s||t.nodeType===1)return e(t,n,r)}:function(t,r,u){if(!u){var a,f=b+" "+o+" ",l=f+n;while(t=t[i])if(s||t.nodeType===1){if((a=t[d])===l)return t.sizset;if(typeof a=="string"&&a.indexOf(f)===0){if(t.sizset)return t}else{t[d]=l;if(e(t,r,u))return t.sizset=!0,t;t.sizset=!1}}}else while(t=t[i])if(s||t.nodeType===1)if(e(t,r,u))return t}}function ft(e){return e.length>1?function(t,n,r){var i=e.length;while(i--)if(!e[i](t,n,r))return!1;return!0}:e[0]}function lt(e,t,n,r,i){var s,o=[],u=0,a=e.length,f=t!=null;for(;u<a;u++)if(s=e[u])if(!n||n(s,r,i))o.push(s),f&&t.push(u);return o}function ct(e,t,n,r,i,s){return r&&!r[d]&&(r=ct(r)),i&&!i[d]&&(i=ct(i,s)),N(function(s,o,u,a){var f,l,c,h=[],p=[],d=o.length,v=s||dt(t||"*",u.nodeType?[u]:u,[]),m=e&&(s||!t)?lt(v,h,e,u,a):v,g=n?i||(s?e:d||r)?[]:o:m;n&&n(m,g,u,a);if(r){f=lt(g,p),r(f,[],u,a),l=f.length;while(l--)if(c=f[l])g[p[l]]=!(m[p[l]]=c)}if(s){if(i||e){if(i){f=[],l=g.length;while(l--)(c=g[l])&&f.push(m[l]=c);i(null,g=[],f,a)}l=g.length;while(l--)(c=g[l])&&(f=i?T.call(s,c):h[l])>-1&&(s[f]=!(o[f]=c))}}else g=lt(g===o?g.splice(d,g.length):g),i?i(null,o,g,a):S.apply(o,g)})}function ht(e){var t,n,r,s=e.length,o=i.relative[e[0].type],u=o||i.relative[" "],a=o?1:0,f=at(function(e){return e===t},u,!0),l=at(function(e){return T.call(t,e)>-1},u,!0),h=[function(e,n,r){return!o&&(r||n!==c)||((t=n).nodeType?f(e,n,r):l(e,n,r))}];for(;a<s;a++)if(n=i.relative[e[a].type])h=[at(ft(h),n)];else{n=i.filter[e[a].type].apply(null,e[a].matches);if(n[d]){r=++a;for(;r<s;r++)if(i.relative[e[r].type])break;return ct(a>1&&ft(h),a>1&&e.slice(0,a-1).join("").replace(j,"$1"),n,a<r&&ht(e.slice(a,r)),r<s&&ht(e=e.slice(r)),r<s&&e.join(""))}h.push(n)}return ft(h)}function pt(e,t){var r=t.length>0,s=e.length>0,o=function(u,a,f,l,h){var p,d,v,m=[],y=0,w="0",x=u&&[],T=h!=null,N=c,C=u||s&&i.find.TAG("*",h&&a.parentNode||a),k=b+=N==null?1:Math.E;T&&(c=a!==g&&a,n=o.el);for(;(p=C[w])!=null;w++){if(s&&p){for(d=0;v=e[d];d++)if(v(p,a,f)){l.push(p);break}T&&(b=k,n=++o.el)}r&&((p=!v&&p)&&y--,u&&x.push(p))}y+=w;if(r&&w!==y){for(d=0;v=t[d];d++)v(x,m,a,f);if(u){if(y>0)while(w--)!x[w]&&!m[w]&&(m[w]=E.call(l));m=lt(m)}S.apply(l,m),T&&!u&&m.length>0&&y+t.length>1&&nt.uniqueSort(l)}return T&&(b=k,c=N),x};return o.el=0,r?N(o):o}function dt(e,t,n){var r=0,i=t.length;for(;r<i;r++)nt(e,t[r],n);return n}function vt(e,t,n,r,s){var o,u,f,l,c,h=ut(e),p=h.length;if(!r&&h.length===1){u=h[0]=h[0].slice(0);if(u.length>2&&(f=u[0]).type==="ID"&&t.nodeType===9&&!s&&i.relative[u[1].type]){t=i.find.ID(f.matches[0].replace($,""),t,s)[0];if(!t)return n;e=e.slice(u.shift().length)}for(o=J.POS.test(e)?-1:u.length-1;o>=0;o--){f=u[o];if(i.relative[l=f.type])break;if(c=i.find[l])if(r=c(f.matches[0].replace($,""),z.test(u[0].type)&&t.parentNode||t,s)){u.splice(o,1),e=r.length&&u.join("");if(!e)return S.apply(n,x.call(r,0)),n;break}}}return a(e,h)(r,t,s,n,z.test(e)),n}function mt(){}var n,r,i,s,o,u,a,f,l,c,h=!0,p="undefined",d=("sizcache"+Math.random()).replace(".",""),m=String,g=e.document,y=g.documentElement,b=0,w=0,E=[].pop,S=[].push,x=[].slice,T=[].indexOf||function(e){var t=0,n=this.length;for(;t<n;t++)if(this[t]===e)return t;return-1},N=function(e,t){return e[d]=t==null||t,e},C=function(){var e={},t=[];return N(function(n,r){return t.push(n)>i.cacheLength&&delete e[t.shift()],e[n+" "]=r},e)},k=C(),L=C(),A=C(),O="[\\x20\\t\\r\\n\\f]",M="(?:\\\\.|[-\\w]|[^\\x00-\\xa0])+",_=M.replace("w","w#"),D="([*^$|!~]?=)",P="\\["+O+"*("+M+")"+O+"*(?:"+D+O+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+_+")|)|)"+O+"*\\]",H=":("+M+")(?:\\((?:(['\"])((?:\\\\.|[^\\\\])*?)\\2|([^()[\\]]*|(?:(?:"+P+")|[^:]|\\\\.)*|.*))\\)|)",B=":(even|odd|eq|gt|lt|nth|first|last)(?:\\("+O+"*((?:-\\d)?\\d*)"+O+"*\\)|)(?=[^-]|$)",j=new RegExp("^"+O+"+|((?:^|[^\\\\])(?:\\\\.)*)"+O+"+$","g"),F=new RegExp("^"+O+"*,"+O+"*"),I=new RegExp("^"+O+"*([\\x20\\t\\r\\n\\f>+~])"+O+"*"),q=new RegExp(H),R=/^(?:#([\w\-]+)|(\w+)|\.([\w\-]+))$/,U=/^:not/,z=/[\x20\t\r\n\f]*[+~]/,W=/:not\($/,X=/h\d/i,V=/input|select|textarea|button/i,$=/\\(?!\\)/g,J={ID:new RegExp("^#("+M+")"),CLASS:new RegExp("^\\.("+M+")"),NAME:new RegExp("^\\[name=['\"]?("+M+")['\"]?\\]"),TAG:new RegExp("^("+M.replace("w","w*")+")"),ATTR:new RegExp("^"+P),PSEUDO:new RegExp("^"+H),POS:new RegExp(B,"i"),CHILD:new RegExp("^:(only|nth|first|last)-child(?:\\("+O+"*(even|odd|(([+-]|)(\\d*)n|)"+O+"*(?:([+-]|)"+O+"*(\\d+)|))"+O+"*\\)|)","i"),needsContext:new RegExp("^"+O+"*[>+~]|"+B,"i")},K=function(e){var t=g.createElement("div");try{return e(t)}catch(n){return!1}finally{t=null}},Q=K(function(e){return e.appendChild(g.createComment("")),!e.getElementsByTagName("*").length}),G=K(function(e){return e.innerHTML="<a href='#'></a>",e.firstChild&&typeof e.firstChild.getAttribute!==p&&e.firstChild.getAttribute("href")==="#"}),Y=K(function(e){e.innerHTML="<select></select>";var t=typeof e.lastChild.getAttribute("multiple");return t!=="boolean"&&t!=="string"}),Z=K(function(e){return e.innerHTML="<div class='hidden e'></div><div class='hidden'></div>",!e.getElementsByClassName||!e.getElementsByClassName("e").length?!1:(e.lastChild.className="e",e.getElementsByClassName("e").length===2)}),et=K(function(e){e.id=d+0,e.innerHTML="<a name='"+d+"'></a><div name='"+d+"'></div>",y.insertBefore(e,y.firstChild);var t=g.getElementsByName&&g.getElementsByName(d).length===2+g.getElementsByName(d+0).length;return r=!g.getElementById(d),y.removeChild(e),t});try{x.call(y.childNodes,0)[0].nodeType}catch(tt){x=function(e){var t,n=[];for(;t=this[e];e++)n.push(t);return n}}nt.matches=function(e,t){return nt(e,null,null,t)},nt.matchesSelector=function(e,t){return nt(t,null,null,[e]).length>0},s=nt.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(i===1||i===9||i===11){if(typeof e.textContent=="string")return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=s(e)}else if(i===3||i===4)return e.nodeValue}else for(;t=e[r];r++)n+=s(t);return n},o=nt.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?t.nodeName!=="HTML":!1},u=nt.contains=y.contains?function(e,t){var n=e.nodeType===9?e.documentElement:e,r=t&&t.parentNode;return e===r||!!(r&&r.nodeType===1&&n.contains&&n.contains(r))}:y.compareDocumentPosition?function(e,t){return t&&!!(e.compareDocumentPosition(t)&16)}:function(e,t){while(t=t.parentNode)if(t===e)return!0;return!1},nt.attr=function(e,t){var n,r=o(e);return r||(t=t.toLowerCase()),(n=i.attrHandle[t])?n(e):r||Y?e.getAttribute(t):(n=e.getAttributeNode(t),n?typeof e[t]=="boolean"?e[t]?t:null:n.specified?n.value:null:null)},i=nt.selectors={cacheLength:50,createPseudo:N,match:J,attrHandle:G?{}:{href:function(e){return e.getAttribute("href",2)},type:function(e){return e.getAttribute("type")}},find:{ID:r?function(e,t,n){if(typeof t.getElementById!==p&&!n){var r=t.getElementById(e);return r&&r.parentNode?[r]:[]}}:function(e,n,r){if(typeof n.getElementById!==p&&!r){var i=n.getElementById(e);return i?i.id===e||typeof i.getAttributeNode!==p&&i.getAttributeNode("id").value===e?[i]:t:[]}},TAG:Q?function(e,t){if(typeof t.getElementsByTagName!==p)return t.getElementsByTagName(e)}:function(e,t){var n=t.getElementsByTagName(e);if(e==="*"){var r,i=[],s=0;for(;r=n[s];s++)r.nodeType===1&&i.push(r);return i}return n},NAME:et&&function(e,t){if(typeof t.getElementsByName!==p)return t.getElementsByName(name)},CLASS:Z&&function(e,t,n){if(typeof t.getElementsByClassName!==p&&!n)return t.getElementsByClassName(e)}},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace($,""),e[3]=(e[4]||e[5]||"").replace($,""),e[2]==="~="&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),e[1]==="nth"?(e[2]||nt.error(e[0]),e[3]=+(e[3]?e[4]+(e[5]||1):2*(e[2]==="even"||e[2]==="odd")),e[4]=+(e[6]+e[7]||e[2]==="odd")):e[2]&&nt.error(e[0]),e},PSEUDO:function(e){var t,n;if(J.CHILD.test(e[0]))return null;if(e[3])e[2]=e[3];else if(t=e[4])q.test(t)&&(n=ut(t,!0))&&(n=t.indexOf(")",t.length-n)-t.length)&&(t=t.slice(0,n),e[0]=e[0].slice(0,n)),e[2]=t;return e.slice(0,3)}},filter:{ID:r?function(e){return e=e.replace($,""),function(t){return t.getAttribute("id")===e}}:function(e){return e=e.replace($,""),function(t){var n=typeof t.getAttributeNode!==p&&t.getAttributeNode("id");return n&&n.value===e}},TAG:function(e){return e==="*"?function(){return!0}:(e=e.replace($,"").toLowerCase(),function(t){return t.nodeName&&t.nodeName.toLowerCase()===e})},CLASS:function(e){var t=k[d][e+" "];return t||(t=new RegExp("(^|"+O+")"+e+"("+O+"|$)"))&&k(e,function(e){return t.test(e.className||typeof e.getAttribute!==p&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(r,i){var s=nt.attr(r,e);return s==null?t==="!=":t?(s+="",t==="="?s===n:t==="!="?s!==n:t==="^="?n&&s.indexOf(n)===0:t==="*="?n&&s.indexOf(n)>-1:t==="$="?n&&s.substr(s.length-n.length)===n:t==="~="?(" "+s+" ").indexOf(n)>-1:t==="|="?s===n||s.substr(0,n.length+1)===n+"-":!1):!0}},CHILD:function(e,t,n,r){return e==="nth"?function(e){var t,i,s=e.parentNode;if(n===1&&r===0)return!0;if(s){i=0;for(t=s.firstChild;t;t=t.nextSibling)if(t.nodeType===1){i++;if(e===t)break}}return i-=r,i===n||i%n===0&&i/n>=0}:function(t){var n=t;switch(e){case"only":case"first":while(n=n.previousSibling)if(n.nodeType===1)return!1;if(e==="first")return!0;n=t;case"last":while(n=n.nextSibling)if(n.nodeType===1)return!1;return!0}}},PSEUDO:function(e,t){var n,r=i.pseudos[e]||i.setFilters[e.toLowerCase()]||nt.error("unsupported pseudo: "+e);return r[d]?r(t):r.length>1?(n=[e,e,"",t],i.setFilters.hasOwnProperty(e.toLowerCase())?N(function(e,n){var i,s=r(e,t),o=s.length;while(o--)i=T.call(e,s[o]),e[i]=!(n[i]=s[o])}):function(e){return r(e,0,n)}):r}},pseudos:{not:N(function(e){var t=[],n=[],r=a(e.replace(j,"$1"));return r[d]?N(function(e,t,n,i){var s,o=r(e,null,i,[]),u=e.length;while(u--)if(s=o[u])e[u]=!(t[u]=s)}):function(e,i,s){return t[0]=e,r(t,null,s,n),!n.pop()}}),has:N(function(e){return function(t){return nt(e,t).length>0}}),contains:N(function(e){return function(t){return(t.textContent||t.innerText||s(t)).indexOf(e)>-1}}),enabled:function(e){return e.disabled===!1},disabled:function(e){return e.disabled===!0},checked:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&!!e.checked||t==="option"&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},parent:function(e){return!i.pseudos.empty(e)},empty:function(e){var t;e=e.firstChild;while(e){if(e.nodeName>"@"||(t=e.nodeType)===3||t===4)return!1;e=e.nextSibling}return!0},header:function(e){return X.test(e.nodeName)},text:function(e){var t,n;return e.nodeName.toLowerCase()==="input"&&(t=e.type)==="text"&&((n=e.getAttribute("type"))==null||n.toLowerCase()===t)},radio:rt("radio"),checkbox:rt("checkbox"),file:rt("file"),password:rt("password"),image:rt("image"),submit:it("submit"),reset:it("reset"),button:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&e.type==="button"||t==="button"},input:function(e){return V.test(e.nodeName)},focus:function(e){var t=e.ownerDocument;return e===t.activeElement&&(!t.hasFocus||t.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},active:function(e){return e===e.ownerDocument.activeElement},first:st(function(){return[0]}),last:st(function(e,t){return[t-1]}),eq:st(function(e,t,n){return[n<0?n+t:n]}),even:st(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:st(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:st(function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e}),gt:st(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}},f=y.compareDocumentPosition?function(e,t){return e===t?(l=!0,0):(!e.compareDocumentPosition||!t.compareDocumentPosition?e.compareDocumentPosition:e.compareDocumentPosition(t)&4)?-1:1}:function(e,t){if(e===t)return l=!0,0;if(e.sourceIndex&&t.sourceIndex)return e.sourceIndex-t.sourceIndex;var n,r,i=[],s=[],o=e.parentNode,u=t.parentNode,a=o;if(o===u)return ot(e,t);if(!o)return-1;if(!u)return 1;while(a)i.unshift(a),a=a.parentNode;a=u;while(a)s.unshift(a),a=a.parentNode;n=i.length,r=s.length;for(var f=0;f<n&&f<r;f++)if(i[f]!==s[f])return ot(i[f],s[f]);return f===n?ot(e,s[f],-1):ot(i[f],t,1)},[0,0].sort(f),h=!l,nt.uniqueSort=function(e){var t,n=[],r=1,i=0;l=h,e.sort(f);if(l){for(;t=e[r];r++)t===e[r-1]&&(i=n.push(r));while(i--)e.splice(n[i],1)}return e},nt.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},a=nt.compile=function(e,t){var n,r=[],i=[],s=A[d][e+" "];if(!s){t||(t=ut(e)),n=t.length;while(n--)s=ht(t[n]),s[d]?r.push(s):i.push(s);s=A(e,pt(i,r))}return s},g.querySelectorAll&&function(){var e,t=vt,n=/'|\\/g,r=/\=[\x20\t\r\n\f]*([^'"\]]*)[\x20\t\r\n\f]*\]/g,i=[":focus"],s=[":active"],u=y.matchesSelector||y.mozMatchesSelector||y.webkitMatchesSelector||y.oMatchesSelector||y.msMatchesSelector;K(function(e){e.innerHTML="<select><option selected=''></option></select>",e.querySelectorAll("[selected]").length||i.push("\\["+O+"*(?:checked|disabled|ismap|multiple|readonly|selected|value)"),e.querySelectorAll(":checked").length||i.push(":checked")}),K(function(e){e.innerHTML="<p test=''></p>",e.querySelectorAll("[test^='']").length&&i.push("[*^$]="+O+"*(?:\"\"|'')"),e.innerHTML="<input type='hidden'/>",e.querySelectorAll(":enabled").length||i.push(":enabled",":disabled")}),i=new RegExp(i.join("|")),vt=function(e,r,s,o,u){if(!o&&!u&&!i.test(e)){var a,f,l=!0,c=d,h=r,p=r.nodeType===9&&e;if(r.nodeType===1&&r.nodeName.toLowerCase()!=="object"){a=ut(e),(l=r.getAttribute("id"))?c=l.replace(n,"\\$&"):r.setAttribute("id",c),c="[id='"+c+"'] ",f=a.length;while(f--)a[f]=c+a[f].join("");h=z.test(e)&&r.parentNode||r,p=a.join(",")}if(p)try{return S.apply(s,x.call(h.querySelectorAll(p),0)),s}catch(v){}finally{l||r.removeAttribute("id")}}return t(e,r,s,o,u)},u&&(K(function(t){e=u.call(t,"div");try{u.call(t,"[test!='']:sizzle"),s.push("!=",H)}catch(n){}}),s=new RegExp(s.join("|")),nt.matchesSelector=function(t,n){n=n.replace(r,"='$1']");if(!o(t)&&!s.test(n)&&!i.test(n))try{var a=u.call(t,n);if(a||e||t.document&&t.document.nodeType!==11)return a}catch(f){}return nt(n,null,null,[t]).length>0})}(),i.pseudos.nth=i.pseudos.eq,i.filters=mt.prototype=i.pseudos,i.setFilters=new mt,nt.attr=v.attr,v.find=nt,v.expr=nt.selectors,v.expr[":"]=v.expr.pseudos,v.unique=nt.uniqueSort,v.text=nt.getText,v.isXMLDoc=nt.isXML,v.contains=nt.contains}(e);var nt=/Until$/,rt=/^(?:parents|prev(?:Until|All))/,it=/^.[^:#\[\.,]*$/,st=v.expr.match.needsContext,ot={children:!0,contents:!0,next:!0,prev:!0};v.fn.extend({find:function(e){var t,n,r,i,s,o,u=this;if(typeof e!="string")return v(e).filter(function(){for(t=0,n=u.length;t<n;t++)if(v.contains(u[t],this))return!0});o=this.pushStack("","find",e);for(t=0,n=this.length;t<n;t++){r=o.length,v.find(e,this[t],o);if(t>0)for(i=r;i<o.length;i++)for(s=0;s<r;s++)if(o[s]===o[i]){o.splice(i--,1);break}}return o},has:function(e){var t,n=v(e,this),r=n.length;return this.filter(function(){for(t=0;t<r;t++)if(v.contains(this,n[t]))return!0})},not:function(e){return this.pushStack(ft(this,e,!1),"not",e)},filter:function(e){return this.pushStack(ft(this,e,!0),"filter",e)},is:function(e){return!!e&&(typeof e=="string"?st.test(e)?v(e,this.context).index(this[0])>=0:v.filter(e,this).length>0:this.filter(e).length>0)},closest:function(e,t){var n,r=0,i=this.length,s=[],o=st.test(e)||typeof e!="string"?v(e,t||this.context):0;for(;r<i;r++){n=this[r];while(n&&n.ownerDocument&&n!==t&&n.nodeType!==11){if(o?o.index(n)>-1:v.find.matchesSelector(n,e)){s.push(n);break}n=n.parentNode}}return s=s.length>1?v.unique(s):s,this.pushStack(s,"closest",e)},index:function(e){return e?typeof e=="string"?v.inArray(this[0],v(e)):v.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.prevAll().length:-1},add:function(e,t){var n=typeof e=="string"?v(e,t):v.makeArray(e&&e.nodeType?[e]:e),r=v.merge(this.get(),n);return this.pushStack(ut(n[0])||ut(r[0])?r:v.unique(r))},addBack:function(e){return this.add(e==null?this.prevObject:this.prevObject.filter(e))}}),v.fn.andSelf=v.fn.addBack,v.each({parent:function(e){var t=e.parentNode;return t&&t.nodeType!==11?t:null},parents:function(e){return v.dir(e,"parentNode")},parentsUntil:function(e,t,n){return v.dir(e,"parentNode",n)},next:function(e){return at(e,"nextSibling")},prev:function(e){return at(e,"previousSibling")},nextAll:function(e){return v.dir(e,"nextSibling")},prevAll:function(e){return v.dir(e,"previousSibling")},nextUntil:function(e,t,n){return v.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return v.dir(e,"previousSibling",n)},siblings:function(e){return v.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return v.sibling(e.firstChild)},contents:function(e){return v.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:v.merge([],e.childNodes)}},function(e,t){v.fn[e]=function(n,r){var i=v.map(this,t,n);return nt.test(e)||(r=n),r&&typeof r=="string"&&(i=v.filter(r,i)),i=this.length>1&&!ot[e]?v.unique(i):i,this.length>1&&rt.test(e)&&(i=i.reverse()),this.pushStack(i,e,l.call(arguments).join(","))}}),v.extend({filter:function(e,t,n){return n&&(e=":not("+e+")"),t.length===1?v.find.matchesSelector(t[0],e)?[t[0]]:[]:v.find.matches(e,t)},dir:function(e,n,r){var i=[],s=e[n];while(s&&s.nodeType!==9&&(r===t||s.nodeType!==1||!v(s).is(r)))s.nodeType===1&&i.push(s),s=s[n];return i},sibling:function(e,t){var n=[];for(;e;e=e.nextSibling)e.nodeType===1&&e!==t&&n.push(e);return n}});var ct="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",ht=/ jQuery\d+="(?:null|\d+)"/g,pt=/^\s+/,dt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,vt=/<([\w:]+)/,mt=/<tbody/i,gt=/<|&#?\w+;/,yt=/<(?:script|style|link)/i,bt=/<(?:script|object|embed|option|style)/i,wt=new RegExp("<(?:"+ct+")[\\s/>]","i"),Et=/^(?:checkbox|radio)$/,St=/checked\s*(?:[^=]|=\s*.checked.)/i,xt=/\/(java|ecma)script/i,Tt=/^\s*<!(?:\[CDATA\[|\-\-)|[\]\-]{2}>\s*$/g,Nt={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],area:[1,"<map>","</map>"],_default:[0,"",""]},Ct=lt(i),kt=Ct.appendChild(i.createElement("div"));Nt.optgroup=Nt.option,Nt.tbody=Nt.tfoot=Nt.colgroup=Nt.caption=Nt.thead,Nt.th=Nt.td,v.support.htmlSerialize||(Nt._default=[1,"X<div>","</div>"]),v.fn.extend({text:function(e){return v.access(this,function(e){return e===t?v.text(this):this.empty().append((this[0]&&this[0].ownerDocument||i).createTextNode(e))},null,e,arguments.length)},wrapAll:function(e){if(v.isFunction(e))return this.each(function(t){v(this).wrapAll(e.call(this,t))});if(this[0]){var t=v(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){var e=this;while(e.firstChild&&e.firstChild.nodeType===1)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return v.isFunction(e)?this.each(function(t){v(this).wrapInner(e.call(this,t))}):this.each(function(){var t=v(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=v.isFunction(e);return this.each(function(n){v(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){v.nodeName(this,"body")||v(this).replaceWith(this.childNodes)}).end()},append:function(){return this.domManip(arguments,!0,function(e){(this.nodeType===1||this.nodeType===11)&&this.appendChild(e)})},prepend:function(){return this.domManip(arguments,!0,function(e){(this.nodeType===1||this.nodeType===11)&&this.insertBefore(e,this.firstChild)})},before:function(){if(!ut(this[0]))return this.domManip(arguments,!1,function(e){this.parentNode.insertBefore(e,this)});if(arguments.length){var e=v.clean(arguments);return this.pushStack(v.merge(e,this),"before",this.selector)}},after:function(){if(!ut(this[0]))return this.domManip(arguments,!1,function(e){this.parentNode.insertBefore(e,this.nextSibling)});if(arguments.length){var e=v.clean(arguments);return this.pushStack(v.merge(this,e),"after",this.selector)}},remove:function(e,t){var n,r=0;for(;(n=this[r])!=null;r++)if(!e||v.filter(e,[n]).length)!t&&n.nodeType===1&&(v.cleanData(n.getElementsByTagName("*")),v.cleanData([n])),n.parentNode&&n.parentNode.removeChild(n);return this},empty:function(){var e,t=0;for(;(e=this[t])!=null;t++){e.nodeType===1&&v.cleanData(e.getElementsByTagName("*"));while(e.firstChild)e.removeChild(e.firstChild)}return this},clone:function(e,t){return e=e==null?!1:e,t=t==null?e:t,this.map(function(){return v.clone(this,e,t)})},html:function(e){return v.access(this,function(e){var n=this[0]||{},r=0,i=this.length;if(e===t)return n.nodeType===1?n.innerHTML.replace(ht,""):t;if(typeof e=="string"&&!yt.test(e)&&(v.support.htmlSerialize||!wt.test(e))&&(v.support.leadingWhitespace||!pt.test(e))&&!Nt[(vt.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(dt,"<$1></$2>");try{for(;r<i;r++)n=this[r]||{},n.nodeType===1&&(v.cleanData(n.getElementsByTagName("*")),n.innerHTML=e);n=0}catch(s){}}n&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(e){return ut(this[0])?this.length?this.pushStack(v(v.isFunction(e)?e():e),"replaceWith",e):this:v.isFunction(e)?this.each(function(t){var n=v(this),r=n.html();n.replaceWith(e.call(this,t,r))}):(typeof e!="string"&&(e=v(e).detach()),this.each(function(){var t=this.nextSibling,n=this.parentNode;v(this).remove(),t?v(t).before(e):v(n).append(e)}))},detach:function(e){return this.remove(e,!0)},domManip:function(e,n,r){e=[].concat.apply([],e);var i,s,o,u,a=0,f=e[0],l=[],c=this.length;if(!v.support.checkClone&&c>1&&typeof f=="string"&&St.test(f))return this.each(function(){v(this).domManip(e,n,r)});if(v.isFunction(f))return this.each(function(i){var s=v(this);e[0]=f.call(this,i,n?s.html():t),s.domManip(e,n,r)});if(this[0]){i=v.buildFragment(e,this,l),o=i.fragment,s=o.firstChild,o.childNodes.length===1&&(o=s);if(s){n=n&&v.nodeName(s,"tr");for(u=i.cacheable||c-1;a<c;a++)r.call(n&&v.nodeName(this[a],"table")?Lt(this[a],"tbody"):this[a],a===u?o:v.clone(o,!0,!0))}o=s=null,l.length&&v.each(l,function(e,t){t.src?v.ajax?v.ajax({url:t.src,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0}):v.error("no ajax"):v.globalEval((t.text||t.textContent||t.innerHTML||"").replace(Tt,"")),t.parentNode&&t.parentNode.removeChild(t)})}return this}}),v.buildFragment=function(e,n,r){var s,o,u,a=e[0];return n=n||i,n=!n.nodeType&&n[0]||n,n=n.ownerDocument||n,e.length===1&&typeof a=="string"&&a.length<512&&n===i&&a.charAt(0)==="<"&&!bt.test(a)&&(v.support.checkClone||!St.test(a))&&(v.support.html5Clone||!wt.test(a))&&(o=!0,s=v.fragments[a],u=s!==t),s||(s=n.createDocumentFragment(),v.clean(e,n,s,r),o&&(v.fragments[a]=u&&s)),{fragment:s,cacheable:o}},v.fragments={},v.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){v.fn[e]=function(n){var r,i=0,s=[],o=v(n),u=o.length,a=this.length===1&&this[0].parentNode;if((a==null||a&&a.nodeType===11&&a.childNodes.length===1)&&u===1)return o[t](this[0]),this;for(;i<u;i++)r=(i>0?this.clone(!0):this).get(),v(o[i])[t](r),s=s.concat(r);return this.pushStack(s,e,o.selector)}}),v.extend({clone:function(e,t,n){var r,i,s,o;v.support.html5Clone||v.isXMLDoc(e)||!wt.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(kt.innerHTML=e.outerHTML,kt.removeChild(o=kt.firstChild));if((!v.support.noCloneEvent||!v.support.noCloneChecked)&&(e.nodeType===1||e.nodeType===11)&&!v.isXMLDoc(e)){Ot(e,o),r=Mt(e),i=Mt(o);for(s=0;r[s];++s)i[s]&&Ot(r[s],i[s])}if(t){At(e,o);if(n){r=Mt(e),i=Mt(o);for(s=0;r[s];++s)At(r[s],i[s])}}return r=i=null,o},clean:function(e,t,n,r){var s,o,u,a,f,l,c,h,p,d,m,g,y=t===i&&Ct,b=[];if(!t||typeof t.createDocumentFragment=="undefined")t=i;for(s=0;(u=e[s])!=null;s++){typeof u=="number"&&(u+="");if(!u)continue;if(typeof u=="string")if(!gt.test(u))u=t.createTextNode(u);else{y=y||lt(t),c=t.createElement("div"),y.appendChild(c),u=u.replace(dt,"<$1></$2>"),a=(vt.exec(u)||["",""])[1].toLowerCase(),f=Nt[a]||Nt._default,l=f[0],c.innerHTML=f[1]+u+f[2];while(l--)c=c.lastChild;if(!v.support.tbody){h=mt.test(u),p=a==="table"&&!h?c.firstChild&&c.firstChild.childNodes:f[1]==="<table>"&&!h?c.childNodes:[];for(o=p.length-1;o>=0;--o)v.nodeName(p[o],"tbody")&&!p[o].childNodes.length&&p[o].parentNode.removeChild(p[o])}!v.support.leadingWhitespace&&pt.test(u)&&c.insertBefore(t.createTextNode(pt.exec(u)[0]),c.firstChild),u=c.childNodes,c.parentNode.removeChild(c)}u.nodeType?b.push(u):v.merge(b,u)}c&&(u=c=y=null);if(!v.support.appendChecked)for(s=0;(u=b[s])!=null;s++)v.nodeName(u,"input")?_t(u):typeof u.getElementsByTagName!="undefined"&&v.grep(u.getElementsByTagName("input"),_t);if(n){m=function(e){if(!e.type||xt.test(e.type))return r?r.push(e.parentNode?e.parentNode.removeChild(e):e):n.appendChild(e)};for(s=0;(u=b[s])!=null;s++)if(!v.nodeName(u,"script")||!m(u))n.appendChild(u),typeof u.getElementsByTagName!="undefined"&&(g=v.grep(v.merge([],u.getElementsByTagName("script")),m),b.splice.apply(b,[s+1,0].concat(g)),s+=g.length)}return b},cleanData:function(e,t){var n,r,i,s,o=0,u=v.expando,a=v.cache,f=v.support.deleteExpando,l=v.event.special;for(;(i=e[o])!=null;o++)if(t||v.acceptData(i)){r=i[u],n=r&&a[r];if(n){if(n.events)for(s in n.events)l[s]?v.event.remove(i,s):v.removeEvent(i,s,n.handle);a[r]&&(delete a[r],f?delete i[u]:i.removeAttribute?i.removeAttribute(u):i[u]=null,v.deletedIds.push(r))}}}}),function(){var e,t;v.uaMatch=function(e){e=e.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}},e=v.uaMatch(o.userAgent),t={},e.browser&&(t[e.browser]=!0,t.version=e.version),t.chrome?t.webkit=!0:t.webkit&&(t.safari=!0),v.browser=t,v.sub=function(){function e(t,n){return new e.fn.init(t,n)}v.extend(!0,e,this),e.superclass=this,e.fn=e.prototype=this(),e.fn.constructor=e,e.sub=this.sub,e.fn.init=function(r,i){return i&&i instanceof v&&!(i instanceof e)&&(i=e(i)),v.fn.init.call(this,r,i,t)},e.fn.init.prototype=e.fn;var t=e(i);return e}}();var Dt,Pt,Ht,Bt=/alpha\([^)]*\)/i,jt=/opacity=([^)]*)/,Ft=/^(top|right|bottom|left)$/,It=/^(none|table(?!-c[ea]).+)/,qt=/^margin/,Rt=new RegExp("^("+m+")(.*)$","i"),Ut=new RegExp("^("+m+")(?!px)[a-z%]+$","i"),zt=new RegExp("^([-+])=("+m+")","i"),Wt={BODY:"block"},Xt={position:"absolute",visibility:"hidden",display:"block"},Vt={letterSpacing:0,fontWeight:400},$t=["Top","Right","Bottom","Left"],Jt=["Webkit","O","Moz","ms"],Kt=v.fn.toggle;v.fn.extend({css:function(e,n){return v.access(this,function(e,n,r){return r!==t?v.style(e,n,r):v.css(e,n)},e,n,arguments.length>1)},show:function(){return Yt(this,!0)},hide:function(){return Yt(this)},toggle:function(e,t){var n=typeof e=="boolean";return v.isFunction(e)&&v.isFunction(t)?Kt.apply(this,arguments):this.each(function(){(n?e:Gt(this))?v(this).show():v(this).hide()})}}),v.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Dt(e,"opacity");return n===""?"1":n}}}},cssNumber:{fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":v.support.cssFloat?"cssFloat":"styleFloat"},style:function(e,n,r,i){if(!e||e.nodeType===3||e.nodeType===8||!e.style)return;var s,o,u,a=v.camelCase(n),f=e.style;n=v.cssProps[a]||(v.cssProps[a]=Qt(f,a)),u=v.cssHooks[n]||v.cssHooks[a];if(r===t)return u&&"get"in u&&(s=u.get(e,!1,i))!==t?s:f[n];o=typeof r,o==="string"&&(s=zt.exec(r))&&(r=(s[1]+1)*s[2]+parseFloat(v.css(e,n)),o="number");if(r==null||o==="number"&&isNaN(r))return;o==="number"&&!v.cssNumber[a]&&(r+="px");if(!u||!("set"in u)||(r=u.set(e,r,i))!==t)try{f[n]=r}catch(l){}},css:function(e,n,r,i){var s,o,u,a=v.camelCase(n);return n=v.cssProps[a]||(v.cssProps[a]=Qt(e.style,a)),u=v.cssHooks[n]||v.cssHooks[a],u&&"get"in u&&(s=u.get(e,!0,i)),s===t&&(s=Dt(e,n)),s==="normal"&&n in Vt&&(s=Vt[n]),r||i!==t?(o=parseFloat(s),r||v.isNumeric(o)?o||0:s):s},swap:function(e,t,n){var r,i,s={};for(i in t)s[i]=e.style[i],e.style[i]=t[i];r=n.call(e);for(i in t)e.style[i]=s[i];return r}}),e.getComputedStyle?Dt=function(t,n){var r,i,s,o,u=e.getComputedStyle(t,null),a=t.style;return u&&(r=u.getPropertyValue(n)||u[n],r===""&&!v.contains(t.ownerDocument,t)&&(r=v.style(t,n)),Ut.test(r)&&qt.test(n)&&(i=a.width,s=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=r,r=u.width,a.width=i,a.minWidth=s,a.maxWidth=o)),r}:i.documentElement.currentStyle&&(Dt=function(e,t){var n,r,i=e.currentStyle&&e.currentStyle[t],s=e.style;return i==null&&s&&s[t]&&(i=s[t]),Ut.test(i)&&!Ft.test(t)&&(n=s.left,r=e.runtimeStyle&&e.runtimeStyle.left,r&&(e.runtimeStyle.left=e.currentStyle.left),s.left=t==="fontSize"?"1em":i,i=s.pixelLeft+"px",s.left=n,r&&(e.runtimeStyle.left=r)),i===""?"auto":i}),v.each(["height","width"],function(e,t){v.cssHooks[t]={get:function(e,n,r){if(n)return e.offsetWidth===0&&It.test(Dt(e,"display"))?v.swap(e,Xt,function(){return tn(e,t,r)}):tn(e,t,r)},set:function(e,n,r){return Zt(e,n,r?en(e,t,r,v.support.boxSizing&&v.css(e,"boxSizing")==="border-box"):0)}}}),v.support.opacity||(v.cssHooks.opacity={get:function(e,t){return jt.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,r=e.currentStyle,i=v.isNumeric(t)?"alpha(opacity="+t*100+")":"",s=r&&r.filter||n.filter||"";n.zoom=1;if(t>=1&&v.trim(s.replace(Bt,""))===""&&n.removeAttribute){n.removeAttribute("filter");if(r&&!r.filter)return}n.filter=Bt.test(s)?s.replace(Bt,i):s+" "+i}}),v(function(){v.support.reliableMarginRight||(v.cssHooks.marginRight={get:function(e,t){return v.swap(e,{display:"inline-block"},function(){if(t)return Dt(e,"marginRight")})}}),!v.support.pixelPosition&&v.fn.position&&v.each(["top","left"],function(e,t){v.cssHooks[t]={get:function(e,n){if(n){var r=Dt(e,t);return Ut.test(r)?v(e).position()[t]+"px":r}}}})}),v.expr&&v.expr.filters&&(v.expr.filters.hidden=function(e){return e.offsetWidth===0&&e.offsetHeight===0||!v.support.reliableHiddenOffsets&&(e.style&&e.style.display||Dt(e,"display"))==="none"},v.expr.filters.visible=function(e){return!v.expr.filters.hidden(e)}),v.each({margin:"",padding:"",border:"Width"},function(e,t){v.cssHooks[e+t]={expand:function(n){var r,i=typeof n=="string"?n.split(" "):[n],s={};for(r=0;r<4;r++)s[e+$t[r]+t]=i[r]||i[r-2]||i[0];return s}},qt.test(e)||(v.cssHooks[e+t].set=Zt)});var rn=/%20/g,sn=/\[\]$/,on=/\r?\n/g,un=/^(?:color|date|datetime|datetime-local|email|hidden|month|number|password|range|search|tel|text|time|url|week)$/i,an=/^(?:select|textarea)/i;v.fn.extend({serialize:function(){return v.param(this.serializeArray())},serializeArray:function(){return this.map(function(){return this.elements?v.makeArray(this.elements):this}).filter(function(){return this.name&&!this.disabled&&(this.checked||an.test(this.nodeName)||un.test(this.type))}).map(function(e,t){var n=v(this).val();return n==null?null:v.isArray(n)?v.map(n,function(e,n){return{name:t.name,value:e.replace(on,"\r\n")}}):{name:t.name,value:n.replace(on,"\r\n")}}).get()}}),v.param=function(e,n){var r,i=[],s=function(e,t){t=v.isFunction(t)?t():t==null?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};n===t&&(n=v.ajaxSettings&&v.ajaxSettings.traditional);if(v.isArray(e)||e.jquery&&!v.isPlainObject(e))v.each(e,function(){s(this.name,this.value)});else for(r in e)fn(r,e[r],n,s);return i.join("&").replace(rn,"+")};var ln,cn,hn=/#.*$/,pn=/^(.*?):[ \t]*([^\r\n]*)\r?$/mg,dn=/^(?:about|app|app\-storage|.+\-extension|file|res|widget):$/,vn=/^(?:GET|HEAD)$/,mn=/^\/\//,gn=/\?/,yn=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,tbn=/([?&])_=[^&]*/,wn=/^([\w\+\.\-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,En=v.fn.load,Sn={},xn={},Tn=["*/"]+["*"];try{cn=s.href}catch(Nn){cn=i.createElement("a"),cn.href="",cn=cn.href}ln=wn.exec(cn.toLowerCase())||[],v.fn.load=function(e,n,r){if(typeof e!="string"&&En)return En.apply(this,arguments);if(!this.length)return this;var i,s,o,u=this,a=e.indexOf(" ");return a>=0&&(i=e.slice(a,e.length),e=e.slice(0,a)),v.isFunction(n)?(r=n,n=t):n&&typeof n=="object"&&(s="POST"),v.ajax({url:e,type:s,dataType:"html",data:n,complete:function(e,t){r&&u.each(r,o||[e.responseText,t,e])}}).done(function(e){o=arguments,u.html(i?v("<div>").append(e.replace(yn,"")).find(i):e)}),this},v.each("ajaxStart ajaxStop ajaxComplete ajaxError ajaxSuccess ajaxSend".split(" "),function(e,t){v.fn[t]=function(e){return this.on(t,e)}}),v.each(["get","post"],function(e,n){v[n]=function(e,r,i,s){return v.isFunction(r)&&(s=s||i,i=r,r=t),v.ajax({type:n,url:e,data:r,success:i,dataType:s})}}),v.extend({getScript:function(e,n){return v.get(e,t,n,"script")},getJSON:function(e,t,n){return v.get(e,t,n,"json")},ajaxSetup:function(e,t){return t?Ln(e,v.ajaxSettings):(t=e,e=v.ajaxSettings),Ln(e,t),e},ajaxSettings:{url:cn,isLocal:dn.test(ln[1]),global:!0,type:"GET",contentType:"application/x-www-form-urlencoded; charset=UTF-8",processData:!0,async:!0,accepts:{xml:"application/xml, text/xml",html:"text/html",text:"text/plain",json:"application/json, text/javascript","*":Tn},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText"},converters:{"* text":e.String,"text html":!0,"text json":v.parseJSON,"text xml":v.parseXML},flatOptions:{context:!0,url:!0}},ajaxPrefilter:Cn(Sn),ajaxTransport:Cn(xn),ajax:function(e,n){function T(e,n,s,a){var l,y,b,w,S,T=n;if(E===2)return;E=2,u&&clearTimeout(u),o=t,i=a||"",x.readyState=e>0?4:0,s&&(w=An(c,x,s));if(e>=200&&e<300||e===304)c.ifModified&&(S=x.getResponseHeader("Last-Modified"),S&&(v.lastModified[r]=S),S=x.getResponseHeader("Etag"),S&&(v.etag[r]=S)),e===304?(T="notmodified",l=!0):(l=On(c,w),T=l.state,y=l.data,b=l.error,l=!b);else{b=T;if(!T||e)T="error",e<0&&(e=0)}x.status=e,x.statusText=(n||T)+"",l?d.resolveWith(h,[y,T,x]):d.rejectWith(h,[x,T,b]),x.statusCode(g),g=t,f&&p.trigger("ajax"+(l?"Success":"Error"),[x,c,l?y:b]),m.fireWith(h,[x,T]),f&&(p.trigger("ajaxComplete",[x,c]),--v.active||v.event.trigger("ajaxStop"))}typeof e=="object"&&(n=e,e=t),n=n||{};var r,i,s,o,u,a,f,l,c=v.ajaxSetup({},n),h=c.context||c,p=h!==c&&(h.nodeType||h instanceof v)?v(h):v.event,d=v.Deferred(),m=v.Callbacks("once memory"),g=c.statusCode||{},b={},w={},E=0,S="canceled",x={readyState:0,setRequestHeader:function(e,t){if(!E){var n=e.toLowerCase();e=w[n]=w[n]||e,b[e]=t}return this},getAllResponseHeaders:function(){return E===2?i:null},getResponseHeader:function(e){var n;if(E===2){if(!s){s={};while(n=pn.exec(i))s[n[1].toLowerCase()]=n[2]}n=s[e.toLowerCase()]}return n===t?null:n},overrideMimeType:function(e){return E||(c.mimeType=e),this},abort:function(e){return e=e||S,o&&o.abort(e),T(0,e),this}};d.promise(x),x.success=x.done,x.error=x.fail,x.complete=m.add,x.statusCode=function(e){if(e){var t;if(E<2)for(t in e)g[t]=[g[t],e[t]];else t=e[x.status],x.always(t)}return this},c.url=((e||c.url)+"").replace(hn,"").replace(mn,ln[1]+"//"),c.dataTypes=v.trim(c.dataType||"*").toLowerCase().split(y),c.crossDomain==null&&(a=wn.exec(c.url.toLowerCase()),c.crossDomain=!(!a||a[1]===ln[1]&&a[2]===ln[2]&&(a[3]||(a[1]==="http:"?80:443))==(ln[3]||(ln[1]==="http:"?80:443)))),c.data&&c.processData&&typeof c.data!="string"&&(c.data=v.param(c.data,c.traditional)),kn(Sn,c,n,x);if(E===2)return x;f=c.global,c.type=c.type.toUpperCase(),c.hasContent=!vn.test(c.type),f&&v.active++===0&&v.event.trigger("ajaxStart");if(!c.hasContent){c.data&&(c.url+=(gn.test(c.url)?"&":"?")+c.data,delete c.data),r=c.url;if(c.cache===!1){var N=v.now(),C=c.url.replace(bn,"$1_="+N);c.url=C+(C===c.url?(gn.test(c.url)?"&":"?")+"_="+N:"")}}(c.data&&c.hasContent&&c.contentType!==!1||n.contentType)&&x.setRequestHeader("Content-Type",c.contentType),c.ifModified&&(r=r||c.url,v.lastModified[r]&&x.setRequestHeader("If-Modified-Since",v.lastModified[r]),v.etag[r]&&x.setRequestHeader("If-None-Match",v.etag[r])),x.setRequestHeader("Accept",c.dataTypes[0]&&c.accepts[c.dataTypes[0]]?c.accepts[c.dataTypes[0]]+(c.dataTypes[0]!=="*"?", "+Tn+"; q=0.01":""):c.accepts["*"]);for(l in c.headers)x.setRequestHeader(l,c.headers[l]);if(!c.beforeSend||c.beforeSend.call(h,x,c)!==!1&&E!==2){S="abort";for(l in{success:1,error:1,complete:1})x[l](c[l]);o=kn(xn,c,n,x);if(!o)T(-1,"No Transport");else{x.readyState=1,f&&p.trigger("ajaxSend",[x,c]),c.async&&c.timeout>0&&(u=setTimeout(function(){x.abort("timeout")},c.timeout));try{E=1,o.send(b,T)}catch(k){if(!(E<2))throw k;T(-1,k)}}return x}return x.abort()},active:0,lastModified:{},etag:{}});var Mn=[],_n=/\?/,Dn=/(=)\?(?=&|$)|\?\?/,Pn=v.now();v.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Mn.pop()||v.expando+"_"+Pn++;return this[e]=!0,e}}),v.ajaxPrefilter("json jsonp",function(n,r,i){var s,o,u,a=n.data,f=n.url,l=n.jsonp!==!1,c=l&&Dn.test(f),h=l&&!c&&typeof a=="string"&&!(n.contentType||"").indexOf("application/x-www-form-urlencoded")&&Dn.test(a);if(n.dataTypes[0]==="jsonp"||c||h)return s=n.jsonpCallback=v.isFunction(n.jsonpCallback)?n.jsonpCallback():n.jsonpCallback,o=e[s],c?n.url=f.replace(Dn,"$1"+s):h?n.data=a.replace(Dn,"$1"+s):l&&(n.url+=(_n.test(f)?"&":"?")+n.jsonp+"="+s),n.converters["script json"]=function(){return u||v.error(s+" was not called"),u[0]},n.dataTypes[0]="json",e[s]=function(){u=arguments},i.always(function(){e[s]=o,n[s]&&(n.jsonpCallback=r.jsonpCallback,Mn.push(s)),u&&v.isFunction(o)&&o(u[0]),u=o=t}),"script"}),v.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/javascript|ecmascript/},converters:{"text script":function(e){return v.globalEval(e),e}}}),v.ajaxPrefilter("script",function(e){e.cache===t&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),v.ajaxTransport("script",function(e){if(e.crossDomain){var n,r=i.head||i.getElementsByTagName("head")[0]||i.documentElement;return{send:function(s,o){n=i.createElement("script"),n.async="async",e.scriptCharset&&(n.charset=e.scriptCharset),n.src=e.url,n.onload=n.onreadystatechange=function(e,i){if(i||!n.readyState||/loaded|complete/.test(n.readyState))n.onload=n.onreadystatechange=null,r&&n.parentNode&&r.removeChild(n),n=t,i||o(200,"success")},r.insertBefore(n,r.firstChild)},abort:function(){n&&n.onload(0,1)}}}});var Hn,Bn=e.ActiveXObject?function(){for(var e in Hn)Hn[e](0,1)}:!1,jn=0;v.ajaxSettings.xhr=e.ActiveXObject?function(){return!this.isLocal&&Fn()||In()}:Fn,function(e){v.extend(v.support,{ajax:!!e,cors:!!e&&"withCredentials"in e})}(v.ajaxSettings.xhr()),v.support.ajax&&v.ajaxTransport(function(n){if(!n.crossDomain||v.support.cors){var r;return{send:function(i,s){var o,u,a=n.xhr();n.username?a.open(n.type,n.url,n.async,n.username,n.password):a.open(n.type,n.url,n.async);if(n.xhrFields)for(u in n.xhrFields)a[u]=n.xhrFields[u];n.mimeType&&a.overrideMimeType&&a.overrideMimeType(n.mimeType),!n.crossDomain&&!i["X-Requested-With"]&&(i["X-Requested-With"]="XMLHttpRequest");try{for(u in i)a.setRequestHeader(u,i[u])}catch(f){}a.send(n.hasContent&&n.data||null),r=function(e,i){var u,f,l,c,h;try{if(r&&(i||a.readyState===4)){r=t,o&&(a.onreadystatechange=v.noop,Bn&&delete Hn[o]);if(i)a.readyState!==4&&a.abort();else{u=a.status,l=a.getAllResponseHeaders(),c={},h=a.responseXML,h&&h.documentElement&&(c.xml=h);try{c.text=a.responseText}catch(p){}try{f=a.statusText}catch(p){f=""}!u&&n.isLocal&&!n.crossDomain?u=c.text?200:404:u===1223&&(u=204)}}}catch(d){i||s(-1,d)}c&&s(u,f,c,l)},n.async?a.readyState===4?setTimeout(r,0):(o=++jn,Bn&&(Hn||(Hn={},v(e).unload(Bn)),Hn[o]=r),a.onreadystatechange=r):r()},abort:function(){r&&r(0,1)}}}});var qn,Rn,Un=/^(?:toggle|show|hide)$/,zn=new RegExp("^(?:([-+])=|)("+m+")([a-z%]*)$","i"),Wn=/queueHooks$/,Xn=[Gn],Vn={"*":[function(e,t){var n,r,i=this.createTween(e,t),s=zn.exec(t),o=i.cur(),u=+o||0,a=1,f=20;if(s){n=+s[2],r=s[3]||(v.cssNumber[e]?"":"px");if(r!=="px"&&u){u=v.css(i.elem,e,!0)||n||1;do a=a||".5",u/=a,v.style(i.elem,e,u+r);while(a!==(a=i.cur()/o)&&a!==1&&--f)}i.unit=r,i.start=u,i.end=s[1]?u+(s[1]+1)*n:n}return i}]};v.Animation=v.extend(Kn,{tweener:function(e,t){v.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");var n,r=0,i=e.length;for(;r<i;r++)n=e[r],Vn[n]=Vn[n]||[],Vn[n].unshift(t)},prefilter:function(e,t){t?Xn.unshift(e):Xn.push(e)}}),v.Tween=Yn,Yn.prototype={constructor:Yn,init:function(e,t,n,r,i,s){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=s||(v.cssNumber[n]?"":"px")},cur:function(){var e=Yn.propHooks[this.prop];return e&&e.get?e.get(this):Yn.propHooks._default.get(this)},run:function(e){var t,n=Yn.propHooks[this.prop];return this.options.duration?this.pos=t=v.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Yn.propHooks._default.set(this),this}},Yn.prototype.init.prototype=Yn.prototype,Yn.propHooks={_default:{get:function(e){var t;return e.elem[e.prop]==null||!!e.elem.style&&e.elem.style[e.prop]!=null?(t=v.css(e.elem,e.prop,!1,""),!t||t==="auto"?0:t):e.elem[e.prop]},set:function(e){v.fx.step[e.prop]?v.fx.step[e.prop](e):e.elem.style&&(e.elem.style[v.cssProps[e.prop]]!=null||v.cssHooks[e.prop])?v.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},Yn.propHooks.scrollTop=Yn.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},v.each(["toggle","show","hide"],function(e,t){var n=v.fn[t];v.fn[t]=function(r,i,s){return r==null||typeof r=="boolean"||!e&&v.isFunction(r)&&v.isFunction(i)?n.apply(this,arguments):this.animate(Zn(t,!0),r,i,s)}}),v.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Gt).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=v.isEmptyObject(e),s=v.speed(t,n,r),o=function(){var t=Kn(this,v.extend({},e),s);i&&t.stop(!0)};return i||s.queue===!1?this.each(o):this.queue(s.queue,o)},stop:function(e,n,r){var i=function(e){var t=e.stop;delete e.stop,t(r)};return typeof e!="string"&&(r=n,n=e,e=t),n&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,n=e!=null&&e+"queueHooks",s=v.timers,o=v._data(this);if(n)o[n]&&o[n].stop&&i(o[n]);else for(n in o)o[n]&&o[n].stop&&Wn.test(n)&&i(o[n]);for(n=s.length;n--;)s[n].elem===this&&(e==null||s[n].queue===e)&&(s[n].anim.stop(r),t=!1,s.splice(n,1));(t||!r)&&v.dequeue(this,e)})}}),v.each({slideDown:Zn("show"),slideUp:Zn("hide"),slideToggle:Zn("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){v.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),v.speed=function(e,t,n){var r=e&&typeof e=="object"?v.extend({},e):{complete:n||!n&&t||v.isFunction(e)&&e,duration:e,easing:n&&t||t&&!v.isFunction(t)&&t};r.duration=v.fx.off?0:typeof r.duration=="number"?r.duration:r.duration in v.fx.speeds?v.fx.speeds[r.duration]:v.fx.speeds._default;if(r.queue==null||r.queue===!0)r.queue="fx";return r.old=r.complete,r.complete=function(){v.isFunction(r.old)&&r.old.call(this),r.queue&&v.dequeue(this,r.queue)},r},v.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},v.timers=[],v.fx=Yn.prototype.init,v.fx.tick=function(){var e,n=v.timers,r=0;qn=v.now();for(;r<n.length;r++)e=n[r],!e()&&n[r]===e&&n.splice(r--,1);n.length||v.fx.stop(),qn=t},v.fx.timer=function(e){e()&&v.timers.push(e)&&!Rn&&(Rn=setInterval(v.fx.tick,v.fx.interval))},v.fx.interval=13,v.fx.stop=function(){clearInterval(Rn),Rn=null},v.fx.speeds={slow:600,fast:200,_default:400},v.fx.step={},v.expr&&v.expr.filters&&(v.expr.filters.animated=function(e){return v.grep(v.timers,function(t){return e===t.elem}).length});var er=/^(?:body|html)$/i;v.fn.offset=function(e){if(arguments.length)return e===t?this:this.each(function(t){v.offset.setOffset(this,e,t)});var n,r,i,s,o,u,a,f={top:0,left:0},l=this[0],c=l&&l.ownerDocument;if(!c)return;return(r=c.body)===l?v.offset.bodyOffset(l):(n=c.documentElement,v.contains(n,l)?(typeof l.getBoundingClientRect!="undefined"&&(f=l.getBoundingClientRect()),i=tr(c),s=n.clientTop||r.clientTop||0,o=n.clientLeft||r.clientLeft||0,u=i.pageYOffset||n.scrollTop,a=i.pageXOffset||n.scrollLeft,{top:f.top+u-s,left:f.left+a-o}):f)},v.offset={bodyOffset:function(e){var t=e.offsetTop,n=e.offsetLeft;return v.support.doesNotIncludeMarginInBodyOffset&&(t+=parseFloat(v.css(e,"marginTop"))||0,n+=parseFloat(v.css(e,"marginLeft"))||0),{top:t,left:n}},setOffset:function(e,t,n){var r=v.css(e,"position");r==="static"&&(e.style.position="relative");var i=v(e),s=i.offset(),o=v.css(e,"top"),u=v.css(e,"left"),a=(r==="absolute"||r==="fixed")&&v.inArray("auto",[o,u])>-1,f={},l={},c,h;a?(l=i.position(),c=l.top,h=l.left):(c=parseFloat(o)||0,h=parseFloat(u)||0),v.isFunction(t)&&(t=t.call(e,n,s)),t.top!=null&&(f.top=t.top-s.top+c),t.left!=null&&(f.left=t.left-s.left+h),"using"in t?t.using.call(e,f):i.css(f)}},v.fn.extend({position:function(){if(!this[0])return;var e=this[0],t=this.offsetParent(),n=this.offset(),r=er.test(t[0].nodeName)?{top:0,left:0}:t.offset();return n.top-=parseFloat(v.css(e,"marginTop"))||0,n.left-=parseFloat(v.css(e,"marginLeft"))||0,r.top+=parseFloat(v.css(t[0],"borderTopWidth"))||0,r.left+=parseFloat(v.css(t[0],"borderLeftWidth"))||0,{top:n.top-r.top,left:n.left-r.left}},offsetParent:function(){return this.map(function(){var e=this.offsetParent||i.body;while(e&&!er.test(e.nodeName)&&v.css(e,"position")==="static")e=e.offsetParent;return e||i.body})}}),v.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,n){var r=/Y/.test(n);v.fn[e]=function(i){return v.access(this,function(e,i,s){var o=tr(e);if(s===t)return o?n in o?o[n]:o.document.documentElement[i]:e[i];o?o.scrollTo(r?v(o).scrollLeft():s,r?s:v(o).scrollTop()):e[i]=s},e,i,arguments.length,null)}}),v.each({Height:"height",Width:"width"},function(e,n){v.each({padding:"inner"+e,content:n,"":"outer"+e},function(r,i){v.fn[i]=function(i,s){var o=arguments.length&&(r||typeof i!="boolean"),u=r||(i===!0||s===!0?"margin":"border");return v.access(this,function(n,r,i){var s;return v.isWindow(n)?n.document.documentElement["client"+e]:n.nodeType===9?(s=n.documentElement,Math.max(n.body["scroll"+e],s["scroll"+e],n.body["offset"+e],s["offset"+e],s["client"+e])):i===t?v.css(n,r,i,u):v.style(n,r,i,u)},n,o?i:t,o,null)}})}),e.jQuery=e.$=v,typeof define=="function"&&define.amd&&define.amd.jQuery&&define("jquery",[],function(){return v})})(window);
    </script>
    <script type="text/javascript"   charset="UTF-8">

        (function() {

        var $ = jQuery,
          undefined,
          _, // temp variable of prototypes
          mqCmdId = 'mathquill-command-id',
          mqBlockId = 'mathquill-block-id',
          min = Math.min,
          max = Math.max;

        var __slice = [].slice;

        function noop() {}

        /**
         * sugar to make defining lots of commands easier. TODO: rethink this.
         */
        function bind(cons /* , args... */) {
          var args = __slice.call(arguments, 1);
          return function() {
            return cons.apply(this, args);
          };
        }

        /**
         * a development-only debug method. This definition and all calls to `pray` will
         * be stripped from the minified build of mathquill.
         *
         * This function must be called by name to be removed at compile time. Do not
         * define another function with the same name, and only call this function by
         * name.
         */
        function pray(message, cond) {
          if (!cond) throw new Error('prayer failed: '+message);
        }
        var P = (function(prototype, ownProperty, undefined) {
          // helper functions that also help minification
          function isObject(o) { return typeof o === 'object'; }
          function isFunction(f) { return typeof f === 'function'; }

          function P(_superclass /* = Object */, definition) {
            // handle the case where no superclass is given
            if (definition === undefined) {
              definition = _superclass;
              _superclass = Object;
            }

            // C is the class to be returned.
            // There are three ways C will be called:
            //
            // 1) We call `new C` to create a new uninitialized object.
            // The behavior is similar to Object.create, where the prototype
            // relationship is set up, but the ::init method is not run.
            // Note that in this case we have `this instanceof C`, so we don't
            // spring the first trap. Also, `args` is undefined, so the initializer
            // doesn't get run.
            //
            // 2) A user will simply call C(a, b, c, ...) to create a new object with
            // initialization. This allows the user to create objects without `new`,
            // and in particular to initialize objects with variable arguments, which
            // is impossible with the `new` keyword. Note that in this case,
            // !(this instanceof C) springs the return trap at the beginning, and
            // C is called with the `new` keyword and one argument, which is the
            // Arguments object passed in.
            //
            // 3) For internal use only, if new C(args) is called, where args is an
            // Arguments object. In this case, the presence of `new` means the
            // return trap is not sprung, but the initializer is called if present.
            //
            // You can also call `new C([a, b, c])`, which is equivalent to `C(a, b,
            // c)`.
            //
            // TODO: the Chrome inspector shows all created objects as `C` rather than
            // `Object`.
            // Setting the .name property seems to have no effect. Is there a way to
            // override
            // this behavior?
            function C(args) {
              var self = this;
              if (!(self instanceof C)) return new C(arguments);
              if (args && isFunction(self.init)) self.init.apply(self, args);
            }

            // set up the prototype of the new class
            // note that this resolves to `new Object`
            // if the superclass isn't given
            var proto = C[prototype] = new _superclass();
            var _super = _superclass[prototype];
            var extensions;

            var mixin = C.mixin = function(def) {
              extensions = {};

              if (isFunction(def)) {
                // call the defining function with all the arguments you need
                // extensions captures the return value.
                extensions = def.call(C, proto, _super, C, _superclass);
              }
              else if (isObject(def)) {
                // if you passed an object instead, we'll take it
                extensions = def;
              }

              // ...and extend it
              if (isObject(extensions)) {
                for (var ext in extensions) {
                  if (ownProperty.call(extensions, ext)) {
                    proto[ext] = extensions[ext];
                  }
                }
              }

              // if there's no init, we assume we're inheriting a non-pjs class, so
              // we default to applying the superclass's constructor.
              if (!isFunction(proto.init)) {
                proto.init = function() { _superclass.apply(this, arguments); };
              }

              return C;
            };

            // set the constructor property, for convenience
            proto.constructor = C;

            return mixin(definition);
          }

          // ship it
          return P;

          // as a minifier optimization, we've closured in a few helper functions
          // and the string 'prototype' (C[p] is much shorter than C.prototype)
        })('prototype', ({}).hasOwnProperty);
        /*******************************************************************************
         * Textarea Manager
         *
         * An abstraction layer wrapping the textarea in an object with methods to
         * manipulate and listen to events on, that hides all the nasty cross- browser
         * incompatibilities behind a uniform API.
         *
         * Design goal: This is a *HARD* internal abstraction barrier. Cross-browser
         * inconsistencies are not allowed to leak through and be dealt with by event
         * handlers. All future cross-browser issues that arise must be dealt with here,
         * and if necessary, the API updated.
         *
         * Organization: - key values map and stringify() - manageTextarea() + defer()
         * and flush() + event handler logic + attach event handlers and export methods
         ******************************************************************************/

        var manageTextarea = (function() {
          // The following [key values][1] map was compiled from the
          // [DOM3 Events appendix section on key codes][2] and
          // [a widely cited report on cross-browser tests of key codes][3],
          // except for 10: 'Enter', which I've empirically observed in Safari on iOS
          // and doesn't appear to conflict with any other known key codes.
          //
          // [1]:
            // http://www.w3.org/TR/2012/WD-DOM-Level-3-Events-20120614/#keys-keyvalues
          // [2]:
            // http://www.w3.org/TR/2012/WD-DOM-Level-3-Events-20120614/#fixed-virtual-key-codes
          // [3]: http://unixpapa.com/js/key.html
          var KEY_VALUES = {
            8: 'Backspace',
            9: 'Tab',

            10: 'Enter', // for Safari on iOS

            13: 'Enter',

            16: 'Shift',
            17: 'Control',
            18: 'Alt',
            20: 'CapsLock',

            27: 'Esc',

            32: 'Spacebar',

            33: 'PageUp',
            34: 'PageDown',
            35: 'End',
            36: 'Home',

            37: 'Left',
            38: 'Up',
            39: 'Right',
            40: 'Down',

            45: 'Insert',

            46: 'Del',

            144: 'NumLock'
          };

          // To the extent possible, create a normalized string representation
          // of the key combo (i.e., key code and modifier keys).
          function stringify(evt) {
            var which = evt.which || evt.keyCode;
            var keyVal = KEY_VALUES[which];
            var key;
            var modifiers = [];

            if (evt.ctrlKey) modifiers.push('Ctrl');
            if (evt.originalEvent && evt.originalEvent.metaKey) modifiers.push('Meta');
            if (evt.altKey) modifiers.push('Alt');
            if (evt.shiftKey) modifiers.push('Shift');

            key = keyVal || String.fromCharCode(which);

            if (!modifiers.length && !keyVal) return key;

            modifiers.push(key);
            return modifiers.join('-');
          }

          // create a textarea manager that calls callbacks at useful times
          // and exports useful public methods
          return function manageTextarea(el, opts) {
            var keydown = null;
            var keypress = null;

            if (!opts) opts = {};
            var textCallback = opts.text || noop;
            var keyCallback = opts.key || noop;
            var pasteCallback = opts.paste || noop;
            var onCut = opts.cut || noop;

            var textarea = $(el);
            var target = $(opts.container || textarea);

            // checkTextareaFor() is called after keypress or paste events to
            // say "Hey, I think something was just typed" or "pasted" (resp.),
            // so that at all subsequent opportune times (next event or timeout),
            // will check for expected typed or pasted text.
            // Need to check repeatedly because #135: in Safari 5.1 (at least),
            // after selecting something and then typing, the textarea is
            // incorrectly reported as selected during the input event (but not
            // subsequently).
            var checkTextarea = noop;
            function checkTextareaFor(checker) {
              checkTextarea = checker;
              setTimeout(checker);
            }
            target.bind('keydown keypress input keyup focusout paste', function() { checkTextarea(); });


            // -*- public methods -*- //
            function select(text) {
              checkTextarea();

              textarea.val(text);
              if (text) textarea[0].select();
            }

            // -*- helper subroutines -*- //

            // Determine whether there's a selection in the textarea.
            // This will always return false in IE < 9, which don't support
            // HTMLTextareaElement::selection{Start,End}.
            function hasSelection() {
              var dom = textarea[0];

              if (!('selectionStart' in dom)) return false;
              return dom.selectionStart !== dom.selectionEnd;
            }

            function popText(callback) {
              var text = textarea.val();
              textarea.val('');
              if (text) callback(text);
            }

            function handleKey() {
              keyCallback(stringify(keydown), keydown);
            }

            // -*- event handlers -*- //
            function onKeydown(e) {
              keydown = e;
              keypress = null;

              handleKey();
            }

            function onKeypress(e) {
              // call the key handler for repeated keypresses.
              // This excludes keypresses that happen directly
              // after keydown. In that case, there will be
              // no previous keypress, so we skip it here
              if (keydown && keypress) handleKey();

              keypress = e;

              checkTextareaFor(typedText);
            }
            function typedText() {
              // If there is a selection, the contents of the textarea couldn't
              // possibly have just been typed in.
              // This happens in browsers like Firefox and Opera that fire
              // keypress for keystrokes that are not text entry and leave the
              // selection in the textarea alone, such as Ctrl-C.
              // Note: we assume that browsers that don't support hasSelection()
              // also never fire keypress on keystrokes that are not text entry.
              // This seems reasonably safe because:
              // - all modern browsers including IE 9+ support hasSelection(),
              // making it extremely unlikely any browser besides IE < 9 won't
              // - as far as we know IE < 9 never fires keypress on keystrokes
              // that aren't text entry, which is only as reliable as our
              // tests are comprehensive, but the IE < 9 way to do
              // hasSelection() is poorly documented and is also only as
              // reliable as our tests are comprehensive
              // If anything like #40 or #71 is reported in IE < 9, see
              // b1318e5349160b665003e36d4eedd64101ceacd8
              if (hasSelection()) return;

              popText(textCallback);
            }

            function onBlur() { keydown = keypress = null; }

            function onPaste(e) {
              // browsers are dumb.
              //
              // In Linux, middle-click pasting causes onPaste to be called,
              // when the textarea is not necessarily focused. We focus it
              // here to ensure that the pasted text actually ends up in the
              // textarea.
              //
              // It's pretty nifty that by changing focus in this handler,
              // we can change the target of the default action. (This works
              // on keydown too, FWIW).
              //
              // And by nifty, we mean dumb (but useful sometimes).
              textarea.focus();

              checkTextareaFor(pastedText);
            }
            function pastedText() {
              popText(pasteCallback);
            }

            // -*- attach event handlers -*- //
            target.bind({
              keydown: onKeydown,
              keypress: onKeypress,
              focusout: onBlur,
              cut: onCut,
              paste: onPaste
            });

            // -*- export public methods -*- //
            return {
              select: select
            };
          };
        }());
        var Parser = P(function(_, _super, Parser) {
          // The Parser object is a wrapper for a parser function.
          // Externally, you use one to parse a string by calling
          // var result = SomeParser.parse('Me Me Me! Parse Me!');
          // You should never call the constructor, rather you should
          // construct your Parser from the base parsers and the
          // parser combinator methods.

          function parseError(stream, message) {
            if (stream) {
              stream = "'"+stream+"'";
            }
            else {
              stream = 'EOF';
            }

            throw 'Parse Error: '+message+' at '+stream;
          }

          _.init = function(body) { this._ = body; };

          _.parse = function(stream) {
            return this.skip(eof)._(stream, success, parseError);

            function success(stream, result) { return result; }
          };

          // -*- primitive combinators -*- //
          _.or = function(alternative) {
            pray('or is passed a parser', alternative instanceof Parser);

            var self = this;

            return Parser(function(stream, onSuccess, onFailure) {
              return self._(stream, onSuccess, failure);

              function failure(newStream) {
                return alternative._(stream, onSuccess, onFailure);
              }
            });
          };

          _.then = function(next) {
            var self = this;

            return Parser(function(stream, onSuccess, onFailure) {
              return self._(stream, success, onFailure);

              function success(newStream, result) {
                var nextParser = (next instanceof Parser ? next : next(result));
                pray('a parser is returned', nextParser instanceof Parser);
                return nextParser._(newStream, onSuccess, onFailure);
              }
            });
          };

          // -*- optimized iterative combinators -*- //
          _.many = function() {
            var self = this;

            return Parser(function(stream, onSuccess, onFailure) {
              var xs = [];
              while (self._(stream, success, failure));
              return onSuccess(stream, xs);

              function success(newStream, x) {
                stream = newStream;
                xs.push(x);
                return true;
              }

              function failure() {
                return false;
              }
            });
          };

          _.times = function(min, max) {
            if (arguments.length < 2) max = min;
            var self = this;

            return Parser(function(stream, onSuccess, onFailure) {
              var xs = [];
              var result = true;
              var failure;

              for (var i = 0; i < min; i += 1) {
                result = self._(stream, success, firstFailure);
                if (!result) return onFailure(stream, failure);
              }

              for (; i < max && result; i += 1) {
                result = self._(stream, success, secondFailure);
              }

              return onSuccess(stream, xs);

              function success(newStream, x) {
                xs.push(x);
                stream = newStream;
                return true;
              }

              function firstFailure(newStream, msg) {
                failure = msg;
                stream = newStream;
                return false;
              }

              function secondFailure(newStream, msg) {
                return false;
              }
            });
          };

          // -*- higher-level combinators -*- //
          _.result = function(res) { return this.then(succeed(res)); };
          _.atMost = function(n) { return this.times(0, n); };
          _.atLeast = function(n) {
            var self = this;
            return self.times(n).then(function(start) {
              return self.many().map(function(end) {
                return start.concat(end);
              });
            });
          };

          _.map = function(fn) {
            return this.then(function(result) { return succeed(fn(result)); });
          };

          _.skip = function(two) {
            return this.then(function(result) { return two.result(result); });
          };

          // -*- primitive parsers -*- //
          var string = this.string = function(str) {
            var len = str.length;
            var expected = "expected '"+str+"'";

            return Parser(function(stream, onSuccess, onFailure) {
              var head = stream.slice(0, len);

              if (head === str) {
                return onSuccess(stream.slice(len), head);
              }
              else {
                return onFailure(stream, expected);
              }
            });
          };

          var regex = this.regex = function(re) {
            pray('regexp parser is anchored', re.toString().charAt(1) === '^');

            var expected = 'expected '+re;

            return Parser(function(stream, onSuccess, onFailure) {
              var match = re.exec(stream);

              if (match) {
                var result = match[0];
                return onSuccess(stream.slice(result.length), result);
              }
              else {
                return onFailure(stream, expected);
              }
            });
          };

          var succeed = Parser.succeed = function(result) {
            return Parser(function(stream, onSuccess) {
              return onSuccess(stream, result);
            });
          };

          var fail = Parser.fail = function(msg) {
            return Parser(function(stream, _, onFailure) {
              return onFailure(stream, msg);
            });
          };

          var letter = Parser.letter = regex(/^[a-z]/i);
          var letters = Parser.letters = regex(/^[a-z]*/i);
          var digit = Parser.digit = regex(/^[0-9]/);
          var digits = Parser.digits = regex(/^[0-9]*/);
          var whitespace = Parser.whitespace = regex(/^\s+/);
          var optWhitespace = Parser.optWhitespace = regex(/^\s*/);

          var any = Parser.any = Parser(function(stream, onSuccess, onFailure) {
            if (!stream) return onFailure(stream, 'expected any character');

            return onSuccess(stream.slice(1), stream.charAt(0));
          });

          var all = Parser.all = Parser(function(stream, onSuccess, onFailure) {
            return onSuccess('', stream);
          });

          var eof = Parser.eof = Parser(function(stream, onSuccess, onFailure) {
            if (stream) return onFailure(stream, 'expected EOF');

            return onSuccess(stream, stream);
          });
        });
        /*******************************************************************************
         * Base classes of the MathQuill virtual DOM tree
         *
         * Only doing tree node manipulation via these adopt/ disown methods guarantees
         * well-formedness of the tree.
         ******************************************************************************/

        /**
         * MathQuill virtual-DOM tree-node abstract base class
         */
        var Node = P(function(_) {
          _.prev = 0;
          _.next = 0;
          _.parent = 0;
          _.firstChild = 0;
          _.lastChild = 0;

          _.children = function() {
            return Fragment(this.firstChild, this.lastChild);
          };

          _.eachChild = function(fn) {
            return this.children().each(fn);
          };

          _.foldChildren = function(fold, fn) {
            return this.children().fold(fold, fn);
          };

          _.adopt = function(parent, prev, next) {
            Fragment(this, this).adopt(parent, prev, next);
            return this;
          };

          _.disown = function() {
            Fragment(this, this).disown();
            return this;
          };
        });

        /**
         * An entity outside the virtual tree with one-way pointers (so it's only a
         * "view" of part of the tree, not an actual node/entity in the tree) that
         * delimits a doubly-linked list of sibling nodes. It's like a fanfic love-child
         * between HTML DOM DocumentFragment and the Range classes: like
         * DocumentFragment, its contents must be sibling nodes (unlike Range, whose
         * contents are arbitrary contiguous pieces of subtrees), but like Range, it has
         * only one-way pointers to its contents, its contents have no reference to it
         * and in fact may still be in the visible tree (unlike DocumentFragment, whose
         * contents must be detached from the visible tree and have their 'parent'
         * pointers set to the DocumentFragment).
         */
        var Fragment = P(function(_) {
          _.first = 0;
          _.last = 0;

          _.init = function(first, last) {
            pray('no half-empty fragments', !first === !last);

            if (!first) return;

            pray('first node is passed to Fragment', first instanceof Node);
            pray('last node is passed to Fragment', last instanceof Node);
            pray('first and last have the same parent',
                 first.parent === last.parent);

            this.first = first;
            this.last = last;
          };

          function prayWellFormed(parent, prev, next) {
            pray('a parent is always present', parent);
            pray('prev is properly set up', (function() {
              // either it's empty and next is the first child (possibly empty)
              if (!prev) return parent.firstChild === next;

              // or it's there and its next and parent are properly set up
              return prev.next === next && prev.parent === parent;
            })());

            pray('next is properly set up', (function() {
              // either it's empty and prev is the last child (possibly empty)
              if (!next) return parent.lastChild === prev;

              // or it's there and its next and parent are properly set up
              return next.prev === prev && next.parent === parent;
            })());
          }

          _.adopt = function(parent, prev, next) {
            prayWellFormed(parent, prev, next);

            var self = this;
            self.disowned = false;

            var first = self.first;
            if (!first) return this;

            var last = self.last;

            if (prev) {
              // NB: this is handled in the ::each() block
              // prev.next = first
            } else {
              parent.firstChild = first;
            }

            if (next) {
              next.prev = last;
            } else {
              parent.lastChild = last;
            }

            self.last.next = next;

            self.each(function(el) {
              el.prev = prev;
              el.parent = parent;
              if (prev) prev.next = el;

              prev = el;
            });

            return self;
          };

          _.disown = function() {
            var self = this;
            var first = self.first;

            // guard for empty and already-disowned fragments
            if (!first || self.disowned) return self;

            self.disowned = true;

            var last = self.last;
            var parent = first.parent;

            prayWellFormed(parent, first.prev, first);
            prayWellFormed(parent, last, last.next);

            if (first.prev) {
              first.prev.next = last.next;
            } else {
              parent.firstChild = last.next;
            }

            if (last.next) {
              last.next.prev = first.prev;
            } else {
              parent.lastChild = first.prev;
            }

            return self;
          };

          _.each = function(fn) {
            var self = this;
            var el = self.first;
            if (!el) return self;

            for (;el !== self.last.next; el = el.next) {
              if (fn.call(self, el) === false) break;
            }

            return self;
          };

          _.fold = function(fold, fn) {
            this.each(function(el) {
              fold = fn.call(this, fold, el);
            });

            return fold;
          };
        });
        /*******************************************************************************
         * Abstract classes of math blocks and commands.
         ******************************************************************************/

        var uuid = (function() {
          var id = 0;

          return function() { return id += 1; };
        })();

        /**
         * Math tree node base class. Some math-tree-specific extensions to Node. Both
         * MathBlock's and MathCommand's descend from it.
         */
        var MathElement = P(Node, function(_) {
          _.init = function(obj) {
            this.id = uuid();
            MathElement[this.id] = this;
          };

          _.toString = function() {
            return '[MathElement '+this.id+']';
          };

          _.bubble = function(event /* , args... */) {
            var args = __slice.call(arguments, 1);

            for (var ancestor = this; ancestor; ancestor = ancestor.parent) {
              var res = ancestor[event] && ancestor[event].apply(ancestor, args);
              if (res === false) break;
            }

            return this;
          };

          _.postOrder = function(fn /* , args... */) {
            var args = __slice.call(arguments, 1);

            if (typeof fn === 'string') {
              var methodName = fn;
              fn = function(el) {
                if (methodName in el) el[methodName].apply(el, arguments);
              };
            }

            (function recurse(desc) {
              desc.eachChild(recurse);
              fn(desc);
            })(this);
          };

          _.jQ = $();
          _.jQadd = function(jQ) { this.jQ = this.jQ.add(jQ); };

          this.jQize = function(html) {
            // Sets the .jQ of the entire math subtree rooted at this command.
            // Expects .createBlocks() to have been called already, since it
            // calls .html().
            var jQ = $(html);
            jQ.find('*').andSelf().each(function() {
              var jQ = $(this),
                cmdId = jQ.attr('mathquill-command-id'),
                blockId = jQ.attr('mathquill-block-id');
              if (cmdId) MathElement[cmdId].jQadd(jQ);
              if (blockId) MathElement[blockId].jQadd(jQ);
            });
            return jQ;
          };

          _.finalizeInsert = function() {
            var self = this;
            self.postOrder('finalizeTree');

            // note: this order is important.
            // empty elements need the empty box provided by blur to
            // be present in order for their dimensions to be measured
            // correctly in redraw.
            self.postOrder('blur');

            // adjust context-sensitive spacing
            self.postOrder('respace');
            if (self.next.respace) self.next.respace();
            if (self.prev.respace) self.prev.respace();

            self.postOrder('redraw');
            self.bubble('redraw');
          };
        });

        /**
         * Commands and operators, like subscripts, exponents, or fractions. Descendant
         * commands are organized into blocks.
         */
        var MathCommand = P(MathElement, function(_, _super) {
          _.init = function(ctrlSeq, htmlTemplate, textTemplate) {
            var cmd = this;
            _super.init.call(cmd);

            if (!cmd.ctrlSeq) cmd.ctrlSeq = ctrlSeq;
            if (htmlTemplate) cmd.htmlTemplate = htmlTemplate;
            if (textTemplate) cmd.textTemplate = textTemplate;
          };

          // obvious methods
          _.replaces = function(replacedFragment) {
            replacedFragment.disown();
            this.replacedFragment = replacedFragment;
          };
          _.isEmpty = function() {
            return this.foldChildren(true, function(isEmpty, child) {
              return isEmpty && child.isEmpty();
            });
          };

          _.parser = function() {
            var block = latexMathParser.block;
            var self = this;

            return block.times(self.numBlocks()).map(function(blocks) {
              self.blocks = blocks;

              for (var i = 0; i < blocks.length; i += 1) {
                blocks[i].adopt(self, self.lastChild, 0);
              }

              return self;
            });
          };

          // createBefore(cursor) and the methods it calls
          _.createBefore = function(cursor) {
            var cmd = this;
            var replacedFragment = cmd.replacedFragment;

            cmd.createBlocks();
            MathElement.jQize(cmd.html());
            if (replacedFragment) {
              replacedFragment.adopt(cmd.firstChild, 0, 0);
              replacedFragment.jQ.appendTo(cmd.firstChild.jQ);
            }

            cursor.jQ.before(cmd.jQ);
            cursor.prev = cmd.adopt(cursor.parent, cursor.prev, cursor.next);

            cmd.finalizeInsert(cursor);

            cmd.placeCursor(cursor);
          };
          _.createBlocks = function() {
            var cmd = this,
              numBlocks = cmd.numBlocks(),
              blocks = cmd.blocks = Array(numBlocks);

            for (var i = 0; i < numBlocks; i += 1) {
              var newBlock = blocks[i] = MathBlock();
              newBlock.adopt(cmd, cmd.lastChild, 0);
            }
          };
          _.respace = noop; // placeholder for context-sensitive spacing
          _.placeCursor = function(cursor) {
            // append the cursor to the first empty child, or if none empty, the last
            // one
            cursor.appendTo(this.foldChildren(this.firstChild, function(prev, child) {
              return prev.isEmpty() ? prev : child;
            }));
          };

          // remove()
          _.remove = function() {
            this.disown()
            this.jQ.remove();

            this.postOrder(function(el) { delete MathElement[el.id]; });

            return this;
          };

          // methods involved in creating and cross-linking with HTML DOM nodes
          /*
             * They all expect an .htmlTemplate like '<span>&0</span>' or '<span><span>&0</span><span>&1</span></span>'
             *
             * See html.test.js for more examples.
             *
             * Requirements: - For each block of the command, there must be exactly one
             * "block content marker" of the form '&<number>' where <number> is the
             * 0-based index of the block. (Like the LaTeX \newcommand syntax, but with
             * a 0-based rather than 1-based index, because JavaScript because C because
             * Dijkstra.) - The block content marker must be the sole contents of the
             * containing element, there can't even be surrounding whitespace, or else
             * we can't guarantee sticking to within the bounds of the block content
             * marker when mucking with the HTML DOM. - The HTML not only must be
             * well-formed HTML (of course), but also must conform to the XHTML
             * requirements on tags, specifically all tags must either be self-closing
             * (like '<br/>') or come in matching pairs. Close tags are never optional.
             *
             * Note that &<number> isn't well-formed HTML; if you wanted a literal
             * '&123', your HTML template would have to have '&amp;123'.
             */
          _.numBlocks = function() {
            var matches = this.htmlTemplate.match(/&\d+/g);
            return matches ? matches.length : 0;
          };
          _.html = function() {
            // Render the entire math subtree rooted at this command, as HTML.
            // Expects .createBlocks() to have been called already, since it uses the
            // .blocks array of child blocks.
            //
            // See html.test.js for example templates and intended outputs.
            //
            // Given an .htmlTemplate as described above,
            // - insert the mathquill-command-id attribute into all top-level tags,
            // which will be used to set this.jQ in .jQize().
            // This is straightforward:
            // * tokenize into tags and non-tags
            // * loop through top-level tokens:
            // * add #cmdId attribute macro to top-level self-closing tags
            // * else add #cmdId attribute macro to top-level open tags
            // * skip the matching top-level close tag and all tag pairs
            // in between
            // - for each block content marker,
            // + replace it with the contents of the corresponding block,
            // rendered as HTML
            // + insert the mathquill-block-id attribute into the containing tag
            // This is even easier, a quick regex replace, since block tags cannot
            // contain anything besides the block content marker.
            //
            // Two notes:
            // - The outermost loop through top-level tokens should never encounter any
            // top-level close tags, because we should have first encountered a
            // matching top-level open tag, all inner tags should have appeared in
            // matching pairs and been skipped, and then we should have skipped the
            // close tag in question.
            // - All open tags should have matching close tags, which means our inner
            // loop should always encounter a close tag and drop nesting to 0. If
            // a close tag is missing, the loop will continue until i >= tokens.length
            // and token becomes undefined. This will not infinite loop, even in
            // production without pray(), because it will then TypeError on .slice().

            var cmd = this;
            var blocks = cmd.blocks;
            var cmdId = ' mathquill-command-id=' + cmd.id;
            var tokens = cmd.htmlTemplate.match(/<[^<>]+>|[^<>]+/g);

            pray('no unmatched angle brackets', tokens.join('') === this.htmlTemplate);

            // add cmdId to all top-level tags
            for (var i = 0, token = tokens[0]; token; i += 1, token = tokens[i]) {
              // top-level self-closing tags
              if (token.slice(-2) === '/>') {
                tokens[i] = token.slice(0,-2) + cmdId + '/>';
              }
              // top-level open tags
              else if (token.charAt(0) === '<') {
                pray('not an unmatched top-level close tag', token.charAt(1) !== '/');

                tokens[i] = token.slice(0,-1) + cmdId + '>';

                // skip matching top-level close tag and all tag pairs in between
                var nesting = 1;
                do {
                  i += 1, token = tokens[i];
                  pray('no missing close tags', token);
                  // close tags
                  if (token.slice(0,2) === '</') {
                    nesting -= 1;
                  }
                  // non-self-closing open tags
                  else if (token.charAt(0) === '<' && token.slice(-2) !== '/>') {
                    nesting += 1;
                  }
                } while (nesting > 0);
              }
            }
            return tokens.join('').replace(/>&(\d+)/g, function($0, $1) {
              return ' mathquill-block-id=' + blocks[$1].id + '>' + blocks[$1].join('html');
            });
          };

          // methods to export a string representation of the math tree
          _.latex = function() {
            return this.foldChildren(this.ctrlSeq, function(latex, child) {
              return latex + '{' + (child.latex() || ' ') + '}';
            });
          };
          _.textTemplate = [''];
          _.text = function() {
            var i = 0;
            return this.foldChildren(this.textTemplate[i], function(text, child) {
              i += 1;
              var child_text = child.text();
              if (text && this.textTemplate[i] === '('
                  && child_text[0] === '(' && child_text.slice(-1) === ')')
                return text + child_text.slice(1, -1) + this.textTemplate[i];
              return text + child.text() + (this.textTemplate[i] || '');
            });
          };
        });

        /**
         * Lightweight command without blocks or children.
         */
        var Symbol = P(MathCommand, function(_, _super) {
          _.init = function(ctrlSeq, html, text) {
            if (!text) text = ctrlSeq && ctrlSeq.length > 1 ? ctrlSeq.slice(1) : ctrlSeq;

            _super.init.call(this, ctrlSeq, html, [ text ]);
          };

          _.parser = function() { return Parser.succeed(this); };
          _.numBlocks = function() { return 0; };

          _.replaces = function(replacedFragment) {
            replacedFragment.remove();
          };
          _.createBlocks = noop;
          _.latex = function(){ return this.ctrlSeq; };
          _.text = function(){ return this.textTemplate; };
          _.placeCursor = noop;
          _.isEmpty = function(){ return true; };
        });

        /**
         * Children and parent of MathCommand's. Basically partitions all the symbols
         * and operators that descend (in the Math DOM tree) from ancestor operators.
         */
        var MathBlock = P(MathElement, function(_) {
          _.join = function(methodName) {
            return this.foldChildren('', function(fold, child) {
              return fold + child[methodName]();
            });
          };
          _.latex = function() { return this.join('latex'); };
          _.text = function() {
            return this.firstChild === this.lastChild ?
              this.firstChild.text() :
              '(' + this.join('text') + ')'
            ;
          };
          _.isEmpty = function() {
            return this.firstChild === 0 && this.lastChild === 0;
          };
          _.focus = function() {
            this.jQ.addClass('hasCursor');
            this.jQ.removeClass('empty');

            return this;
          };
          _.blur = function() {
            this.jQ.removeClass('hasCursor');
            if (this.isEmpty())
              this.jQ.addClass('empty');

            return this;
          };
        });

        /**
         * Math tree fragment base class. Some math-tree-specific extensions to
         * Fragment.
         */
        var MathFragment = P(Fragment, function(_, _super) {
          _.init = function(first, last) {
            // just select one thing if only one argument
            _super.init.call(this, first, last || first);
            this.jQ = this.fold($(), function(jQ, child){ return child.jQ.add(jQ); });
          };
          _.latex = function() {
            return this.fold('', function(latex, el){ return latex + el.latex(); });
          };
          _.remove = function() {
            this.jQ.remove();

            this.each(function(el) {
              el.postOrder(function(desc) {
                delete MathElement[desc.id];
              });
            });

            return this.disown();
          };
        });
        /*******************************************************************************
         * Root math elements with event delegation.
         ******************************************************************************/

        function createRoot(jQ, root, textbox, editable) {
          var contents = jQ.contents().detach();

          if (!textbox) {
            jQ.addClass('mathquill-rendered-math');
          }

          root.jQ = jQ.attr(mqBlockId, root.id);
          root.revert = function() {
            jQ.empty().unbind('.mathquill')
              .removeClass('mathquill-rendered-math mathquill-editable mathquill-textbox')
              .append(contents);
          };

          var cursor = root.cursor = Cursor(root);

          root.renderLatex(contents.text());

          // textarea stuff
          var textareaSpan = root.textarea = $('<span class="textarea"><textarea></textarea></span>'),
            textarea = textareaSpan.children();

          /***************************************************************************
             * TODO [Han]: Document this
             */
          var textareaSelectionTimeout;
          root.selectionChanged = function() {
            if (textareaSelectionTimeout === undefined) {
              textareaSelectionTimeout = setTimeout(setTextareaSelection);
            }
            forceIERedraw(jQ[0]);
          };
          function setTextareaSelection() {
            textareaSelectionTimeout = undefined;
            var latex = cursor.selection ? '':'';// '$'+cursor.selection.latex()+'$'
                                                    // : '';
            textareaManager.select(latex);
          }

          // prevent native selection except textarea
          jQ.bind('selectstart.mathquill', function(e) {
            if (e.target !== textarea[0]) e.preventDefault();
            e.stopPropagation();
          });

          // drag-to-select event handling
          var anticursor, blink = cursor.blink;
          jQ.bind('mousedown.mathquill', function(e) {
            function mousemove(e) {
              cursor.seek($(e.target), e.pageX, e.pageY);

              if (cursor.prev !== anticursor.prev
                  || cursor.parent !== anticursor.parent) {
                cursor.selectFrom(anticursor);
              }

              return false;
            }

            // docmousemove is attached to the document, so that
            // selection still works when the mouse leaves the window.
            function docmousemove(e) {
              // [Han]: i delete the target because of the way seek works.
              // it will not move the mouse to the target, but will instead
              // just seek those X and Y coordinates. If there is a target,
              // it will try to move the cursor to document, which will not work.
              // cursor.seek needs to be refactored.
              delete e.target;

              return mousemove(e);
            }

            function mouseup(e) {
              anticursor = undefined;
              cursor.blink = blink;
              if (!cursor.selection) {
                if (editable) {
                  cursor.show();
                }
                else {
                  textareaSpan.detach();
                }
              }

              // delete the mouse handlers now that we're not dragging anymore
              jQ.unbind('mousemove', mousemove);
              $(e.target.ownerDocument).unbind('mousemove', docmousemove).unbind('mouseup', mouseup);
            }

            setTimeout(function() { textarea.focus(); });
              // preventDefault won't prevent focus on mousedown in IE<9
              // that means immediately after this mousedown, whatever was
              // mousedown-ed will receive focus
              // http://bugs.jquery.com/ticket/10345

            cursor.blink = noop;
            cursor.seek($(e.target), e.pageX, e.pageY);

            anticursor = {parent: cursor.parent, prev: cursor.prev, next: cursor.next};

            if (!editable) jQ.prepend(textareaSpan);

            jQ.mousemove(mousemove);
            $(e.target.ownerDocument).mousemove(docmousemove).mouseup(mouseup);

            return false;
          });

          if (!editable) {
            var textareaManager = manageTextarea(textarea, { container: jQ });
            jQ.bind('cut paste', false).bind('copy', setTextareaSelection)
              .prepend('<span class="selectable">$'+root.latex()+'$</span>');
            textarea.blur(function() {
              cursor.clearSelection();
              setTimeout(detach); // detaching during blur explodes in WebKit
            });
            function detach() {
              textareaSpan.detach();
            }
            return;
          }

          var textareaManager = manageTextarea(textarea, {
            container: jQ,
            key: function(key, evt) {
              cursor.parent.bubble('onKey', key, evt);
            },
            text: function(text) {
              cursor.parent.bubble('onText', text);
            },
            cut: function(e) {
              if (cursor.selection) {
                setTimeout(function() {
                  cursor.prepareEdit();
                  cursor.parent.bubble('redraw');
                });
              }

              e.stopPropagation();
            },
            paste: function(text) {
              // FIXME HACK the parser in RootTextBlock needs to be moved to
              // Cursor::writeLatex or something so this'll work with
              // MathQuill textboxes
              if (text.slice(0,1) === '$' && text.slice(-1) === '$') {
                text = text.slice(1, -1);
              }
              else {
                text = '\\text{' + text + '}';
              }

              cursor.writeLatex(text).show();
            }
          });

          jQ.prepend(textareaSpan);

          // root CSS classes
          jQ.addClass('mathquill-editable');
          if (textbox)
            jQ.addClass('mathquill-textbox');

          // focus and blur handling
          textarea.focus(function(e) {
            if (!cursor.parent)
              cursor.appendTo(root);
            cursor.parent.jQ.addClass('hasCursor');
            if (cursor.selection) {
              cursor.selection.jQ.removeClass('blur');
              setTimeout(root.selectionChanged); // re-select textarea contents after
                                                // tabbing away and back
            }
            else
              cursor.show();
            e.stopPropagation();
          }).blur(function(e) {
            cursor.hide().parent.blur();
            if (cursor.selection)
              cursor.selection.jQ.addClass('blur');
            e.stopPropagation();
          });

          jQ.bind('focus.mathquill blur.mathquill', function(e) {
            textarea.trigger(e);
          }).blur();
        }

        var RootMathBlock = P(MathBlock, function(_, _super) {
          _.latex = function() {
            return _super.latex.call(this).replace(/(\\[a-z]+) (?![a-z])/ig,'$1');
          };
          _.text = function() {
            return this.foldChildren('', function(text, child) {
              return text + child.text();
            });
          };
          _.renderLatex = function(latex) {
            var jQ = this.jQ;

            jQ.children().slice(1).remove();
            this.firstChild = this.lastChild = 0;

            this.cursor.appendTo(this).writeLatex(latex);
          };
          _.onKey = function(key, e) {
            switch (key) {
            case 'Ctrl-Shift-Backspace':
            case 'Ctrl-Backspace':
              while (this.cursor.prev || this.cursor.selection) {
                this.cursor.backspace();
              }
              break;

            case 'Shift-Backspace':
            case 'Backspace':
              this.cursor.backspace();
              break;

            // Tab or Esc -> go one block right if it exists, else escape right.
            case 'Esc':
            case 'Tab':
            case 'Spacebar':
              var parent = this.cursor.parent;
              // cursor is in root editable, continue default
              if (parent === this.cursor.root) {
                if (key === 'Spacebar') e.preventDefault();
                return;
              }

              this.cursor.prepareMove();
              if (parent.next) {
                // go one block right
                this.cursor.prependTo(parent.next);
              } else {
                // get out of the block
                this.cursor.insertAfter(parent.parent);
              }
              break;

            // Shift-Tab -> go one block left if it exists, else escape left.
            case 'Shift-Tab':
            case 'Shift-Esc':
            case 'Shift-Spacebar':
              var parent = this.cursor.parent;
              // cursor is in root editable, continue default
              if (parent === this.cursor.root) {
                if (key === 'Shift-Spacebar') e.preventDefault();
                return;
              }

              this.cursor.prepareMove();
              if (parent.prev) {
                // go one block left
                this.cursor.appendTo(parent.prev);
              } else {
                // get out of the block
                this.cursor.insertBefore(parent.parent);
              }
              break;

            // Prevent newlines from showing up
            case 'Enter': break;


            // End -> move to the end of the current block.
            case 'End':
              this.cursor.prepareMove().appendTo(this.cursor.parent);
              break;

            // Ctrl-End -> move all the way to the end of the root block.
            case 'Ctrl-End':
              this.cursor.prepareMove().appendTo(this);
              break;

            // Shift-End -> select to the end of the current block.
            case 'Shift-End':
              while (this.cursor.next) {
                this.cursor.selectRight();
              }
              break;

            // Ctrl-Shift-End -> select to the end of the root block.
            case 'Ctrl-Shift-End':
              while (this.cursor.next || this.cursor.parent !== this) {
                this.cursor.selectRight();
              }
              break;

            // Home -> move to the start of the root block or the current block.
            case 'Home':
              this.cursor.prepareMove().prependTo(this.cursor.parent);
              break;

            // Ctrl-Home -> move to the start of the current block.
            case 'Ctrl-Home':
              this.cursor.prepareMove().prependTo(this);
              break;

            // Shift-Home -> select to the start of the current block.
            case 'Shift-Home':
              while (this.cursor.prev) {
                this.cursor.selectLeft();
              }
              break;

            // Ctrl-Shift-Home -> move to the start of the root block.
            case 'Ctrl-Shift-Home':
              while (this.cursor.prev || this.cursor.parent !== this) {
                this.cursor.selectLeft();
              }
              break;

            case 'Left': this.cursor.moveLeft(); break;
            case 'Shift-Left': this.cursor.selectLeft(); break;
            case 'Ctrl-Left': break;

            case 'Right': this.cursor.moveRight(); break;
            case 'Shift-Right': this.cursor.selectRight(); break;
            case 'Ctrl-Right': break;

            case 'Up': this.cursor.moveUp(); break;
            case 'Down': this.cursor.moveDown(); break;

            case 'Shift-Up':
              if (this.cursor.prev) {
                while (this.cursor.prev) this.cursor.selectLeft();
              } else {
                this.cursor.selectLeft();
              }

            case 'Shift-Down':
              if (this.cursor.next) {
                while (this.cursor.next) this.cursor.selectRight();
              }
              else {
                this.cursor.selectRight();
              }

            case 'Ctrl-Up': break;
            case 'Ctrl-Down': break;

            case 'Ctrl-Shift-Del':
            case 'Ctrl-Del':
              while (this.cursor.next || this.cursor.selection) {
                this.cursor.deleteForward();
              }
              break;

            case 'Shift-Del':
            case 'Del':
              this.cursor.deleteForward();
              break;

            case 'Meta-A':
            case 'Ctrl-A':
              // so not stopPropagation'd at RootMathCommand
              if (this !== this.cursor.root) return;

              this.cursor.prepareMove().appendTo(this);
              while (this.cursor.prev) this.cursor.selectLeft();
              break;

            default:
              return false;
            }
            e.preventDefault();
            return false;
          };
          _.onText = function(ch) {
            this.cursor.write(ch);
            return false;
          };
        });

        var RootMathCommand = P(MathCommand, function(_, _super) {
          _.init = function(cursor) {
            _super.init.call(this, '$');
            this.cursor = cursor;
          };
          _.htmlTemplate = '<span class="mathquill-rendered-math">&0</span>';
          _.createBlocks = function() {
            this.firstChild =
            this.lastChild =
              RootMathBlock();

            this.blocks = [ this.firstChild ];

            this.firstChild.parent = this;

            var cursor = this.firstChild.cursor = this.cursor;
            this.firstChild.onText = function(ch) {
              if (ch !== '$' || cursor.parent !== this)
                cursor.write(ch);
              else if (this.isEmpty()) {
                cursor.insertAfter(this.parent).backspace()
                  .insertNew(VanillaSymbol('\\$','$')).show();
              }
              else if (!cursor.next)
                cursor.insertAfter(this.parent);
              else if (!cursor.prev)
                cursor.insertBefore(this.parent);
              else
                cursor.write(ch);

              return false;
            };
          };
          _.latex = function() {
            return '$' + this.firstChild.latex() + '$';
          };
        });

        var RootTextBlock = P(MathBlock, function(_) {
          _.renderLatex = function(latex) {
            var self = this
            var cursor = self.cursor;
            self.jQ.children().slice(1).remove();
            self.firstChild = self.lastChild = 0;
            cursor.show().appendTo(self);

            var regex = Parser.regex;
            var string = Parser.string;
            var eof = Parser.eof;
            var all = Parser.all;

            // Parser RootMathCommand
            var mathMode = string('$').then(latexMathParser)
              // because TeX is insane, math mode doesn't necessarily
              // have to end. So we allow for the case that math mode
              // continues to the end of the stream.
              .skip(string('$').or(eof))
              .map(function(block) {
                // HACK FIXME: this shouldn't have to have access to cursor
                var rootMathCommand = RootMathCommand(cursor);

                rootMathCommand.createBlocks();
                var rootMathBlock = rootMathCommand.firstChild;
                block.children().adopt(rootMathBlock, 0, 0);

                return rootMathCommand;
              })
            ;

            var escapedDollar = string('\\$').result('$');
            var textChar = escapedDollar.or(regex(/^[^$]/)).map(VanillaSymbol);
            var latexText = mathMode.or(textChar).many();
            var commands = latexText.skip(eof).or(all.result(false)).parse(latex);

            if (commands) {
              for (var i = 0; i < commands.length; i += 1) {
                commands[i].adopt(self, self.lastChild, 0);
              }

              var html = self.join('html');
              MathElement.jQize(html).appendTo(self.jQ);

              this.finalizeInsert();
            }
          };
          _.onKey = RootMathBlock.prototype.onKey;
          _.onText = function(ch) {
            this.cursor.prepareEdit();
            if (ch === '$')
              this.cursor.insertNew(RootMathCommand(this.cursor));
            else
              this.cursor.insertNew(VanillaSymbol(ch));

            return false;
          };
        });
        /*******************************************************************************
         * Commands and Operators.
         ******************************************************************************/

        var CharCmds = {}, LatexCmds = {}; // single character commands, LaTeX commands

        var scale, // = function(jQ, x, y) { ... }
        // will use a CSS 2D transform to scale the jQuery-wrapped HTML elements,
        // or the filter matrix transform fallback for IE 5.5-8, or gracefully degrade
        // to
        // increasing the fontSize to match the vertical Y scaling factor.

        // ideas from http://github.com/louisremi/jquery.transform.js
        // see also http://msdn.microsoft.com/en-us/library/ms533014(v=vs.85).aspx

          forceIERedraw = noop,
          div = document.createElement('div'),
          div_style = div.style,
          transformPropNames = {
            transform:1,
            WebkitTransform:1,
            MozTransform:1,
            OTransform:1,
            msTransform:1
          },
          transformPropName;

        for (var prop in transformPropNames) {
          if (prop in div_style) {
            transformPropName = prop;
            break;
          }
        }

        if (transformPropName) {
          scale = function(jQ, x, y) {
            jQ.css(transformPropName, 'scale('+x+','+y+')');
          };
        }
        else if ('filter' in div_style) { // IE 6, 7, & 8 fallback, see
                                            // https://github.com/laughinghan/mathquill/wiki/Transforms
          forceIERedraw = function(el){ el.className = el.className; };
          scale = function(jQ, x, y) { // NOTE: assumes y > x
            x /= (1+(y-1)/2);
            x = x<1.5?1:x;
            jQ.css({fontSize:y + 'em'});
            if (!jQ.hasClass('matrixed-container')) {
              jQ.addClass('matrixed-container')
              .wrapInner('<span class="matrixed"></span>');
            }
            var innerjQ = jQ.children()
            .css({filter:'progid:DXImageTransform.Microsoft'
                + '.Matrix(M11=' + x + ",SizingMethod='auto expand')"
            });
            function calculateMarginRight() {
              x = parseInt(x);
              jQ.css({paddingRight:x+'px'});
            }
            calculateMarginRight();
            var intervalId = setInterval(calculateMarginRight);
            $(window).load(function() {
              clearTimeout(intervalId);
              calculateMarginRight();
            });
          };
        }
        else {
          scale = function(jQ, x, y) {
            jQ.css('fontSize', y + 'em');
          };
        }

        var Style = P(MathCommand, function(_, _super) {
          _.init = function(ctrlSeq, tagName, attrs) {
            _super.init.call(this, ctrlSeq, '<'+tagName+' '+attrs+'>&0</'+tagName+'>');
          };
        });

        // fonts
        LatexCmds.mathrm = bind(Style, '\\mathrm', 'span', 'class="roman font"');
        LatexCmds.mathit = bind(Style, '\\mathit', 'i', 'class="font"');
        LatexCmds.mathbf = bind(Style, '\\mathbf', 'b', 'class="font"');
        LatexCmds.mathsf = bind(Style, '\\mathsf', 'span', 'class="sans-serif font"');
        LatexCmds.mathtt = bind(Style, '\\mathtt', 'span', 'class="monospace font"');
        // text-decoration
        LatexCmds.underline = bind(Style, '\\underline', 'span', 'class="non-leaf underline"');
        LatexCmds.overline = LatexCmds.bar = bind(Style, '\\overline', 'span', 'class="non-leaf overline"');

        LatexCmds.overrightarrow = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\overrightarrow';
          _.htmlTemplate =
              '<span class="non-leaf overrightarrow">'
            +   '<span class="overrightarrow-arrow">&gt;</span>'
            +   '<span class="non-leaf overrightarrow-stem">&0</span>'
            + '</span>'
          ;
          _.textTemplate = ['overrightarrow(', ')'];
        });

        LatexCmds.customa = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\customa';
          _.htmlTemplate =
              '<span class="non-leaf customa customdiv">'
            +   '<span class="scaled sqrt-prefix">&#123;</span>'
            +   '<ul class="customul">'
            +   '<li class="non-leaf customli">&0</li>'
            +   '<li class="non-leaf customli">&1</li>'
            +   '</ul>'
            + '</span>'
          ;
          _.textTemplate = ['customa(', ')'];
          _.finalizeTree = function() {
            this.up = this.lastChild.up = this.firstChild;
            this.down = this.firstChild.down = this.lastChild;
          };
          _.redraw = function() {
            /*
             * var $sup = this.jQ.find('li').eq(0); var $sub = this.jQ.find('li').eq(1);
             * var fontSize = +$sup.css('fontSize').slice(0,-2); var prevWidth =
             * $sup.outerWidth(); var thisWidth = $sub.outerWidth(); $sub.css({ left: -
             * prevWidth/fontSize + 'em', marginRight: .1 - min(thisWidth,
             * prevWidth)/fontSize + 'em' //1px extra so it doesn't wrap in retarded
             * browsers (Firefox 2, I think) });
             */
            var blockjQ = this.jQ.find('ul').eq(0);
            var height = blockjQ.outerHeight()/+blockjQ.css('fontSize').slice(0,-2);
            var parens = this.jQ.find('span').eq(0);
                scale(parens, min(1 + .2*(height - 1), 1.2), .95*height);
          }
        });

        LatexCmds.customb = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\customb';
          _.htmlTemplate =
              '<span class="non-leaf customb customdiv_3">'
            +   '<span class="scaled sqrt-prefix" style="font-family:simsun">&#123;</span>'
            +   '<ul class="customul">'
            +   '<li class="non-leaf customli">&0</li>'
            +   '<li class="non-leaf customli">&1</li>'
            +   '<li class="non-leaf customli">&2</li>'
            +   '</ul>'
            + '</span>'
          ;
          _.textTemplate = ['customb(', ')'];
          _.finalizeTree = function() {
            this.firstChild.next.up = this.firstChild;
            this.lastChild.up = this.firstChild.next;
            this.firstChild.down = this.firstChild.next;
            this.firstChild.next.down = this.lastChild;
          };
           _.redraw = function() {
            var $sup = this.jQ.find('li').eq(0);
            var $sub = this.jQ.find('li').eq(1);
            var fontSize = +$sup.css('fontSize').slice(0,-2);
            var prevWidth = $sup.outerWidth();
            var thisWidth = $sub.outerWidth();
            /*
             * $sub.css({ left: - prevWidth/fontSize + 'em', marginRight: .1 -
             * min(thisWidth, prevWidth)/fontSize + 'em' //1px extra so it doesn't wrap
             * in retarded browsers (Firefox 2, I think) });
             */
            var blockjQ = this.jQ.find('ul').eq(0);
            var height = blockjQ.outerHeight()/+blockjQ.css('fontSize').slice(0,-2);
            var parens = this.jQ.find('span').eq(0);

            scale(parens, min(1 + .2*(height - 1), 1.2), .95*height);
          }
        });

        LatexCmds.customc = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\customc';
          _.htmlTemplate =
              '<span class="non-leaf customc customdiv">'
            +   '<ul class="customul">'
            +   '<li class="non-leaf customli_r">&0</li>'
            +   '<li class="non-leaf customli_r">&1</li>'
            +   '</ul>'
            +   '<span class="scaled sqrt-prefix">&#125;</span>'
            + '</span>'
          ;
          _.textTemplate = ['customc(', ')'];
          _.finalizeTree = function() {
            this.up = this.lastChild.up = this.firstChild;
            this.down = this.firstChild.down = this.lastChild
          };
          _.redraw = function() {
            /*
             * var $sup = this.jQ.find('li').eq(0); var $sub = this.jQ.find('li').eq(1);
             * var fontSize = +$sup.css('fontSize').slice(0,-2); var prevWidth =
             * $sup.outerWidth(); var thisWidth = $sub.outerWidth();
             */
            /*
             * $sub.css({ left: - prevWidth/fontSize + 'em', marginRight: .1 -
             * min(thisWidth, prevWidth)/fontSize + 'em' //1px extra so it doesn't wrap
             * in retarded browsers (Firefox 2, I think) });
             */
            var blockjQ = this.jQ.find('ul').eq(0);
            var height = blockjQ.outerHeight()/+blockjQ.css('fontSize').slice(0,-2);
            var parens = this.jQ.find('.scaled').eq(0);
            scale(parens, min(1 + .2*(height - 1), 1.2), .95*height);
          }
        });

        LatexCmds.customd = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\customd';
          _.htmlTemplate =
              '<span class="non-leaf customd topmark">'
            +   '<span class="non-leaf up">&0</span>'
            +   '<span class="non-leaf down">&1</span>'
            + '</span>'
          ;
          _.textTemplate = ['customd(', ')'];
          _.finalizeTree = function() {
            this.up = this.lastChild.up = this.firstChild;
            this.down = this.firstChild.down = this.lastChild;
          };
        });

        LatexCmds.custome = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\custome';
          _.htmlTemplate =
              '<span class="non-leaf custome">'
            +   '<span class="non-leaf up">⌒</span>'
            +   '<span class="non-leaf down">&0</span>'
            + '</span>'
          ;
          _.textTemplate = ['custome(', ')'];
          _.finalizeTree = function() {
            this.up = this.lastChild.up = this.firstChild;
            this.down = this.firstChild.down = this.lastChild;
          };
          _.redraw = function() {
            var block = this.lastChild.jQ;
            var fontSize = +this.jQ.css('fontSize').slice(0,-2);
            if($.browser.msie&&$.browser.version<=8)
                scale(block.prev(), (1.2*block.outerWidth())/fontSize, 1);
            else
                scale(block.prev(), (1.6*block.outerWidth())/fontSize, 1);
          }
        });

        LatexCmds.customf = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\customf';
          _.htmlTemplate =
              '<span class="non-leaf customf">'
            +   '<span class="symbol-lim">&0</span>'
            +   '<span class="symbol-lim-bt">&1</span>'
            + '</span>'
            + '<span class="non-leaf">&2</span>'
          ;
          _.textTemplate = ['customf(', ')'];
          _.finalizeTree = function() {
            this.up = this.lastChild.up = this.firstChild;
            this.down = this.firstChild.down = this.lastChild;
          };
        });

        LatexCmds.customg = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\customg';
          _.htmlTemplate =
              '<span class="non-leaf customg">'
            +   '<span class="non-leaf">&sum;</span>'
            +   '<span class="non-leaf">&0</span>'
            + '</span>'
          ;
          _.textTemplate = ['customg(', ')'];
        });

        LatexCmds.customh = P(MathCommand, function(_, _super) {
            _.ctrlSeq = '\\customh';
            _.htmlTemplate =
                '<span class="non-leaf customh">'
                +   '<span class="non-leaf customh_up" style="text-align:center;">&1</span>'
                +   '<span class="non-leaf customh_sum">&sum;</span>'
                +   '<span class="non-leaf customh_down" style="text-align:center;">&0</span>'
                + '</span>'
                +   '<span class="non-leaf customh_center">&2</span>'
                ;
            _.textTemplate = ['customh(', ')'];
            _.finalizeTree = function() {
                this.firstChild.next.up = this.firstChild;
                this.lastChild.up = this.firstChild.next;
                this.firstChild.down = this.firstChild.next;
                this.firstChild.next.down = this.lastChild;
            };
            _.redraw = function() {
                var _move = (this.firstChild.jQ.width())/2-11;
                this.lastChild.jQ.css({marginLeft:-(_move)+'px'});
                var _sumleft = _move>0?_move+3:0;
                this.jQ.find('.customh_sum').css({marginLeft:_sumleft+'px'});
            }
        });

        LatexCmds.customi = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\customi';
          _.htmlTemplate =
              '<span class="non-leaf customi">'
            +   '<span class="non-leaf">ln</span>'
            +   '<span class="non-leaf">&0</span>'
            + '</span>'
          ;
          _.textTemplate = ['customi(', ')'];
        });

        LatexCmds.customj = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\customj';
          _.htmlTemplate =
              '<span class="fraction non-leaf customj ">'
            +   '<sup class="non-leaf customli">&0</sup>'
            +   '<span class="non-leaf customli">&1</span>'
            +   '<sup class="non-leaf customli">&2</sup>'
            + '</span>'
          ;
          _.textTemplate = ['customj(', ')'];
          _.finalizeTree = function() {
            this.firstChild.next.up = this.firstChild;
            this.lastChild.up = this.firstChild.next;
            this.firstChild.down = this.firstChild.next;
            this.firstChild.next.down = this.lastChild;
          };
        });

        LatexCmds.customk = P(MathCommand, function(_, _super) {
            _.ctrlSeq = '\\customk';
            _.htmlTemplate =
                '<span class="fraction non-leaf customk ">'
                +   '<span class="numerator">&0<span class="arrowr">﹨</span></span>'
                +   '<span class="denominator">&1<span class="arrowl">﹨</span></span>'
                + '</span>'
                ;
          _.textTemplate = ['customk(', ')'];
          _.finalizeTree = function() {
            this.up = this.lastChild.up = this.firstChild;
            this.down = this.firstChild.down = this.lastChild;
          };
          _.redraw = function() { //实时跟踪输入
              var width = this.jQ.outerWidth();
          };
        });

        LatexCmds.customl = P(MathCommand, function(_, _super) {
            _.ctrlSeq = '\\customl';
            _.htmlTemplate =
                '<span class="fraction non-leaf customl ">'
                +   '<span class="numerator">&0</span>'
                +   '<span class="denominator">&1<span class="arrowright">＞</span></span>'
                + '</span>'
                ;
            _.textTemplate = ['customl(', ')'];
            _.finalizeTree = function() {
                this.up = this.lastChild.up = this.firstChild;
                this.down = this.firstChild.down = this.lastChild;
            };
        });

        LatexCmds.customm = P(MathCommand, function(_, _super) {
            _.ctrlSeq = '\\customm';
            _.htmlTemplate =
                '<span class="fraction non-leaf customm ">'
                +   '<span class="numerator">&0</span>'
                +   '<span class="denominator">&1</span>'
                + '</span>'
                ;
            _.textTemplate = ['customm(', ')'];
            _.finalizeTree = function() {
                this.up = this.lastChild.up = this.firstChild;
                this.down = this.firstChild.down = this.lastChild;
            };
        });

        LatexCmds.log = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\log';
          _.htmlTemplate =
              '<span class="non-leaf log">'
            +   '<span class="non-leaf">log</span>'
            +   '<span class="non-leaf log_down">&0</span>'
            +   '<span class="non-leaf log_up">&1</span>'
            + '</span>'
          ;
          _.textTemplate = ['log(', ')'];
          _.finalizeTree = function() {
            this.up = this.lastChild.up = this.firstChild;
            this.down = this.firstChild.down = this.lastChild;
          };
        });

        var SupSub = P(MathCommand, function(_, _super) {
          _.init = function(ctrlSeq, tag, text) {
            _super.init.call(this, ctrlSeq, '<'+tag+' class="non-leaf">&0</'+tag+'>', [ text ]);
          };
          _.finalizeTree = function() {
            // TODO: use inheritance
            pray('SupSub is only _ and ^',
              this.ctrlSeq === '^' || this.ctrlSeq === '_'
            );

            if (this.ctrlSeq === '_') {
              this.down = this.firstChild;
              this.firstChild.up = insertBeforeUnlessAtEnd;
            }
            else {
              this.up = this.firstChild;
              this.firstChild.down = insertBeforeUnlessAtEnd;
            }
            function insertBeforeUnlessAtEnd(cursor) {
              // cursor.insertBefore(cmd), unless cursor at the end of block, and
                // every
              // ancestor cmd is at the end of every ancestor block
              var cmd = this.parent, ancestorCmd = cursor;
              do {
                if (ancestorCmd.next) {
                  cursor.insertBefore(cmd);
                  return false;
                }
                ancestorCmd = ancestorCmd.parent.parent;
              } while (ancestorCmd !== cmd);
              cursor.insertAfter(cmd);
              return false;
            }
          };
          _.latex = function() {
            var latex = this.firstChild.latex();
            if (latex.length === 1)
              return this.ctrlSeq + latex;
            else
              return this.ctrlSeq + '{' + (latex || ' ') + '}';
          };
          _.redraw = function() {
            if (this.prev)
              this.prev.respace();
            // SupSub::respace recursively calls respace on all the following SupSubs
            // so if prev is a SupSub, no need to call respace on this or following
            // nodes
            if (!(this.prev instanceof SupSub)) {
              this.respace();
              // and if next is a SupSub, then this.respace() will have already called
              // this.next.respace()
              if (this.next && !(this.next instanceof SupSub))
                this.next.respace();
            }
          };
          _.respace = function() {
            if (
              this.prev.ctrlSeq === '\\int ' || this.prev.ctrlSeq === '\\oint ' || (
                this.prev instanceof SupSub && this.prev.ctrlSeq != this.ctrlSeq
                && this.prev.prev && (this.prev.prev.ctrlSeq === '\\int ' || this.prev.prev.ctrlSeq === '\\oint ')
              )
            ) {
              if (!this.limit) {
                this.limit = true;
                this.jQ.addClass('limit');
              }
            }
            else {
              if (this.limit) {
                this.limit = false;
                this.jQ.removeClass('limit');
              }
            }

            this.respaced = this.prev instanceof SupSub && this.prev.ctrlSeq != this.ctrlSeq && !this.prev.respaced;
            if (this.respaced) {
              var fontSize = +this.jQ.css('fontSize').slice(0,-2),
                prevWidth = this.prev.jQ.outerWidth(),
                thisWidth = this.jQ.outerWidth();
              this.jQ.css({
                left: (this.limit && this.ctrlSeq === '_' ? -.25 : 0) - prevWidth/fontSize + 'em',
                marginRight: .1 - min(thisWidth, prevWidth)/fontSize + 'em'
                  // 1px extra so it doesn't wrap in retarded browsers (Firefox 2, I
                    // think)
              });
            }
            else if (this.limit && this.ctrlSeq === '_') {
              this.jQ.css({
                left: '-.25em',
                marginRight: ''
              });
            }
            else {
              this.jQ.css({
                left: '',
                marginRight: ''
              });
            }

            if (this.next instanceof SupSub)
              this.next.respace();

            return this;
          };
        });

        LatexCmds.subscript =
        LatexCmds._ = bind(SupSub, '_', 'sub', '_');

        LatexCmds.superscript =
        LatexCmds.supscript =
        LatexCmds['^'] = bind(SupSub, '^', 'sup', '**');

        var Fraction =
        LatexCmds.frac =
        LatexCmds.dfrac =
        LatexCmds.cfrac =
        LatexCmds.fraction = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\frac';
          _.htmlTemplate =
              '<span class="fraction non-leaf">'
            +   '<span class="numerator">&0</span>'
            +   '<span class="denominator">&1</span>'
            +   '<span style="display:inline-block;width:0">&nbsp;</span>'
            + '</span>'
          ;
          _.textTemplate = ['(', '/', ')'];
          _.finalizeTree = function() {
            this.up = this.lastChild.up = this.firstChild;
            this.down = this.firstChild.down = this.lastChild;
          };
        });

        var LiveFraction =
        LatexCmds.over =
        CharCmds['/'] = P(Fraction, function(_, _super) {
          _.createBefore = function(cursor) {
            if (!this.replacedFragment) {
              var prev = cursor.prev;
              while (prev &&
                !(
                  prev instanceof BinaryOperator ||
                  prev instanceof TextBlock ||
                  prev instanceof BigSymbol
                ) // lookbehind for operator
              )
                prev = prev.prev;

              if (prev instanceof BigSymbol && prev.next instanceof SupSub) {
                prev = prev.next;
                if (prev.next instanceof SupSub && prev.next.ctrlSeq != prev.ctrlSeq)
                  prev = prev.next;
              }

              if (prev !== cursor.prev) {
                this.replaces(MathFragment(prev.next || cursor.parent.firstChild, cursor.prev));
                cursor.prev = prev;
              }
            }
            _super.createBefore.call(this, cursor);
          };
        });

        var SquareRoot =
        LatexCmds.sqrt =
        LatexCmds['√'] = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\sqrt';
          _.htmlTemplate =
              '<span class="non-leaf">'
            +   '<span class="scaled sqrt-prefix">&radic;</span>'
            +   '<span class="non-leaf sqrt-stem">&0</span>'
            + '</span>'
          ;
          _.textTemplate = ['sqrt(', ')'];
          _.parser = function() {
            return latexMathParser.optBlock.then(function(optBlock) {
              return latexMathParser.block.map(function(block) {
                var nthroot = NthRoot();
                nthroot.blocks = [ optBlock, block ];
                optBlock.adopt(nthroot, 0, 0);
                block.adopt(nthroot, optBlock, 0);
                return nthroot;
              });
            }).or(_super.parser.call(this));
          };
          _.redraw = function() {
            var block = this.lastChild.jQ;
            scale(block.prev(), 1, block.innerHeight()/+block.css('fontSize').slice(0,-2) - .1);
          };
        });


        var NthRoot =
        LatexCmds.nthroot = P(SquareRoot, function(_, _super) {
          _.htmlTemplate =
              '<sup class="nthroot non-leaf" style="z-index:1;">&0</sup>'
            + '<span class="scaled" style="margin-left:-4px">'
            +   '<span class="sqrt-prefix scaled">&radic;</span>'
            +   '<span class="sqrt-stem non-leaf">&1</span>'
            + '</span>'
          ;
          _.textTemplate = ['sqrt[', '](', ')'];
          _.latex = function() {
            return '\\sqrt['+this.firstChild.latex()+']{'+this.lastChild.latex()+'}';
          };
        });

        // Round/Square/Curly/Angle Brackets (aka Parens/Brackets/Braces)
        var Bracket = P(MathCommand, function(_, _super) {
          _.init = function(open, close, ctrlSeq, end) {
            _super.init.call(this, '\\left'+ctrlSeq,
              '<span class="non-leaf">'
              +   '<span class="scaled paren">'+open+'</span>'
              +   '<span class="non-leaf">&0</span>'
              +   '<span class="scaled paren">'+close+'</span>'
              + '</span>',
              [open, close]);
            this.end = '\\right'+end;
          };
          _.jQadd = function() {
            _super.jQadd.apply(this, arguments);
            var jQ = this.jQ;
            this.bracketjQs = jQ.children(':first').add(jQ.children(':last'));
          };
          _.latex = function() {
            return this.ctrlSeq + this.firstChild.latex() + this.end;
          };
          _.redraw = function() {
            var blockjQ = this.firstChild.jQ;

            var height = blockjQ.outerHeight()/+blockjQ.css('fontSize').slice(0,-2);

            scale(this.bracketjQs, min(1 + .2*(height - 1), 1.2), 1.05*height);
          };
        });

        LatexCmds.left = P(MathCommand, function(_) {
          _.parser = function() {
            var regex = Parser.regex;
            var string = Parser.string;
            var regex = Parser.regex;
            var succeed = Parser.succeed;
            var block = latexMathParser.block;
            var optWhitespace = Parser.optWhitespace;

            return optWhitespace.then(regex(/^(?:[([|]|\\\{)/))
              .then(function(open) {
                if (open.charAt(0) === '\\') open = open.slice(1);

                var cmd = CharCmds[open]();

                return latexMathParser
                  .map(function (block) {
                    cmd.blocks = [ block ];
                    block.adopt(cmd, 0, 0);
                  })
                  .then(string('\\right'))
                  .skip(optWhitespace)
                  .then(regex(/^(?:[\])|]|\\\})/))
                  .then(function(close) {
                    if (close.slice(-1) !== cmd.end.slice(-1)) {
                      return Parser.fail('open doesn\'t match close');
                    }

                    return succeed(cmd);
                  })
                ;
              })
            ;
          };
        });

        LatexCmds.right = P(MathCommand, function(_) {
          _.parser = function() {
            return Parser.fail('unmatched \\right');
          };
        });

        LatexCmds.lbrace =
        CharCmds['{'] = bind(Bracket, '{', '}', '\\{', '\\}');
        LatexCmds.langle =
        LatexCmds.lang = bind(Bracket, '&lang;','&rang;','\\langle ','\\rangle ');

        // Closing bracket matching opening bracket above
        var CloseBracket = P(Bracket, function(_, _super) {
          _.createBefore = function(cursor) {
            // if I'm at the end of my parent who is a matching open-paren,
            // and I am not replacing a selection fragment, don't create me,
            // just put cursor after my parent
            if (!cursor.next && cursor.parent.parent && cursor.parent.parent.end === this.end && !this.replacedFragment)
              cursor.insertAfter(cursor.parent.parent);
            else
              _super.createBefore.call(this, cursor);
          };
          _.placeCursor = function(cursor) {
            this.firstChild.blur();
            cursor.insertAfter(this);
          };
        });

        LatexCmds.rbrace =
        CharCmds['}'] = bind(CloseBracket, '{','}','\\{','\\}');
        LatexCmds.rangle =
        LatexCmds.rang = bind(CloseBracket, '&lang;','&rang;','\\langle ','\\rangle ');

        var parenMixin = function(_, _super) {
          _.init = function(open, close) {
            _super.init.call(this, open, close, open, close);
          };
        };

        var Paren = P(Bracket, parenMixin);

        LatexCmds.lparen =
        CharCmds['('] = bind(Paren, '(', ')');
        LatexCmds.lbrack =
        LatexCmds.lbracket =
        CharCmds['['] = bind(Paren, '[', ']');

        var CloseParen = P(CloseBracket, parenMixin);

        LatexCmds.rparen =
        CharCmds[')'] = bind(CloseParen, '(', ')');
        LatexCmds.rbrack =
        LatexCmds.rbracket =
        CharCmds[']'] = bind(CloseParen, '[', ']');

        var Pipes =
        LatexCmds.lpipe =
        LatexCmds.rpipe =
        CharCmds['|'] = P(Paren, function(_, _super) {
          _.init = function() {
            _super.init.call(this, '|', '|');
          }

          _.createBefore = CloseBracket.prototype.createBefore;
        });

        var TextBlock =
        CharCmds.$ =
        LatexCmds.text =
        LatexCmds.textnormal =
        LatexCmds.textrm =
        LatexCmds.textup =
        LatexCmds.textmd = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\text';
          _.htmlTemplate = '<span class="text">&0</span>';
          _.replaces = function(replacedText) {
            if (replacedText instanceof MathFragment)
              this.replacedText = replacedText.remove().jQ.text();
            else if (typeof replacedText === 'string')
              this.replacedText = replacedText;
          };
          _.textTemplate = ['"', '"'];
          _.parser = function() {
            // TODO: correctly parse text mode
            var string = Parser.string;
            var regex = Parser.regex;
            var optWhitespace = Parser.optWhitespace;
            return optWhitespace
              .then(string('{')).then(regex(/^[^}]*/)).skip(string('}'))
              .map(function(text) {
                var cmd = TextBlock();
                cmd.createBlocks();
                var block = cmd.firstChild;
                for (var i = 0; i < text.length; i += 1) {
                  var ch = VanillaSymbol(text.charAt(i));
                  ch.adopt(block, block.lastChild, 0);
                }
                return cmd;
              })
            ;
          };
          _.createBlocks = function() {
            // FIXME: another possible Law of Demeter violation, but this seems much
            // cleaner, like it was supposed to be done this way
            this.firstChild =
            this.lastChild =
              InnerTextBlock();

            this.blocks = [ this.firstChild ];

            this.firstChild.parent = this;
          };
          _.finalizeInsert = function() {
            // FIXME HACK blur removes the TextBlock
            this.firstChild.blur = function() { delete this.blur; return this; };
            _super.finalizeInsert.call(this);
          };
          _.createBefore = function(cursor) {
            _super.createBefore.call(this, this.cursor = cursor);

            if (this.replacedText)
              for (var i = 0; i < this.replacedText.length; i += 1)
                this.write(this.replacedText.charAt(i));
          };
          _.write = function(ch) {
            this.cursor.insertNew(VanillaSymbol(ch));
          };
          _.onKey = function(key, e) {
            // backspace and delete and ends of block don't unwrap
            if (!this.cursor.selection &&
              (
                (key === 'Backspace' && !this.cursor.prev) ||
                (key === 'Del' && !this.cursor.next)
              )
            ) {
              if (this.isEmpty())
                this.cursor.insertAfter(this);

              return false;
            }
          };
          _.onText = function(ch) {
            this.cursor.prepareEdit();
            if (ch !== '$')
              this.write(ch);
            else if (this.isEmpty())
              this.cursor.insertAfter(this).backspace().insertNew(VanillaSymbol('\\$','$'));
            else if (!this.cursor.next)
              this.cursor.insertAfter(this);
            else if (!this.cursor.prev)
              this.cursor.insertBefore(this);
            else { // split apart
              var next = TextBlock(MathFragment(this.cursor.next, this.firstChild.lastChild));
              next.placeCursor = function(cursor) { // FIXME HACK: pretend no prev so
                                                    // they don't get merged
                this.prev = 0;
                delete this.placeCursor;
                this.placeCursor(cursor);
              };
              next.firstChild.focus = function(){ return this; };
              this.cursor.insertAfter(this).insertNew(next);
              next.prev = this;
              this.cursor.insertBefore(next);
              delete next.firstChild.focus;
            }
            return false;
          };
        });

        var InnerTextBlock = P(MathBlock, function(_, _super) {
          _.blur = function() {
            this.jQ.removeClass('hasCursor');
            if (this.isEmpty()) {
              var textblock = this.parent, cursor = textblock.cursor;
              if (cursor.parent === this)
                this.jQ.addClass('empty');
              else {
                cursor.hide();
                textblock.remove();
                if (cursor.next === textblock)
                  cursor.next = textblock.next;
                else if (cursor.prev === textblock)
                  cursor.prev = textblock.prev;

                cursor.show().parent.bubble('redraw');
              }
            }
            return this;
          };
          _.focus = function() {
            _super.focus.call(this);

            var textblock = this.parent;
            if (textblock.next.ctrlSeq === textblock.ctrlSeq) { // TODO: seems like
                                                                // there should be a
                                                                // better way to move
                                                                // MathElements around
              var innerblock = this,
                cursor = textblock.cursor,
                next = textblock.next.firstChild;

              next.eachChild(function(child){
                child.parent = innerblock;
                child.jQ.appendTo(innerblock.jQ);
              });

              if (this.lastChild)
                this.lastChild.next = next.firstChild;
              else
                this.firstChild = next.firstChild;

              next.firstChild.prev = this.lastChild;
              this.lastChild = next.lastChild;

              next.parent.remove();

              if (cursor.prev)
                cursor.insertAfter(cursor.prev);
              else
                cursor.prependTo(this);

              cursor.parent.bubble('redraw');
            }
            else if (textblock.prev.ctrlSeq === textblock.ctrlSeq) {
              var cursor = textblock.cursor;
              if (cursor.prev)
                textblock.prev.firstChild.focus();
              else
                cursor.appendTo(textblock.prev.firstChild);
            }
            return this;
          };
        });


        function makeTextBlock(latex, tagName, attrs) {
          return P(TextBlock, {
            ctrlSeq: latex,
            htmlTemplate: '<'+tagName+' '+attrs+'>&0</'+tagName+'>'
          });
        }

        LatexCmds.em = LatexCmds.italic = LatexCmds.italics =
        LatexCmds.emph = LatexCmds.textit = LatexCmds.textsl =
          makeTextBlock('\\textit', 'i', 'class="text"');
        LatexCmds.strong = LatexCmds.bold = LatexCmds.textbf =
          makeTextBlock('\\textbf', 'b', 'class="text"');
        LatexCmds.sf = LatexCmds.textsf =
          makeTextBlock('\\textsf', 'span', 'class="sans-serif text"');
        LatexCmds.tt = LatexCmds.texttt =
          makeTextBlock('\\texttt', 'span', 'class="monospace text"');
        LatexCmds.textsc =
          makeTextBlock('\\textsc', 'span', 'style="font-variant:small-caps" class="text"');
        LatexCmds.uppercase =
          makeTextBlock('\\uppercase', 'span', 'style="text-transform:uppercase" class="text"');
        LatexCmds.lowercase =
          makeTextBlock('\\lowercase', 'span', 'style="text-transform:lowercase" class="text"');

        // input box to type a variety of LaTeX commands beginning with a backslash
        var LatexCommandInput =
        CharCmds['\\'] = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\';
          _.replaces = function(replacedFragment) {
            this._replacedFragment = replacedFragment.disown();
            this.isEmpty = function() { return false; };
          };
          _.htmlTemplate = '<span class="latex-command-input non-leaf">\\<span>&0</span></span>';
          _.textTemplate = ['\\'];
          _.createBlocks = function() {
            _super.createBlocks.call(this);
            this.firstChild.focus = function() {
              this.parent.jQ.addClass('hasCursor');
              if (this.isEmpty())
                this.parent.jQ.removeClass('empty');

              return this;
            };
            this.firstChild.blur = function() {
              this.parent.jQ.removeClass('hasCursor');
              if (this.isEmpty())
                this.parent.jQ.addClass('empty');

              return this;
            };
          };
          _.createBefore = function(cursor) {
            _super.createBefore.call(this, cursor);
            this.cursor = cursor.appendTo(this.firstChild);
            if (this._replacedFragment) {
              var el = this.jQ[0];
              this.jQ =
                this._replacedFragment.jQ.addClass('blur').bind(
                  'mousedown mousemove', // FIXME: is monkey-patching the mousedown
                                            // and mousemove handlers the right way to
                                            // do this?
                  function(e) {
                    $(e.target = el).trigger(e);
                    return false;
                  }
                ).insertBefore(this.jQ).add(this.jQ);
            }
          };
          _.latex = function() {
            return '\\' + this.firstChild.latex() + ' ';
          };
          _.onKey = function(key, e) {
            if (key === 'Tab' || key === 'Enter' || key === 'Spacebar') {
              this.renderCommand();
              e.preventDefault();
              return false;
            }
          };
          _.onText = function(ch) {
            if (ch.match(/[a-z]/i)) {
              this.cursor.prepareEdit();
              this.cursor.insertNew(VanillaSymbol(ch));
              return false;
            }
            this.renderCommand();
            if (ch === '\\' && this.firstChild.isEmpty())
              return false;
          };
          _.renderCommand = function() {
            this.jQ = this.jQ.last();
            this.remove();
            if (this.next) {
              this.cursor.insertBefore(this.next);
            } else {
              this.cursor.appendTo(this.parent);
            }

            var latex = this.firstChild.latex(), cmd;
            if (!latex) latex = 'backslash';
            this.cursor.insertCmd(latex, this._replacedFragment);
          };
        });

        var Binomial =
        LatexCmds.binom =
        LatexCmds.binomial = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\binom';
          _.htmlTemplate =
              '<span class="paren scaled">(</span>'
            + '<span class="non-leaf">'
            +   '<span class="array non-leaf">'
            +     '<span>&0</span>'
            +     '<span>&1</span>'
            +   '</span>'
            + '</span>'
            + '<span class="paren scaled">)</span>'
          ;
          _.textTemplate = ['choose(',',',')'];
          _.redraw = function() {
            var blockjQ = this.jQ.eq(1);

            var height = blockjQ.outerHeight()/+blockjQ.css('fontSize').slice(0,-2);

            var parens = this.jQ.filter('.paren');
            scale(parens, min(1 + .2*(height - 1), 1.2), 1.05*height);
          };
        });

        var Choose =
        LatexCmds.choose = P(Binomial, function(_) {
          _.createBefore = LiveFraction.prototype.createBefore;
        });

        var Vector =
        LatexCmds.vector = P(MathCommand, function(_, _super) {
          _.ctrlSeq = '\\vector';
          _.htmlTemplate = '<span class="array"><span>&0</span></span>';
          _.latex = function() {
            return '\\begin{matrix}' + this.foldChildren([], function(latex, child) {
              latex.push(child.latex());
              return latex;
            }).join('\\\\') + '\\end{matrix}';
          };
          _.text = function() {
            return '[' + this.foldChildren([], function(text, child) {
              text.push(child.text());
              return text;
            }).join() + ']';
          }
          _.createBefore = function(cursor) {
            _super.createBefore.call(this, this.cursor = cursor);
          };
          _.onKey = function(key, e) {
            var currentBlock = this.cursor.parent;

            if (currentBlock.parent === this) {
              if (key === 'Enter') { // enter
                var newBlock = MathBlock();
                newBlock.parent = this;
                newBlock.jQ = $('<span></span>')
                  .attr(mqBlockId, newBlock.id)
                  .insertAfter(currentBlock.jQ);
                if (currentBlock.next)
                  currentBlock.next.prev = newBlock;
                else
                  this.lastChild = newBlock;

                newBlock.next = currentBlock.next;
                currentBlock.next = newBlock;
                newBlock.prev = currentBlock;
                this.bubble('redraw').cursor.appendTo(newBlock);

                e.preventDefault();
                return false;
              }
              else if (key === 'Tab' && !currentBlock.next) {
                if (currentBlock.isEmpty()) {
                  if (currentBlock.prev) {
                    this.cursor.insertAfter(this);
                    delete currentBlock.prev.next;
                    this.lastChild = currentBlock.prev;
                    currentBlock.jQ.remove();
                    this.bubble('redraw');

                    e.preventDefault();
                    return false;
                  }
                  else
                    return;
                }

                var newBlock = MathBlock();
                newBlock.parent = this;
                newBlock.jQ = $('<span></span>').attr(mqBlockId, newBlock.id).appendTo(this.jQ);
                this.lastChild = newBlock;
                currentBlock.next = newBlock;
                newBlock.prev = currentBlock;
                this.bubble('redraw').cursor.appendTo(newBlock);

                e.preventDefault();
                return false;
              }
              else if (e.which === 8) { // backspace
                if (currentBlock.isEmpty()) {
                  if (currentBlock.prev) {
                    this.cursor.appendTo(currentBlock.prev)
                    currentBlock.prev.next = currentBlock.next;
                  }
                  else {
                    this.cursor.insertBefore(this);
                    this.firstChild = currentBlock.next;
                  }

                  if (currentBlock.next)
                    currentBlock.next.prev = currentBlock.prev;
                  else
                    this.lastChild = currentBlock.prev;

                  currentBlock.jQ.remove();
                  if (this.isEmpty())
                    this.cursor.deleteForward();
                  else
                    this.bubble('redraw');

                  e.preventDefault();
                  return false;
                }
                else if (!this.cursor.prev) {
                  e.preventDefault();
                  return false;
                }
              }
            }
          };
        });

        LatexCmds.editable = P(RootMathCommand, function(_, _super) {
          _.init = function() {
            MathCommand.prototype.init.call(this, '\\editable');
          };

          _.jQadd = function() {
            var self = this;
            // FIXME: this entire method is a giant hack to get around
            // having to call createBlocks, and createRoot expecting to
            // render the contents' LaTeX. Both need to be refactored.
            _super.jQadd.apply(self, arguments);
            var block = self.firstChild.disown();
            var blockjQ = self.jQ.children().detach();

            self.firstChild =
            self.lastChild =
              RootMathBlock();

            self.blocks = [ self.firstChild ];

            self.firstChild.parent = self;

            createRoot(self.jQ, self.firstChild, false, true);
            self.cursor = self.firstChild.cursor;

            block.children().adopt(self.firstChild, 0, 0);
            blockjQ.appendTo(self.firstChild.jQ);

            self.firstChild.cursor.appendTo(self.firstChild);
          };

          _.latex = function(){ return this.firstChild.latex(); };
          _.text = function(){ return this.firstChild.text(); };
        });
        /*******************************************************************************
         * Symbols and Special Characters
         ******************************************************************************/

        //LatexCmds.f = bind(Symbol, 'f', '<var class="florin">&fnof;</var><span style="display:inline-block;width:0">&nbsp;</span>');

        var Variable = P(Symbol, function(_, _super) {
          _.init = function(ch, html) {
            _super.init.call(this, ch, '<var>'+(html || ch)+'</var>');
          }
          _.text = function() {
            var text = this.ctrlSeq;
            if (this.prev && !(this.prev instanceof Variable)
                && !(this.prev instanceof BinaryOperator))
              text = '*' + text;
            if (this.next && !(this.next instanceof BinaryOperator)
                && !(this.next.ctrlSeq === '^'))
              text += '*';
            return text;
          };
        });

        var VanillaSymbol = P(Symbol, function(_, _super) {
          _.init = function(ch, html) {
            _super.init.call(this, ch, '<span>'+(html || ch)+'</span>');
          };
        });

        CharCmds[' '] = bind(VanillaSymbol, '\\:', ' ');

        LatexCmds.prime = CharCmds["'"] = bind(VanillaSymbol, "'", '&prime;');

        // does not use Symbola font
        var NonSymbolaSymbol = P(Symbol, function(_, _super) {
          _.init = function(ch, html) {
            _super.init.call(this, ch, '<span class="nonSymbola">'+(html || ch)+'</span>');
          };
        });

        LatexCmds['@'] = NonSymbolaSymbol;
        LatexCmds['&'] = bind(NonSymbolaSymbol, '&', '&amp;');
        LatexCmds['%'] = bind(NonSymbolaSymbol, '%', '%');

        // the following are all Greek to me, but this helped a lot:
        // http://www.ams.org/STIX/ion/stixsig03.html


        LatexCmds['α'] = bind(Variable,'α','&alpha;');
        LatexCmds['β'] = bind(Variable,'β','&beta;');
        LatexCmds['γ'] = bind(Variable,'γ','&gamma;');
        LatexCmds['δ'] = bind(Variable,'δ','&delta;');
        LatexCmds['μ'] = bind(Variable,'μ','&mu;');
        LatexCmds['ρ'] = bind(Variable,'ρ','&rho;');
        LatexCmds['σ'] = bind(Variable,'σ','&sigma;');
        LatexCmds['ω'] = bind(Variable,'ω','&omega;');

        // lowercase Greek letter variables
        /*
         * LatexCmds.alpha = LatexCmds.beta = LatexCmds.gamma = LatexCmds.delta =
         * LatexCmds.zeta = LatexCmds.eta = LatexCmds.theta = LatexCmds.iota =
         * LatexCmds.kappa = LatexCmds.mu = LatexCmds.nu = LatexCmds.xi = LatexCmds.rho =
         * LatexCmds.sigma = LatexCmds.tau = LatexCmds.chi = LatexCmds.psi =
         * LatexCmds.omega = P(Variable, function(_, _super) { _.init = function(latex) {
         * _super.init.call(this,'\\'+latex+' ','&'+latex+';'); }; });
         */

        // why can't anybody FUCKING agree on these
        LatexCmds.phi = // W3C or Unicode?
          bind(Variable,'\\phi ','&#981;');

        LatexCmds.phiv = // Elsevier and 9573-13
        LatexCmds.varphi = LatexCmds['φ'] = // AMS and LaTeX
          bind(Variable,'φ','&phi;');

        LatexCmds.epsilon = // W3C or Unicode?
          bind(Variable,'\\epsilon ','&#1013;');

        LatexCmds.epsiv = // Elsevier and 9573-13
        LatexCmds.varepsilon = LatexCmds['ε'] = // AMS and LaTeX
          bind(Variable,'ε','&epsilon;');

        LatexCmds.piv = // W3C/Unicode and Elsevier and 9573-13
        LatexCmds.varpi = // AMS and LaTeX
          bind(Variable,'\\varpi ','&piv;');

        LatexCmds.sigmaf = // W3C/Unicode
        LatexCmds.sigmav = // Elsevier
        LatexCmds.varsigma = // LaTeX
          bind(Variable,'\\varsigma ','&sigmaf;');

        LatexCmds.thetav = // Elsevier and 9573-13
        LatexCmds.vartheta = // AMS and LaTeX
        LatexCmds.thetasym = // W3C/Unicode
          bind(Variable,'\\vartheta ','&thetasym;');

        LatexCmds.upsilon = // AMS and LaTeX and W3C/Unicode
        LatexCmds.upsi = // Elsevier and 9573-13
          bind(Variable,'\\upsilon ','&upsilon;');

        // these aren't even mentioned in the HTML character entity references
        LatexCmds.gammad = // Elsevier
        LatexCmds.Gammad = // 9573-13 -- WTF, right? I dunno if this was a typo in the
                            // reference (see above)
        LatexCmds.digamma = // LaTeX
          bind(Variable,'\\digamma ','&#989;');

        LatexCmds.kappav = // Elsevier
        LatexCmds.varkappa = // AMS and LaTeX
          bind(Variable,'\\varkappa ','&#1008;');

        LatexCmds.rhov = // Elsevier and 9573-13
        LatexCmds.varrho = // AMS and LaTeX
          bind(Variable,'\\varrho ','&#1009;');

        // Greek constants, look best in un-italicised Times New Roman
        LatexCmds.pi = LatexCmds['π'] = bind(NonSymbolaSymbol,'π','&pi;');
        LatexCmds.lambda = LatexCmds['λ'] = bind(NonSymbolaSymbol,'λ','&lambda;');

        // uppercase greek letters

        LatexCmds.Upsilon = LatexCmds['ϒ'] =// LaTeX
        LatexCmds.Upsi = // Elsevier and 9573-13
        LatexCmds.upsih = // W3C/Unicode "upsilon with hook"
        LatexCmds.Upsih = // 'cos it makes sense to me
          bind(Symbol,'ϒ','<var style="font-family: serif">&upsih;</var>'); // Symbola's
                                                                            // 'upsilon
                                                                            // with a
                                                                            // hook' is
                                                                            // a capital
                                                                            // Y without
                                                                            // hooks :(

        LatexCmds['Γ'] =  bind(VanillaSymbol,'Γ','&Gamma;');
        LatexCmds['Δ'] =  bind(VanillaSymbol,'Δ','&Delta;');
        LatexCmds['Θ'] =  bind(VanillaSymbol,'Θ','&Theta;');
        LatexCmds['Λ'] =  bind(VanillaSymbol,'Λ','&Lambda;');
        LatexCmds['Ξ'] =  bind(VanillaSymbol,'Ξ','&Xi;');
        LatexCmds['Π'] =  bind(VanillaSymbol,'Π','&Pi;');
        LatexCmds['Σ'] =  bind(VanillaSymbol,'Σ','&Sigma;');
        LatexCmds['Φ'] =  bind(VanillaSymbol,'Φ','&Phi;');
        LatexCmds['Ψ'] =  bind(VanillaSymbol,'Ψ','&Psi;');
        LatexCmds['Ω'] =  bind(VanillaSymbol,'Ω','&Omega;');

        // other symbols with the same LaTeX command and HTML character entity reference
        /*
         * LatexCmds.Gamma = LatexCmds.Delta = LatexCmds.Theta = LatexCmds.Lambda =
         * LatexCmds.Xi = LatexCmds.Pi = LatexCmds.Sigma = LatexCmds.Phi = LatexCmds.Psi =
         * LatexCmds.Omega = LatexCmds.forall = P(VanillaSymbol, function(_, _super) {
         * _.init = function(latex) { _super.init.call(this,'\\'+latex+'
         * ','&'+latex+';'); }; });
         */

        // symbols that aren't a single MathCommand, but are instead a whole
        // Fragment. Creates the Fragment from a LaTeX string
        var LatexFragment = P(MathCommand, function(_) {
          _.init = function(latex) { this.latex = latex; };
          _.createBefore = function(cursor) { cursor.writeLatex(this.latex); };
          _.parser = function() {
            var frag = latexMathParser.parse(this.latex).children();
            return Parser.succeed(frag);
          };
        });

        // for what seems to me like [stupid reasons][1], Unicode provides
        // subscripted and superscripted versions of all ten Arabic numerals,
        // as well as [so-called "vulgar fractions"][2].
        // Nobody really cares about most of them, but some of them actually
        // predate Unicode, dating back to [ISO-8859-1][3], apparently also
        // known as "Latin-1", which among other things [Windows-1252][4]
        // largely coincides with, so Microsoft Word sometimes inserts them
        // and they get copy-pasted into MathQuill.
        //
        // (Irrelevant but funny story: Windows-1252 is actually a strict
        // superset of the "closely related but distinct"[3] "ISO 8859-1" --
        // see the lack of a dash after "ISO"? Completely different character
        // set, like elephants vs elephant seals, or "Zombies" vs "Zombie
        // Redneck Torture Family". What kind of idiot would get them confused.
        // People in fact got them confused so much, it was so common to
        // mislabel Windows-1252 text as ISO-8859-1, that most modern web
        // browsers and email clients treat the MIME charset of ISO-8859-1
        // as actually Windows-1252, behavior now standard in the HTML5 spec.)
        //
        // [1]: http://en.wikipedia.org/wiki/Unicode_subscripts_and_superscripts
        // [2]: http://en.wikipedia.org/wiki/Number_Forms
        // [3]: http://en.wikipedia.org/wiki/ISO/IEC_8859-1
        // [4]: http://en.wikipedia.org/wiki/Windows-1252
        LatexCmds['¹'] = bind(LatexFragment, '^1');
        LatexCmds['²'] = bind(LatexFragment, '^2');
        LatexCmds['³'] = bind(LatexFragment, '^3');
        LatexCmds['¼'] = bind(LatexFragment, '\\frac14');
        LatexCmds['½'] = bind(LatexFragment, '\\frac12');
        LatexCmds['¾'] = bind(LatexFragment, '\\frac34');

        var BinaryOperator = P(Symbol, function(_, _super) {
          _.init = function(ctrlSeq, html, text) {
            _super.init.call(this,
              ctrlSeq, '<span class="binary-operator">'+html+'</span>', text
            );
          };
        });
        var ItalicOperator = P(Symbol, function(_, _super) {
              _.init = function(ctrlSeq, html, text) {
                _super.init.call(this,
                  ctrlSeq, '<span class="italic-operator">'+html+'</span>', text
                );
              };
            });

        var PlusMinus = P(BinaryOperator, function(_) {
          _.init = VanillaSymbol.prototype.init;

          _.respace = function() {
            if (!this.prev) {
              this.jQ[0].className = '';
            }
            else if (
              this.prev instanceof BinaryOperator &&
              this.next && !(this.next instanceof BinaryOperator)
            ) {
              this.jQ[0].className = 'unary-operator';
            }
            else {
              this.jQ[0].className = 'binary-operator';
            }
            return this;
          };
        });

        LatexCmds['+'] = bind(PlusMinus, '+', '+');
        // yes, these are different dashes, I think one is an en dash and the other is a
        // hyphen
        LatexCmds['–'] = LatexCmds['-'] = bind(PlusMinus, '-', '&minus;');
        LatexCmds['±'] = LatexCmds.pm = LatexCmds.plusmn = LatexCmds.plusminus =
          bind(PlusMinus,'±','&plusmn;');
        LatexCmds.mp = LatexCmds.mnplus = LatexCmds.minusplus =
          bind(PlusMinus,'\\mp ','&#8723;');

        /* CharCmds['*'] = */ LatexCmds.sdot = LatexCmds.cdot =
          bind(BinaryOperator, '\\cdot ', '&middot;');
        // semantically should be &sdot;, but &middot; looks better

        LatexCmds['='] = bind(BinaryOperator, '=', '=');
        LatexCmds['<'] = bind(BinaryOperator, '<', '&lt;');
        LatexCmds['>'] = bind(BinaryOperator, '>', '&gt;');
        LatexCmds['∉'] = bind(BinaryOperator, '∉', '&notin;');

        LatexCmds.notin =
        LatexCmds.sim =
        LatexCmds.cong =
        LatexCmds.equiv =
        LatexCmds.oplus =
        LatexCmds.otimes = P(BinaryOperator, function(_, _super) {
          _.init = function(latex) {
            _super.init.call(this, '\\'+latex+' ', '&'+latex+';');
          };
        });

        LatexCmds.times = LatexCmds['×'] = bind(BinaryOperator, '×', '&times;', '[x]');

        LatexCmds['÷'] = LatexCmds.div = LatexCmds.divide = LatexCmds.divides =
          bind(BinaryOperator,'÷','&divide;', '[/]');

        LatexCmds['≠'] = LatexCmds.ne = LatexCmds.neq = bind(BinaryOperator,'≠','&ne;');

        LatexCmds.ast = LatexCmds['∗'] = LatexCmds.star = LatexCmds.loast = LatexCmds.lowast =
          bind(BinaryOperator,'∗','&lowast;');
          // case 'there4 = // a special exception for this one, perhaps?
        LatexCmds.therefor = LatexCmds.therefore = LatexCmds['∴'] =
          bind(BinaryOperator,'∴','&there4;');

        LatexCmds.cuz = // l33t
        LatexCmds.because = LatexCmds['∵'] = bind(BinaryOperator,'∵','&#8757;');

        LatexCmds.prop = LatexCmds.propto = bind(BinaryOperator,'\\propto ','&prop;');

        LatexCmds['≈'] = LatexCmds.asymp = LatexCmds.approx = bind(BinaryOperator,'≈','&asymp;');

        LatexCmds.lt = bind(BinaryOperator,'<','&lt;');

        LatexCmds.gt = bind(BinaryOperator,'>','&gt;');

        LatexCmds['≤'] = LatexCmds.le = LatexCmds.leq = bind(BinaryOperator,'≤','&le;');

        LatexCmds['≥'] = LatexCmds.ge = LatexCmds.geq = bind(BinaryOperator,'≥','&ge;');

        LatexCmds.isin = LatexCmds['in'] = LatexCmds['∈'] = bind(BinaryOperator,'∈','&isin;');

        LatexCmds.ni = LatexCmds.contains = LatexCmds['∋'] = bind(BinaryOperator,'∋','&ni;');

        LatexCmds.notni = LatexCmds.niton = LatexCmds.notcontains = LatexCmds.doesnotcontain =
          bind(BinaryOperator,'\\not\\ni ','&#8716;');

        LatexCmds.sub = LatexCmds.subset = LatexCmds['⊂'] = bind(BinaryOperator,'⊂','&sub;');

        LatexCmds.sup = LatexCmds.supset = LatexCmds['⊃'] = LatexCmds.superset =
          bind(BinaryOperator,'⊃','&sup;');

        LatexCmds.nsub = LatexCmds.notsub =
        LatexCmds.nsubset = LatexCmds.notsubset =
          bind(BinaryOperator,'\\not\\subset ','&#8836;');

        LatexCmds.nsup = LatexCmds.notsup =
        LatexCmds.nsupset = LatexCmds.notsupset =
        LatexCmds.nsuperset = LatexCmds.notsuperset =
          bind(BinaryOperator,'\\not\\supset ','&#8837;');

        LatexCmds.sube = LatexCmds.subeq = LatexCmds.subsete = LatexCmds.subseteq = LatexCmds['⊆'] =
          bind(BinaryOperator,'⊆','&sube;');

        LatexCmds.supe = LatexCmds.supeq =
        LatexCmds.supsete = LatexCmds.supseteq =
        LatexCmds.supersete = LatexCmds.superseteq = LatexCmds['⊇'] =
          bind(BinaryOperator,'⊇','&supe;');

        LatexCmds.nsube = LatexCmds.nsubeq =
        LatexCmds.notsube = LatexCmds.notsubeq =
        LatexCmds.nsubsete = LatexCmds.nsubseteq =
        LatexCmds.notsubsete = LatexCmds.notsubseteq = LatexCmds['⊈'] =
          bind(BinaryOperator,'⊈','&#8840;');

        LatexCmds.nsupe = LatexCmds.nsupeq =
        LatexCmds.notsupe = LatexCmds.notsupeq =
        LatexCmds.nsupsete = LatexCmds.nsupseteq =
        LatexCmds.notsupsete = LatexCmds.notsupseteq =
        LatexCmds.nsupersete = LatexCmds.nsuperseteq =
        LatexCmds.notsupersete = LatexCmds.notsuperseteq = LatexCmds['⊉'] =
          bind(BinaryOperator,'⊉','&#8841;');
        LatexCmds['≡'] = bind(BinaryOperator, '≡', '≡');
        LatexCmds['～'] = bind(BinaryOperator, '～', '～');


        // sum, product, coproduct, integral
        var BigSymbol = P(Symbol, function(_, _super) {
          _.init = function(ch, html) {
            _super.init.call(this, ch, '<big>'+html+'</big>');
          };
        });

        // /LatexCmds['∑'] = LatexCmds.sum = LatexCmds.summation = bind(BigSymbol,'\\sum
        // ','&sum;');
        LatexCmds['∏'] = LatexCmds.prod = LatexCmds.product = bind(BigSymbol,'\\prod ','&prod;');
        LatexCmds.coprod = LatexCmds.coproduct = bind(BigSymbol,'\\coprod ','&#8720;');
        LatexCmds['∫'] = LatexCmds['int'] = LatexCmds.integral = bind(BigSymbol,'\\int ','&int;');
        LatexCmds['∮'] = LatexCmds['oint'] = LatexCmds.oint = bind(BigSymbol,'\\oint ','&#8750;');


        /*
        // the canonical sets of numbers
        LatexCmds.N = LatexCmds.naturals = LatexCmds.Naturals =
          bind(VanillaSymbol,'\\mathbb{N}','&#8469;');

        LatexCmds.P =
        LatexCmds.primes = LatexCmds.Primes =
        LatexCmds.projective = LatexCmds.Projective =
        LatexCmds.probability = LatexCmds.Probability =
          bind(VanillaSymbol,'\\mathbb{P}','&#8473;');

        LatexCmds.Z = LatexCmds.integers = LatexCmds.Integers =
          bind(VanillaSymbol,'\\mathbb{Z}','&#8484;');

        LatexCmds.Q = LatexCmds.rationals = LatexCmds.Rationals =
          bind(VanillaSymbol,'\\mathbb{Q}','&#8474;');

        LatexCmds.R = LatexCmds.reals = LatexCmds.Reals =
          bind(VanillaSymbol,'\\mathbb{R}','&#8477;');

        LatexCmds.C =
        LatexCmds.complex = LatexCmds.Complex =
        LatexCmds.complexes = LatexCmds.Complexes =
        LatexCmds.complexplane = LatexCmds.Complexplane = LatexCmds.ComplexPlane =
          bind(VanillaSymbol,'\\mathbb{C}','&#8450;');

        LatexCmds.H = LatexCmds.Hamiltonian = LatexCmds.quaternions = LatexCmds.Quaternions =
          bind(VanillaSymbol,'\\mathbb{H}','&#8461;');
        */

        // spacing
        LatexCmds.quad = LatexCmds.emsp = bind(VanillaSymbol,'\\quad ','    ');
        LatexCmds.qquad = bind(VanillaSymbol,'\\qquad ','        ');
        /*
         * spacing special characters, gonna have to implement this in
         * LatexCommandInput::onText somehow case ',': return VanillaSymbol('\\, ',' ');
         * case ':': return VanillaSymbol('\\: ',' '); case ';': return
         * VanillaSymbol('\\; ',' '); case '!': return Symbol('\\! ','<span
         * style="margin-right:-.2em"></span>');
         */

        // binary operators
        LatexCmds.diamond = bind(VanillaSymbol, '\\diamond ', '&#9671;');
        LatexCmds.bigtriangleup = LatexCmds['△'] = bind(VanillaSymbol, '△', '&#9651;');
        LatexCmds.ominus = bind(VanillaSymbol, '\\ominus ', '&#8854;');
        LatexCmds.uplus = bind(VanillaSymbol, '\\uplus ', '&#8846;');
        LatexCmds.bigtriangledown = bind(VanillaSymbol, '\\bigtriangledown ', '&#9661;');
        LatexCmds.sqcap = bind(VanillaSymbol, '\\sqcap ', '&#8851;');
        LatexCmds.triangleleft = bind(VanillaSymbol, '\\triangleleft ', '&#8882;');
        LatexCmds.sqcup = bind(VanillaSymbol, '\\sqcup ', '&#8852;');
        LatexCmds.triangleright = bind(VanillaSymbol, '\\triangleright ', '&#8883;');
        LatexCmds.odot = bind(VanillaSymbol, '\\odot ', '&#8857;');
        LatexCmds.bigcirc = bind(VanillaSymbol, '\\bigcirc ', '&#9711;');
        LatexCmds.dagger = bind(VanillaSymbol, '\\dagger ', '&#0134;');
        LatexCmds.ddagger = bind(VanillaSymbol, '\\ddagger ', '&#135;');
        LatexCmds.wr = bind(VanillaSymbol, '\\wr ', '&#8768;');
        LatexCmds.amalg = bind(VanillaSymbol, '\\amalg ', '&#8720;');

        // relationship symbols
        LatexCmds.models = bind(VanillaSymbol, '\\models ', '&#8872;');
        LatexCmds.prec = bind(VanillaSymbol, '\\prec ', '&#8826;');
        LatexCmds.succ = bind(VanillaSymbol, '\\succ ', '&#8827;');
        LatexCmds.preceq = bind(VanillaSymbol, '\\preceq ', '&#8828;');
        LatexCmds.succeq = bind(VanillaSymbol, '\\succeq ', '&#8829;');
        LatexCmds.simeq = bind(VanillaSymbol, '\\simeq ', '&#8771;');
        LatexCmds.mid = bind(VanillaSymbol, '\\mid ', '&#8739;');
        LatexCmds.ll = bind(VanillaSymbol, '\\ll ', '&#8810;');
        LatexCmds.gg = bind(VanillaSymbol, '\\gg ', '&#8811;');
        LatexCmds.parallel = bind(VanillaSymbol, '\\parallel ', '&#8741;');
        LatexCmds.bowtie = bind(VanillaSymbol, '\\bowtie ', '&#8904;');
        LatexCmds.sqsubset = bind(VanillaSymbol, '\\sqsubset ', '&#8847;');
        LatexCmds.sqsupset = bind(VanillaSymbol, '\\sqsupset ', '&#8848;');
        LatexCmds.smile = bind(VanillaSymbol, '\\smile ', '&#8995;');
        LatexCmds.sqsubseteq = bind(VanillaSymbol, '\\sqsubseteq ', '&#8849;');
        LatexCmds.sqsupseteq = bind(VanillaSymbol, '\\sqsupseteq ', '&#8850;');
        LatexCmds.doteq = bind(VanillaSymbol, '\\doteq ', '&#8784;');
        LatexCmds.frown = bind(VanillaSymbol, '\\frown ', '&#8994;');
        LatexCmds.vdash = bind(VanillaSymbol, '\\vdash ', '&#8870;');
        LatexCmds.dashv = bind(VanillaSymbol, '\\dashv ', '&#8867;');

        // arrows
        LatexCmds.longleftarrow = bind(VanillaSymbol, '\\longleftarrow ', '&#8592;');
        LatexCmds.longrightarrow = bind(VanillaSymbol, '\\longrightarrow ', '&#8594;');
        LatexCmds.Longleftarrow = bind(VanillaSymbol, '\\Longleftarrow ', '&#8656;');
        LatexCmds.Longrightarrow = bind(VanillaSymbol, '\\Longrightarrow ', '&#8658;');
        LatexCmds.longleftrightarrow = bind(VanillaSymbol, '\\longleftrightarrow ', '&#8596;');
        LatexCmds.updownarrow = bind(VanillaSymbol, '\\updownarrow ', '&#8597;');
        LatexCmds.Longleftrightarrow = bind(VanillaSymbol, '\\Longleftrightarrow ', '&#8660;');
        LatexCmds.Updownarrow = bind(VanillaSymbol, '\\Updownarrow ', '&#8661;');
        LatexCmds.mapsto = LatexCmds['↦'] = bind(VanillaSymbol, '↦', '&#8614;');
        LatexCmds.nearrow = bind(VanillaSymbol, '\\nearrow ', '&#8599;');
        LatexCmds.hookleftarrow = bind(VanillaSymbol, '\\hookleftarrow ', '&#8617;');
        LatexCmds.hookrightarrow = bind(VanillaSymbol, '\\hookrightarrow ', '&#8618;');
        LatexCmds.searrow = bind(VanillaSymbol, '\\searrow ', '&#8600;');
        LatexCmds.leftharpoonup = bind(VanillaSymbol, '\\leftharpoonup ', '&#8636;');
        LatexCmds.rightharpoonup = bind(VanillaSymbol, '\\rightharpoonup ', '&#8640;');
        LatexCmds.swarrow = bind(VanillaSymbol, '\\swarrow ', '&#8601;');
        LatexCmds.leftharpoondown = bind(VanillaSymbol, '\\leftharpoondown ', '&#8637;');
        LatexCmds.rightharpoondown = bind(VanillaSymbol, '\\rightharpoondown ', '&#8641;');
        LatexCmds.nwarrow = bind(VanillaSymbol, '\\nwarrow ', '&#8598;');

        // Misc
        LatexCmds.ldots = bind(VanillaSymbol, '\\ldots ', '&#8230;');
        LatexCmds.cdots = bind(VanillaSymbol, '\\cdots ', '&#8943;');
        LatexCmds.vdots = bind(VanillaSymbol, '\\vdots ', '&#8942;');
        LatexCmds.ddots = bind(VanillaSymbol, '\\ddots ', '&#8944;');
        LatexCmds.surd = bind(VanillaSymbol, '\\surd ', '&#8730;');
        LatexCmds.triangle = bind(VanillaSymbol, '\\triangle ', '&#9653;');
        LatexCmds.ell = bind(VanillaSymbol, '\\ell ', '&#8467;');
        LatexCmds.top = bind(VanillaSymbol, '\\top ', '&#8868;');
        LatexCmds.flat = bind(VanillaSymbol, '\\flat ', '&#9837;');
        LatexCmds.natural = bind(VanillaSymbol, '\\natural ', '&#9838;');
        LatexCmds.sharp = bind(VanillaSymbol, '\\sharp ', '&#9839;');
        LatexCmds.wp = bind(VanillaSymbol, '\\wp ', '&#8472;');
        LatexCmds.bot = bind(VanillaSymbol, '\\bot ', '&#8869;');
        LatexCmds.clubsuit = bind(VanillaSymbol, '\\clubsuit ', '&#9827;');
        LatexCmds.diamondsuit = bind(VanillaSymbol, '\\diamondsuit ', '&#9826;');
        LatexCmds.heartsuit = bind(VanillaSymbol, '\\heartsuit ', '&#9825;');
        LatexCmds.spadesuit = bind(VanillaSymbol, '\\spadesuit ', '&#9824;');

        // variable-sized
        // /LatexCmds.oint = bind(VanillaSymbol, '\\oint ', '&#8750;');
        LatexCmds.bigcap = bind(VanillaSymbol, '\\bigcap ', '&#8745;');
        LatexCmds.bigcup = bind(VanillaSymbol, '\\bigcup ', '&#8746;');
        LatexCmds.bigsqcup = bind(VanillaSymbol, '\\bigsqcup ', '&#8852;');
        LatexCmds.bigvee = bind(VanillaSymbol, '\\bigvee ', '&#8744;');
        LatexCmds.bigwedge = bind(VanillaSymbol, '\\bigwedge ', '&#8743;');
        LatexCmds.bigodot = bind(VanillaSymbol, '\\bigodot ', '&#8857;');
        LatexCmds.bigotimes = bind(VanillaSymbol, '\\bigotimes ', '&#8855;');
        LatexCmds.bigoplus = bind(VanillaSymbol, '\\bigoplus ', '&#8853;');
        LatexCmds.biguplus = bind(VanillaSymbol, '\\biguplus ', '&#8846;');

        // delimiters
        LatexCmds.lfloor = bind(VanillaSymbol, '\\lfloor ', '&#8970;');
        LatexCmds.rfloor = bind(VanillaSymbol, '\\rfloor ', '&#8971;');
        LatexCmds.lceil = bind(VanillaSymbol, '\\lceil ', '&#8968;');
        LatexCmds.rceil = bind(VanillaSymbol, '\\rceil ', '&#8969;');
        LatexCmds.slash = bind(VanillaSymbol, '\\slash ', '&#47;');
        LatexCmds.opencurlybrace = bind(VanillaSymbol, '\\opencurlybrace ', '&#123;');
        LatexCmds.closecurlybrace = bind(VanillaSymbol, '\\closecurlybrace ', '&#125;');

        // various symbols

        LatexCmds.caret = bind(VanillaSymbol,'\\caret ','^');
        LatexCmds.underscore = bind(VanillaSymbol,'\\underscore ','_');
        LatexCmds.backslash = bind(VanillaSymbol,'\\backslash ','\\');
        LatexCmds.vert = bind(VanillaSymbol,'|');
        LatexCmds.perp = LatexCmds.perpendicular = bind(VanillaSymbol,'\\perp ','&perp;');
        LatexCmds.nabla = LatexCmds.del = bind(VanillaSymbol,'\\nabla ','&nabla;');
        LatexCmds.hbar = bind(VanillaSymbol,'\\hbar ','&#8463;');

        LatexCmds.AA = LatexCmds.Angstrom = LatexCmds.angstrom =
          bind(VanillaSymbol,'\\text\\AA ','&#8491;');

        LatexCmds.ring = LatexCmds.circ = LatexCmds.circle =
          bind(VanillaSymbol,'\\circ ','&#8728;');

        LatexCmds.bull = LatexCmds.bullet = bind(VanillaSymbol,'\\bullet ','&bull;');

        LatexCmds.setminus = LatexCmds.smallsetminus =
          bind(VanillaSymbol,'\\setminus ','&#8726;');

        LatexCmds.not = // bind(Symbol,'\\not ','<span class="not">/</span>');
        LatexCmds['¬'] = LatexCmds.neg = bind(VanillaSymbol,'\\neg ','&not;');

        LatexCmds['…'] = LatexCmds.dots = LatexCmds.ellip = LatexCmds.hellip =
        LatexCmds.ellipsis = LatexCmds.hellipsis =
          bind(VanillaSymbol,'\\dots ','&hellip;');

        LatexCmds.converges =
        LatexCmds.darr = LatexCmds.dnarr = LatexCmds.dnarrow = LatexCmds.downarrow =
          bind(VanillaSymbol,'\\downarrow ','&darr;');

        LatexCmds.dArr = LatexCmds.dnArr = LatexCmds.dnArrow = LatexCmds.Downarrow =
          bind(VanillaSymbol,'\\Downarrow ','&dArr;');

        LatexCmds.diverges = LatexCmds.uarr = LatexCmds.uparrow =
          bind(VanillaSymbol,'\\uparrow ','&uarr;');

        LatexCmds.uArr = LatexCmds.Uparrow = bind(VanillaSymbol,'\\Uparrow ','&uArr;');

        LatexCmds.to = bind(BinaryOperator,'\\to ','&rarr;');

        LatexCmds.rarr = LatexCmds.rightarrow = LatexCmds['→'] = bind(VanillaSymbol,'→','&rarr;');

        LatexCmds.implies = bind(BinaryOperator,'\\Rightarrow ','&rArr;');

        LatexCmds.rArr = LatexCmds.Rightarrow = LatexCmds['⇒'] = bind(VanillaSymbol,'⇒','&rArr;');

        LatexCmds.gets = bind(BinaryOperator,'\\gets ','&larr;');

        LatexCmds.larr = LatexCmds.leftarrow = LatexCmds['←'] = bind(VanillaSymbol,'←','&larr;');

        LatexCmds.impliedby = bind(BinaryOperator,'\\Leftarrow ','&lArr;');

        LatexCmds.lArr = LatexCmds.Leftarrow = LatexCmds['⇐'] = bind(VanillaSymbol,'⇐','&lArr;');

        LatexCmds.harr = LatexCmds.lrarr = LatexCmds.leftrightarrow = LatexCmds['↔'] = bind(VanillaSymbol,'↔','&harr;');

        LatexCmds.para = LatexCmds.Parallelogram = LatexCmds['□'] = bind(ItalicOperator,'□','□');

        LatexCmds.coc = LatexCmds.CircleCenter = LatexCmds['⊙'] = bind(BinaryOperator,'⊙','⊙');

        LatexCmds.iff = bind(BinaryOperator,'\\Leftrightarrow ','&hArr;');

        LatexCmds.hArr = LatexCmds.lrArr = LatexCmds.Leftrightarrow = LatexCmds['⇔'] = bind(VanillaSymbol,'⇔','&hArr;');

        LatexCmds.Re = LatexCmds.Real = LatexCmds.real = bind(VanillaSymbol,'\\Re ','&real;');

        LatexCmds.Im = LatexCmds.imag =
        LatexCmds.image = LatexCmds.imagin = LatexCmds.imaginary = LatexCmds.Imaginary =
          bind(VanillaSymbol,'\\Im ','&image;');

        LatexCmds.part = LatexCmds.partial = bind(VanillaSymbol,'\\partial ','&part;');

        LatexCmds.inf = LatexCmds.infin = LatexCmds.infty = LatexCmds.infinity = LatexCmds['∞']  =
          bind(VanillaSymbol,'∞','&infin;');

        LatexCmds.alef = LatexCmds.alefsym = LatexCmds.aleph = LatexCmds.alephsym =
          bind(VanillaSymbol,'\\aleph ','&alefsym;');

        LatexCmds.xist = // LOL
        LatexCmds.xists = LatexCmds.exist = LatexCmds.exists =
          bind(VanillaSymbol,'\\exists ','&exist;');

        LatexCmds.and = LatexCmds.land = LatexCmds.wedge =
          bind(VanillaSymbol,'\\wedge ','&and;');

        LatexCmds.or = LatexCmds.lor = LatexCmds.vee = bind(VanillaSymbol,'\\vee ','&or;');

        //LatexCmds.o = LatexCmds.O =
        LatexCmds.empty = LatexCmds.emptyset =
        LatexCmds.oslash = LatexCmds.Oslash =
        LatexCmds.nothing = LatexCmds.varnothing =
          bind(BinaryOperator,'\\varnothing ','&empty;');

        LatexCmds.cup = LatexCmds.union = LatexCmds['∪'] = bind(BinaryOperator,'∪','&cup;');

        LatexCmds.cap = LatexCmds.intersect = LatexCmds.intersection = LatexCmds['∩'] =
          bind(BinaryOperator,'∩','&cap;');

        LatexCmds.deg = LatexCmds.degree = bind(VanillaSymbol,'^\\circ ','&deg;');

        LatexCmds.ang = LatexCmds.angle = bind(VanillaSymbol,'\\angle ','&ang;');


        var NonItalicizedFunction = P(Symbol, function(_, _super) {
          _.init = function(fn) {
            _super.init.call(this, '\\'+fn+' ', '<span>'+fn+'</span>');
          };
          _.respace = function()
          {
            this.jQ[0].className =
              (this.next instanceof SupSub || this.next instanceof Bracket) ?
              '' : 'non-italicized-function';
          };
        });

        // LatexCmds.ln =
        LatexCmds.lg =
        // LatexCmds.log =
        LatexCmds.span =
        LatexCmds.proj =
        LatexCmds.det =
        LatexCmds.dim =
        LatexCmds.min =
        LatexCmds.max =
        LatexCmds.mod =
        LatexCmds.lcm =
        LatexCmds.gcd =
        LatexCmds.gcf =
        LatexCmds.hcf =
        LatexCmds.lim = NonItalicizedFunction;

        (function() {
          var trig = ['sin', 'cos', 'tan', 'sec', 'cosec', 'csc', 'cotan', 'cot'];
          for (var i in trig) {
            LatexCmds[trig[i]] =
            LatexCmds[trig[i]+'h'] =
            LatexCmds['a'+trig[i]] = LatexCmds['arc'+trig[i]] =
            LatexCmds['a'+trig[i]+'h'] = LatexCmds['arc'+trig[i]+'h'] =
              NonItalicizedFunction;
          }
        }());

        // Parser MathCommand
        var latexMathParser = (function() {
          function commandToBlock(cmd) {
            var block = MathBlock();
            cmd.adopt(block, 0, 0);
            return block;
          }
          function joinBlocks(blocks) {
            var firstBlock = blocks[0] || MathBlock();

            for (var i = 1; i < blocks.length; i += 1) {
              blocks[i].children().adopt(firstBlock, firstBlock.lastChild, 0);
            }

            return firstBlock;
          }

          var string = Parser.string;
          var regex = Parser.regex;
          var letter = Parser.letter;
          var any = Parser.any;
          var optWhitespace = Parser.optWhitespace;
          var succeed = Parser.succeed;
          var fail = Parser.fail;

          // Parsers yielding MathCommands
          var variable = letter.map(Variable);
          var symbol = regex(/^[^${}\\_^]/).map(VanillaSymbol);

          var controlSequence =
            regex(/^[^\\]/)
            .or(string('\\').then(
              regex(/^[a-z]+/i)
              .or(regex(/^\s+/).result(' '))
              .or(any)
            )).then(function(ctrlSeq) {
              var cmdKlass = LatexCmds[ctrlSeq];

              if (cmdKlass) {
                return cmdKlass(ctrlSeq).parser();
              }
              else {
                return fail('unknown command: \\'+ctrlSeq);
              }
            })
          ;

          var command =
            controlSequence
            .or(variable)
            .or(symbol)
          ;

          // Parsers yielding MathBlocks
          var mathGroup = string('{').then(function() { return mathSequence; }).skip(string('}'));
          var mathBlock = optWhitespace.then(mathGroup.or(command.map(commandToBlock)));
          var mathSequence = mathBlock.many().map(joinBlocks).skip(optWhitespace);

          var optMathBlock =
            string('[').then(
              mathBlock.then(function(block) {
                return block.join('latex') !== ']' ? succeed(block) : fail();
              })
              .many().map(joinBlocks).skip(optWhitespace)
            ).skip(string(']'))
          ;

          var latexMath = mathSequence;

          latexMath.block = mathBlock;
          latexMath.optBlock = optMathBlock;
          return latexMath;
        })();
        /*******************************************************************************
         * Cursor and Selection "singleton" classes
         ******************************************************************************/

        /*
         * The main thing that manipulates the Math DOM. Makes sure to manipulate the
         * HTML DOM to match.
         */

        /*
         * Sort of singletons, since there should only be one per editable math textbox,
         * but any one HTML document can contain many such textboxes, so any one JS
         * environment could actually contain many instances.
         */

        // A fake cursor in the fake textbox that the math is rendered in.
        var Cursor = P(function(_) {
          _.init = function(root) {
            this.parent = this.root = root;
            var jQ = this.jQ = this._jQ = $('<span class="cursor">&zwj;</span>');

            // closured for setInterval
            this.blink = function(){ jQ.toggleClass('blink'); }

            this.upDownCache = {};
          };

          _.prev = 0;
          _.next = 0;
          _.parent = 0;
          _.show = function() {
            this.jQ = this._jQ.removeClass('blink');
            if ('intervalId' in this) // already was shown, just restart interval
              clearInterval(this.intervalId);
            else { // was hidden and detached, insert this.jQ back into HTML DOM
              if (this.next) {
                if (this.selection && this.selection.first.prev === this.prev)
                  this.jQ.insertBefore(this.selection.jQ);
                else
                  this.jQ.insertBefore(this.next.jQ.first());
              }
              else
                this.jQ.appendTo(this.parent.jQ);
              this.parent.focus();
            }
            this.intervalId = setInterval(this.blink, 500);
            return this;
          };
          _.hide = function() {
            if ('intervalId' in this)
              clearInterval(this.intervalId);
            delete this.intervalId;
            this.jQ.detach();
            this.jQ = $();
            return this;
          };
          _.insertAt = function(parent, prev, next) {
            var old_parent = this.parent;

            this.parent = parent;
            this.prev = prev;
            this.next = next;

            old_parent.blur(); // blur may need to know cursor's destination
          };
          _.insertBefore = function(el) {
            this.insertAt(el.parent, el.prev, el)
            this.parent.jQ.addClass('hasCursor');
            this.jQ.insertBefore(el.jQ.first());
            return this;
          };
          _.insertAfter = function(el) {
            this.insertAt(el.parent, el, el.next);
            this.parent.jQ.addClass('hasCursor');
            this.jQ.insertAfter(el.jQ.last());
            return this;
          };
          _.prependTo = function(el) {
            this.insertAt(el, 0, el.firstChild);
            if (el.textarea) // never insert before textarea
              this.jQ.insertAfter(el.textarea);
            else
              this.jQ.prependTo(el.jQ);
            el.focus();
            return this;
          };
          _.appendTo = function(el) {
            this.insertAt(el, el.lastChild, 0);
            this.jQ.appendTo(el.jQ);
            el.focus();
            return this;
          };
          _.hopLeft = function() {
            this.jQ.insertBefore(this.prev.jQ.first());
            this.next = this.prev;
            this.prev = this.prev.prev;
            return this;
          };
          _.hopRight = function() {
            this.jQ.insertAfter(this.next.jQ.last());
            this.prev = this.next;
            this.next = this.next.next;
            return this;
          };
          _.moveLeftWithin = function(block) {
            if (this.prev) {
              if (this.prev.lastChild) this.appendTo(this.prev.lastChild)
              else this.hopLeft();
            }
            else {
              // we're at the beginning of the containing block, so do nothing.
              if (this.parent === block) return;

              if (this.parent.prev) this.appendTo(this.parent.prev);
              else this.insertBefore(this.parent.parent);
            }
          };
          _.moveRightWithin = function(block) {
            if (this.next) {
              if (this.next.firstChild) this.prependTo(this.next.firstChild)
              else this.hopRight();
            }
            else {
              // we're at the end of the containing block, so do nothing.
              if (this.parent === block) return;

              if (this.parent.next) this.prependTo(this.parent.next);
              else this.insertAfter(this.parent.parent);
            }
          };
          _.moveLeft = function() {
            clearUpDownCache(this);

            if (this.selection)
              this.insertBefore(this.selection.first).clearSelection();
            else {
              this.moveLeftWithin(this.root);
            }
            return this.show();
          };
          _.moveRight = function() {
            clearUpDownCache(this);

            if (this.selection)
              this.insertAfter(this.selection.last).clearSelection();
            else {
              this.moveRightWithin(this.root);
            }
            return this.show();
          };

          /**
             * moveUp and moveDown have almost identical algorithms: - first check next
             * and prev, if so prepend/appendTo them - else check the parent's
             * 'up'/'down' property - if it's a function, call it with the cursor as the
             * sole argument and use the return value.
             *
             * Given undefined, will bubble up to the next ancestor block. Given false,
             * will stop bubbling. Given a MathBlock, + moveUp will appendTo it +
             * moveDown will prependTo it
             *
             */
          _.moveUp = function() { return moveUpDown(this, 'up'); };
          _.moveDown = function() { return moveUpDown(this, 'down'); };
          function moveUpDown(self, dir) {
            if (self.next[dir]) self.prependTo(self.next[dir]);
            else if (self.prev[dir]) self.appendTo(self.prev[dir]);
            else {
              var ancestorBlock = self.parent;
              do {
                var prop = ancestorBlock[dir];
                if (prop) {
                  if (typeof prop === 'function') prop = ancestorBlock[dir](self);
                  if (prop === false || prop instanceof MathBlock) {
                    self.upDownCache[ancestorBlock.id] = { parent: self.parent, prev: self.prev, next: self.next };

                    if (prop instanceof MathBlock) {
                      var cached = self.upDownCache[prop.id];

                      if (cached) {
                        if (cached.next) {
                          self.insertBefore(cached.next);
                        } else {
                          self.appendTo(cached.parent);
                        }
                      } else {
                        var pageX = offset(self).left;
                        self.appendTo(prop);
                        self.seekHoriz(pageX, prop);
                      }
                    }
                    break;
                  }
                }
                ancestorBlock = ancestorBlock.parent.parent;
              } while (ancestorBlock);
            }

            return self.clearSelection().show();
          }

          _.seek = function(target, pageX, pageY) {
            clearUpDownCache(this);
            var cmd, block, cursor = this.clearSelection().show();
            if (target.hasClass('empty')) {
              cursor.prependTo(MathElement[target.attr(mqBlockId)]);
              return cursor;
            }

            cmd = MathElement[target.attr(mqCmdId)];
            if (cmd instanceof Symbol) { // insert at whichever side is closer
              if (target.outerWidth() > 2*(pageX - target.offset().left))
                cursor.insertBefore(cmd);
              else
                cursor.insertAfter(cmd);

              return cursor;
            }
            if (!cmd) {
              block = MathElement[target.attr(mqBlockId)];
              if (!block) { // if no MathQuill data, try parent, if still no, just start
                            // from the root
                target = target.parent();
                cmd = MathElement[target.attr(mqCmdId)];
                if (!cmd) {
                  block = MathElement[target.attr(mqBlockId)];
                  if (!block) block = cursor.root;
                }
              }
            }

            if (cmd)
              cursor.insertAfter(cmd);
            else
              cursor.appendTo(block);

            return cursor.seekHoriz(pageX, cursor.root);
          };
          _.seekHoriz = function(pageX, block) {
            // move cursor to position closest to click
            var cursor = this;
            var dist = offset(cursor).left - pageX;
            var prevDist;

            do {
              cursor.moveLeftWithin(block);
              prevDist = dist;
              dist = offset(cursor).left - pageX;
            }
            while (dist > 0 && (cursor.prev || cursor.parent !== block));

            if (-dist > prevDist) cursor.moveRightWithin(block);

            return cursor;
          };
          function offset(self) {
            // in Opera 11.62, .getBoundingClientRect() and hence jQuery::offset()
            // returns all 0's on inline elements with negative margin-right (like
            // the cursor) at the end of their parent, so temporarily remove the
            // negative margin-right when calling jQuery::offset()
            // Opera bug DSK-360043
            // http://bugs.jquery.com/ticket/11523
            // https://github.com/jquery/jquery/pull/717
            var offset = self.jQ.removeClass('cursor').offset();
            self.jQ.addClass('cursor');
            return offset;
          }
          _.writeLatex = function(latex) {
            var self = this;
            clearUpDownCache(self);
            self.show().deleteSelection();

            var all = Parser.all;
            var eof = Parser.eof;

            var block = latexMathParser.skip(eof).or(all.result(false)).parse(latex);

            if (block) {
              block.children().adopt(self.parent, self.prev, self.next);
              MathElement.jQize(block.join('html')).insertBefore(self.jQ);
              self.prev = block.lastChild;
              block.finalizeInsert();
              self.parent.bubble('redraw');
            }

            return this.hide();
          };
          _.write = function(ch) {
            clearUpDownCache(this);
            return this.show().insertCh(ch);
          };
          _.insertCh = function(ch) {
            var cmd;
            if (ch.match(/^[a-eg-zA-Z]$/)) // exclude f because want florin
              cmd = Variable(ch);
            else if (cmd = CharCmds[ch] || LatexCmds[ch])
              cmd = cmd(ch);
            else
              cmd = VanillaSymbol(ch);

            if (this.selection) {
              this.prev = this.selection.first.prev;
              this.next = this.selection.last.next;
              cmd.replaces(this.selection);
              delete this.selection;
            }

            return this.insertNew(cmd);
          };
          _.insertNew = function(cmd) {
            cmd.createBefore(this);
            return this;
          };
          _.insertCmd = function(latexCmd, replacedFragment) {
            var cmd = LatexCmds[latexCmd];
            if (cmd) {
              cmd = cmd(latexCmd);
              if (replacedFragment) cmd.replaces(replacedFragment);
              this.insertNew(cmd);
            }
            else {
              cmd = TextBlock();
              cmd.replaces(latexCmd);
              cmd.firstChild.focus = function(){ delete this.focus; return this; };
              this.insertNew(cmd).insertAfter(cmd);
              if (replacedFragment)
                replacedFragment.remove();
            }
            return this;
          };
          _.unwrapGramp = function() {
            var gramp = this.parent.parent;
            var greatgramp = gramp.parent;
            var next = gramp.next;
            var cursor = this;

            var prev = gramp.prev;
            gramp.disown().eachChild(function(uncle) {
              if (uncle.isEmpty()) return;

              uncle.children()
                .adopt(greatgramp, prev, next)
                .each(function(cousin) {
                  cousin.jQ.insertBefore(gramp.jQ.first());
                })
              ;

              prev = uncle.lastChild;
            });

            if (!this.next) { // then find something to be next to insertBefore
              if (this.prev)
                this.next = this.prev.next;
              else {
                while (!this.next) {
                  this.parent = this.parent.next;
                  if (this.parent)
                    this.next = this.parent.firstChild;
                  else {
                    this.next = gramp.next;
                    this.parent = greatgramp;
                    break;
                  }
                }
              }
            }
            if (this.next)
              this.insertBefore(this.next);
            else
              this.appendTo(greatgramp);

            gramp.jQ.remove();

            if (gramp.prev)
              gramp.prev.respace();
            if (gramp.next)
              gramp.next.respace();
          };
          _.backspace = function() {
            clearUpDownCache(this);
            this.show();

            if (this.deleteSelection()); // pass
            else if (this.prev) {
              if (this.prev.isEmpty())
                this.prev = this.prev.remove().prev;
              else
                this.selectLeft();
            }
            else if (this.parent !== this.root) {
              if (this.parent.parent.isEmpty())
                return this.insertAfter(this.parent.parent).backspace();
              else
                this.unwrapGramp();
            }

            if (this.prev)
              this.prev.respace();
            if (this.next)
              this.next.respace();
            this.parent.bubble('redraw');

            return this;
          };
          _.deleteForward = function() {
            clearUpDownCache(this);
            this.show();

            if (this.deleteSelection()); // pass
            else if (this.next) {
              if (this.next.isEmpty())
                this.next = this.next.remove().next;
              else
                this.selectRight();
            }
            else if (this.parent !== this.root) {
              if (this.parent.parent.isEmpty())
                return this.insertBefore(this.parent.parent).deleteForward();
              else
                this.unwrapGramp();
            }

            if (this.prev)
              this.prev.respace();
            if (this.next)
              this.next.respace();
            this.parent.bubble('redraw');

            return this;
          };
          _.selectFrom = function(anticursor) {
            // find ancestors of each with common parent
            var oneA = this, otherA = anticursor; // one ancestor, the other ancestor
            loopThroughAncestors: while (true) {
              for (var oneI = this; oneI !== oneA.parent.parent; oneI = oneI.parent.parent) // one
                                                                                            // intermediate,
                                                                                            // the
                                                                                            // other
                                                                                            // intermediate
                if (oneI.parent === otherA.parent) {
                  left = oneI;
                  right = otherA;
                  break loopThroughAncestors;
                }

              for (var otherI = anticursor; otherI !== otherA.parent.parent; otherI = otherI.parent.parent)
                if (oneA.parent === otherI.parent) {
                  left = oneA;
                  right = otherI;
                  break loopThroughAncestors;
                }

              if (oneA.parent.parent)
                oneA = oneA.parent.parent;
              if (otherA.parent.parent)
                otherA = otherA.parent.parent;
            }
            // figure out which is left/prev and which is right/next
            var left, right, leftRight;
            if (left.next !== right) {
              for (var next = left; next; next = next.next) {
                if (next === right.prev) {
                  leftRight = true;
                  break;
                }
              }
              if (!leftRight) {
                leftRight = right;
                right = left;
                left = leftRight;
              }
            }
            this.hide().selection = Selection(left.prev.next || left.parent.firstChild, right.next.prev || right.parent.lastChild);
            this.insertAfter(right.next.prev || right.parent.lastChild);
            this.root.selectionChanged();
          };
          _.selectLeft = function() {
            clearUpDownCache(this);
            if (this.selection) {
              if (this.selection.first === this.next) { // if cursor is at left edge of
                                                        // selection;
                if (this.prev) // then extend left if possible
                  this.hopLeft().selection.extendLeft();
                else if (this.parent !== this.root) // else level up if possible
                  this.insertBefore(this.parent.parent).selection.levelUp();
              }
              else { // else cursor is at right edge of selection, retract left if
                        // possible
                this.hopLeft();
                if (this.selection.first === this.selection.last) {
                  this.clearSelection().show(); // clear selection if retracting to
                                                // nothing
                  return; // skip this.root.selectionChanged(), this.clearSelection()
                            // does it anyway
                }
                this.selection.retractLeft();
              }
            }
            else {
              if (this.prev)
                this.hopLeft();
              else // end of a block
                if (this.parent !== this.root)
                  this.insertBefore(this.parent.parent);
                else
                  return;

              this.hide().selection = Selection(this.next);
            }
            this.root.selectionChanged();
          };
          _.selectRight = function() {
            clearUpDownCache(this);
            if (this.selection) {
              if (this.selection.last === this.prev) { // if cursor is at right edge of
                                                        // selection;
                if (this.next) // then extend right if possible
                  this.hopRight().selection.extendRight();
                else if (this.parent !== this.root) // else level up if possible
                  this.insertAfter(this.parent.parent).selection.levelUp();
              }
              else { // else cursor is at left edge of selection, retract right if
                        // possible
                this.hopRight();
                if (this.selection.first === this.selection.last) {
                  this.clearSelection().show(); // clear selection if retracting to
                                                // nothing
                  return; // skip this.root.selectionChanged(), this.clearSelection()
                            // does it anyway
                }
                this.selection.retractRight();
              }
            }
            else {
              if (this.next)
                this.hopRight();
              else // end of a block
                if (this.parent !== this.root)
                  this.insertAfter(this.parent.parent);
                else
                  return;

              this.hide().selection = Selection(this.prev);
            }
            this.root.selectionChanged();
          };

          function clearUpDownCache(self) {
            self.upDownCache = {};
          }

          _.prepareMove = function() {
            clearUpDownCache(this);
            return this.show().clearSelection();
          };

          _.prepareEdit = function() {
            clearUpDownCache(this);
            return this.show().deleteSelection();
          }

          _.clearSelection = function() {
            if (this.selection) {
              this.selection.clear();
              delete this.selection;
              this.root.selectionChanged();
            }
            return this;
          };
          _.deleteSelection = function() {
            if (!this.selection) return false;

            this.prev = this.selection.first.prev;
            this.next = this.selection.last.next;
            this.selection.remove();
            this.root.selectionChanged();
            return delete this.selection;
          };
        });

        var Selection = P(MathFragment, function(_, _super) {
          _.init = function() {
            var frag = this;
            _super.init.apply(frag, arguments);

            frag.jQwrap(frag.jQ);
          };
          _.jQwrap = function(children) {
            this.jQ = children.wrapAll('<span class="selection"></span>').parent();
              // can't do wrapAll(this.jQ = $(...)) because wrapAll will clone it
          };
          _.adopt = function() {
            this.jQ.replaceWith(this.jQ = this.jQ.children());
            return _super.adopt.apply(this, arguments);
          };
          _.clear = function() {
            this.jQ.replaceWith(this.jQ.children());
            return this;
          };
          _.levelUp = function() {
            var seln = this,
              gramp = seln.first = seln.last = seln.last.parent.parent;
            seln.clear().jQwrap(gramp.jQ);
            return seln;
          };
          _.extendLeft = function() {
            this.first = this.first.prev;
            this.first.jQ.prependTo(this.jQ);
          };
          _.extendRight = function() {
            this.last = this.last.next;
            this.last.jQ.appendTo(this.jQ);
          };
          _.retractRight = function() {
            this.first.jQ.insertBefore(this.jQ);
            this.first = this.first.next;
          };
          _.retractLeft = function() {
            this.last.jQ.insertAfter(this.jQ);
            this.last = this.last.prev;
          };
        });
        /*******************************************************************************
         * The actual jQuery plugin and document ready handlers.
         ******************************************************************************/

        // The publicy exposed method of jQuery.prototype, available (and meant to be
        // called) on jQuery-wrapped HTML DOM elements.
        $.fn.mathquill = function(cmd, latex) {
          switch (cmd) {
          case 'redraw':
            return this.each(function() {
              var blockId = $(this).attr(mqBlockId),
                rootBlock = blockId && MathElement[blockId];
              if (rootBlock) {
                (function postOrderRedraw(el) {
                  el.eachChild(postOrderRedraw);
                  if (el.redraw) el.redraw();
                }(rootBlock));
              }
            });
          case 'revert':
            return this.each(function() {
              var blockId = $(this).attr(mqBlockId),
                block = blockId && MathElement[blockId];
              if (block && block.revert)
                block.revert();
            });
          case 'latex':
            if (arguments.length > 1) {
              return this.each(function() {
                var blockId = $(this).attr(mqBlockId),
                  block = blockId && MathElement[blockId];
                if (block)
                  block.renderLatex(latex);
              });
            }

            var blockId = $(this).attr(mqBlockId),
              block = blockId && MathElement[blockId];
            return block && block.latex();
          case 'text':
            var blockId = $(this).attr(mqBlockId),
              block = blockId && MathElement[blockId];
            return block && block.text();
          case 'html':
            return this.html().replace(/ ?hasCursor|hasCursor /, '')
              .replace(/ class=(""|(?= |>))/g, '')
              .replace(/<span class="?cursor( blink)?"?><\/span>/i, '')
              .replace(/<span class="?textarea"?><textarea><\/textarea><\/span>/i, '');
          case 'write':
            if (arguments.length > 1)
              return this.each(function() {
                var blockId = $(this).attr(mqBlockId),
                  block = blockId && MathElement[blockId],
                  cursor = block && block.cursor;

                if (cursor)
                  cursor.writeLatex(latex).parent.blur();
              });
          case 'cmd':
            if (arguments.length > 1)
              return this.each(function() {
                var blockId = $(this).attr(mqBlockId),
                  block = blockId && MathElement[blockId],
                  cursor = block && block.cursor;

                if (cursor) {
                  cursor.show();
                  if (/^\\[a-z]+$/i.test(latex)) {
                    var selection = cursor.selection;
                    if (selection) {
                      cursor.prev = selection.first.prev;
                      cursor.next = selection.last.next;
                      delete cursor.selection;
                    }
                    cursor.insertCmd(latex.slice(1), selection);
                  }
                  else
                    cursor.insertCh(latex);
                  cursor.hide().parent.blur();
                }
              });
          default:
            var textbox = cmd === 'textbox',
              editable = textbox || cmd === 'editable',
              RootBlock = textbox ? RootTextBlock : RootMathBlock;
            return this.each(function() {
              createRoot($(this), RootBlock(), textbox, editable);
            });
          }
        };

        // on document ready, mathquill-ify all `<tag class="mathquill-*">latex</tag>`
        // elements according to their CSS class.
        $(function() {
          $('.mathquill-editable:not(.mathquill-rendered-math)').mathquill('editable');
          $('.mathquill-textbox:not(.mathquill-rendered-math)').mathquill('textbox');
          $('.mathquill-embedded-latex').mathquill();
        });


        }());

    </script>
    <script type="text/javascript"  charset="UTF-8">
        $(document).ready(function(){
	// 初始化公式显示区域
	$(".myeq").each(function(){
		initMyEq(this, true);
	});
});

/**
 * 初始化公式显示区域
 *
 * @param elt
 * @param readonly 前台显示时是否只读
 */
function initMyEq(elt, readonly) {
	$(elt).html(parseDataToHTML($(elt).attr('content'), true, readonly));
	$(elt).removeAttr('content');
}

/**
 * 将HTML解析为数据
 *
 * @param html
 * @param inDiv 是否在可编辑div中
 */
function parseHTMLToData(html, inDiv) {
	var result = "";
	// ie
	html = html.replace(/<p>/gi, '');
	html = html.replace(/<\/p>/gi, '<br>');
	// 去掉末尾<br>标记
	if (html.substring(html.length-4, html.length) == "<br>")
		html = html.substring(0, html.length-4);
	if (inDiv) {
		html = $.trim(html.replace(/&nbsp;/gi, " "));
	}

	//解析html中的数字公式
	var pattern = /<iframe.*?\slatex="(.*?)".*?<\/iframe>/gi; //匹配span开始标记和结束标记
	var posArr = []; //存放匹配元素位置的数组
	var latexArr = []; //存放latex的数组
	var tmpArr = null;
	while ((tmpArr = pattern.exec(html)) != null) {
		posArr.push(tmpArr.index);
		posArr.push(tmpArr.index + tmpArr[0].length);
		latexArr.push(tmpArr[1]);
	}
	posArr.unshift(0);
	posArr.push(html.length);
	for(var i=0,j=0;i<posArr.length;i=i+2,j++){
		result += html.substring(posArr[i],posArr[i+1]);
		if(latexArr[j]){
			result += "|math<|" + latexArr[j] + "|>math|";
		}
	}
	//解析html中的填空标记
	result = result.replace(/<img\s[^>]*?\/block_space.jpg\">/gi, "|space<||>space|");

	return result;
}

/**
 * 将数据解析为HTML
 *
 * @param data 原始值
 * @param isFront 是否用于前台显示
 * @param readonly 前台显示时是否可输入公式
 */
function parseDataToHTML(data, isFront, readonly) {
	if(data==null){
		return "";
	}
	var result = "";
	//找出特殊标志将其放入数组
	var pattern = /\|(math|space)\<\|(.*?)\|\>(math|space)\|/g;
	var specArr = [];
	var tmpArr = null;
	while (tmpArr = pattern.exec(data)) {
		var obj = {};
		obj.type = tmpArr[1];
		obj.originalStr = tmpArr[2];
		obj.startIndex = pattern.lastIndex - RegExp.lastMatch.length;
		obj.endIndex = pattern.lastIndex;
		specArr.push(obj);
	}
	//将特殊标志数组中的数据解析为html代码并存入数组
	var arrHtml = [];
	for (var i = 0; i < specArr.length; i++) {
		var obj = specArr[i];
		var html = null;
		if (obj.type == 'space') {
			if (isFront) {
				if (readonly) {
					html = '<div class="intoText">' + '</div>';
				} else {
					html = '<div contenteditable="true" onclick="getCursorPos(this);showMath(this);" onkeyup="getCursorPos(this);" class="intoText">' + '</div>';
				}
			} else {
				html = '<img src="' + '/ewebeditor/sysimage/editarea/block_space.jpg">';
			}
		} else {
			if (isFront) {
				var $el = $('<span style="vertical-align: middle;"></span>');
				$el.appendTo('body').mathquill().mathquill('write', obj.originalStr);
				html = $el.wrap('<div></div>').parent().html();
				$el.unwrap().remove();
			} else {
				html = createIframeHTML(obj.originalStr);
			}
		}
		arrHtml.push(html)
	}
	//将原数据中公式、空格等特殊标志替换成html并存入返回结果中
	var arrPostion = [];
	arrPostion.push(0);
	for (var i = 0; i < specArr.length; i++) {
		var obj = specArr[i];
		arrPostion.push(obj.startIndex, obj.endIndex);
	}
	arrPostion.push(data.length);
	for (var i = 0; i < arrPostion.length; i=i+2) {
		if (i > 0) {
			result += arrHtml[i/2-1];
		}
		result += data.substring(arrPostion[i], arrPostion[i+1]);
	}
	return result;
}

/**
 * 给指定对象添加公式
 *
 * @param obj
 * @param latex
 * @returns
 */
function addLatex(obj, latex) {
	$('<span></span>').appendTo(obj).mathquill().mathquill('write', latex);
}

/**
 * 获取光标在div中的位置
 */
function getCursorPos(obj) {
	if (!obj.id) {
		obj.id = "_autoid_" + utils.uuid();
	}
	var vname = "_ctrl_" + obj.id;
	if (eval('window.'+vname) == undefined) {
		eval('window.'+vname+'=new cursorControl(document.getElementById("' + obj.id + '"))');
		$(obj).trigger('keyup');
	}
	//ie8下处理
	if ($.browser.msie && parseInt($.browser.version)<=8) {
		var html = $(obj).html();
		if (html.length) {
			var sEnd = html.charAt(html.length-1);
			if (sEnd !== " ") {
				$(obj).append(" ");
			}
			var len = obj.innerText.length + getIFrameCount(obj);
			var pos = getCaretPos(obj) + getIFrameCount(obj);
			if (pos >= len) {
				setCaretPos(obj, len-1);
				$(obj).trigger('keyup');
			}
		}
	}
}

function getIFrameCount(obj) {
	var len = 0;
	var arr = $(obj).html().match(/<\/iframe>|<img/gi);
	 if (arr) {
		len = arr.length;
	 }
	return len;
}

/**
 * 将html字符串插入obj的当前位置
 *
 * @param obj
 * @param html
 */
function insertHTML(obj, html) {
	if (! obj)
		return;
	var vname = "_ctrl_" + obj.id;
	eval(vname+'.insertText(html)');
	setTimeout(function(){
		$(obj).toggleClass("repain");
	}, 500);
};

/**
 * 创建用于显示公式的iframe的HTML字符串
 *
 * @param latex
 * @returns
 */
function createIframeHTML(latex) {
	var frame = document.createElement("iframe");
	frame.setAttribute("frameBorder", "0");
	frame.setAttribute("latex", latex);
	frame.scrolling = "no";
	frame.src = "/myeq/formula.htm?r=" + Math.random();
	frame.style.margin = "0 1px";
	frame.style.padding = "0";
	frame.style.verticalAlign = "middle";
	var div = document.createElement('div');
	div.appendChild(frame);
	return div.innerHTML;
}

/**
 * 调整iframe宽度、高度
 *
 * @param obj
 */
function dynIframeSize(obj) {
	var count = 1;
	setTimeout(function t(){
		count++;
		if (count < 25) {
			var doc = obj.contentDocument || obj.Document;
			if (!obj || !doc) return;
			var box = doc.getElementsByTagName('span')[0].getBoundingClientRect();
			var w = box.right - box.left + 2
			var h = box.bottom - box.top;
			if (obj.width != w)	obj.width = w;
		    if (obj.height != h) obj.height = h;
		    setTimeout(t, 200);
		}
	}, 200);
}

/**
 * 显示段落解析
 *
 * @param obj
 */
function showComment(obj) {
	$(obj).prev().toggle();
}

/**
 * 显示段解、词解
 *
 * @param obj
 */
function showWordexplain(obj) {
	alert($(obj).text() + "--" + $(obj).attr("wordtype") + "--" + $(obj).attr("wordaction"));
}

/**
 * 初始化文章，处理其中的段解、词解
 *
 * @param id
 */
function initArticle(id) {
	var $el = $('#'+id);
	$el.find("table.paragraphcomment").each(function (index) {
		$(this).hide().after("<a href='javascript:void(0)' onclick='javascript:showComment(this);'>【段落解析】</a>");
	});
	$el.find("a.wordexplain").each(function (index) {
		$(this).click(function(){
			showWordexplain(this);
		});
	});
}

function filterPasteText(str) {
    str = str.replace(/\r\n|\n|\r/ig, "");
    str = str.replace(/<(!DOCTYPE)(\n|.)*?>/ig, "");
    str = str.replace(/<\/?\w+\b[^>]*>/ig, "");
    return str;
}

    </script>
</head>
<body>
<div class="ub ub-fh ub-fv ub-ver">
    <div id="content" class="ub ub-f1 ub-ver ub-fh">loading</div>
</div>
</body>
</html>
