<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" dir="ltr">
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

    <link rel="stylesheet" href="file:///android_asset/myeq/mathquill-0.9.1/mathquill.css" >
    <script type="text/javascript" src="file:///android_asset/jquery-1.8.3.min.js"  charset="UTF-8"></script>
    <script type="text/javascript" src="file:///android_asset/myeq/mathquill-0.9.1/mathquill.js"  charset="UTF-8"></script>
    <script type="text/javascript" src="file:///android_asset/myeq/util.js"  charset="UTF-8"></script>
    <style type="text/css">
     .ub
     {
         display: -webkit-box !important;
         display: box !important;
         position:relative;
     }

     .ub-rev
     {
         -webkit-box-direction:reverse;
         box-direction:reverse;
     }

     .ub-fh
     {
         width:100%;
     }

     .ub-fv
     {
         height:100%;
     }

     .ub-ver
     {
         -webkit-box-orient:vertical;
         box-orient:vertical;
     }

     .ub-f1
     {
         position:relative;
         -webkit-box-flex: 1;
         box-flex: 1;
     }
     .uinn
     {
        padding:0.5em;
     }
     .umar-t
     {
        margin-top:1em;
     }
     .color-red
     {
        color:red;
     }
  </style>

  <script type="text/javascript">
        function showMain(dataStr,id,url) {
            var hl = parseHTMLToData(dataStr,true);
            $("#"+id).html(hl);
            $.each($("#"+id).find("img"),function(){
                 $(this).attr("src",url + $(this).attr("src"));
                 imgPx2Em($(this));
            });
        }

        function imgPx2Em(obj) {
            var count = 1;
            setTimeout(function t() {
                count++;
                if (count < 30) {
                    var w = Number($(obj).css('width').substring(0, $(obj).css('width').length - 2));
                    var h = Number($(obj).css('height').substring(0, $(obj).css('height').length - 2));
                    if (w) {
                        var windowWidth=window.screen.availWidth-50;
                        if(w>windowWidth){
                            $(obj).css("width", "100%");
                            $(obj).css("height", "auto");
                            //$(obj).css('height', h * (windowWidth/w) + 'px');
                        }else{
                            $(obj).css('width', w * 0.0625 + 'em');
                            $(obj).css('height', h * 0.0625 + 'em');
                        }
                        $(obj).attr("loaded", "true");
                    } else {
                        setTimeout(t, 100);
                    }
                }
            }, 100);
        }
  </script>
</head>
<body>
<div class="ub ub-fh ub-fv ub-ver">
    <div class="ub ub-ac ub-pc">
        <div class="ub ub-f1 uinn color-red">
            题目
        </div>
    </div>
    <div class="ub ub-ac ub-pc">
        <div id="question" class="ub ub-f1 ub-ver"></div>
    </div>

    <div class="ub ub-ac ub-pc umar-t">
        <div class="ub ub-f1 uinn color-red">
            分析
        </div>
    </div>
    <div class="ub ub-ac ub-pc">
        <div id="answer" class="ub ub-f1 ub-ver"></div>
    </div>

    <div class="ub ub-ac ub-pc umar-t">
        <div class="ub ub-f1 uinn color-red">
            解答
        </div>
    </div>
    <div class="ub ub-ac ub-pc">
        <div id="content" class="ub ub-f1 ub-ver"></div>
    </div>

    <div class="ub ub-ac ub-pc umar-t">
        <div class="ub ub-f1 uinn color-red">
            考点
        </div>
     </div>
    <div class="ub ub-ac ub-pc">
        <div id="know" class="ub ub-f1 ub-ver"></div>
    </div>
</div>
</body>
</html>
