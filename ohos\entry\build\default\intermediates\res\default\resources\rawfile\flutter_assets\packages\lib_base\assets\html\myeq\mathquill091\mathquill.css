/*
 * LaTeX Math in pure HTML and CSS -- No images whatsoever
 * v0.xa
 * by <PERSON> and <PERSON>
 * Lesser GPL Licensed: http: //www.gnu.org/licenses/lgpl.html
 *
 * This file is automatically included by mathquill.js
 *
 */
 /*
@font-face {
  font-family: Symbola;  
}
 */
.mathquill-editable {
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-editable .cursor {
  border-left: 1px solid black;
  margin-right: -1px;
  position: relative;
  z-index: 1;
  padding: 0;
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-editable .cursor.blink {
  visibility: hidden;
}
.mathquill-editable,
.mathquill-embedded-latex .mathquill-editable {
  border: 1px solid gray;
  padding: 8px;
}
.mathquill-embedded-latex .mathquill-editable {
  margin: 1px;
}
.mathquill-editable.hasCursor,
.mathquill-editable .hasCursor {
  -webkit-box-shadow: #68b4df 0 0 3px 2px;
  -moz-box-shadow: #68b4df 0 0 3px 2px;
  box-shadow: #68b4df 0 0 3px 2px;
}
.mathquill-editable .latex-command-input {
  color: inherit;
  font-family: "Courier New", monospace;
  border: 1px solid gray;
  padding-right: 1px;
  margin-right: 1px;
  margin-left: 2px;
}
.mathquill-editable .latex-command-input.empty {
  background: transparent;
}
.mathquill-editable .latex-command-input.hasCursor {
  border-color: ActiveBorder;
}
.mathquill-editable.empty{display:inline-table;overflow:hidden;}
.mathquill-editable.empty:after,
.mathquill-textbox:after,
.mathquill-rendered-math .empty:after {
  visibility: hidden;
  content: 'c';
}
.mathquill-editable .cursor:only-child:after,
.mathquill-editable .textarea + .cursor:last-child:after {
  visibility: hidden;
  content: 'c';
}
.mathquill-textbox {
  overflow-x: auto;
  overflow-y: hidden;
}
.mathquill-rendered-math {
  font-variant: normal;
  font-weight: normal;
  font-style: normal;

  line-height: 1;
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-rendered-math .non-leaf,
.mathquill-rendered-math .scaled {
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-rendered-math var,
.mathquill-rendered-math .text,
.mathquill-rendered-math .nonSymbola {
  font-family: "Times New Roman", serif;
}
.mathquill-rendered-math * {
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  border-color: black;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.mathquill-editable .empty {
  background: #ccc;
}
.mathquill-rendered-math.empty {
  background: transparent;
}
.mathquill-rendered-math .text {
  font-size: 87%;
}
.mathquill-rendered-math .font {
  font: 1em Symbola, "Times New Roman", serif
}
.mathquill-rendered-math .font * {
  font-family: inherit;
  font-style: inherit;
}
.mathquill-rendered-math b,
.mathquill-rendered-math b.font {
  font-weight: bolder;
}
.mathquill-rendered-math var,
.mathquill-rendered-math i,
.mathquill-rendered-math i.font {
  font-style: italic;
}
.mathquill-rendered-math var.florin {
  margin: 0 -0.1em;
}
.mathquill-rendered-math big {
  font-size: 125%;
}
.mathquill-rendered-math .roman {
  font-style: normal;
}
.mathquill-rendered-math .sans-serif {
  font-family: sans-serif, Symbola, serif;
}
.mathquill-rendered-math .monospace {
  font-family: monospace, Symbola, serif;
}
.mathquill-rendered-math .overline {
  border-top: 1px solid black;
  margin-top: 1px;
}
.mathquill-rendered-math .overline {
  border-top: 1px solid black;
  margin-top: 1px;
}
.mathquill-rendered-math .overrightarrow {
	position:relative;
}
.mathquill-rendered-math .overrightarrow-arrow{
	position:absolute;
	top:-1px;
	right:2px;
	height:5px;
	width:3px;
	font-size:0;
	line-height:0;
	background:url(img/symbol_vector_arrow.gif) no-repeat;
}
/**/
.mathquill-rendered-math .underline {
  border-bottom: 1px solid black;
  margin-bottom: 1px;
}
.mathquill-rendered-math .binary-operator {
  padding: 0 0.2em;
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-rendered-math .italic-operator {
  padding: 0 0.2em;
  display: -moz-inline-box;
  display: inline-block;
  font-style:italic;
  font-family:tahoma;
}
.mathquill-rendered-math .unary-operator {
  padding-left: 0.2em;
}
.mathquill-rendered-math sup .binary-operator,
.mathquill-rendered-math sub .binary-operator {
  padding: 0 .1em;
}
.mathquill-rendered-math sup .unary-operator,
.mathquill-rendered-math sub .unary-operator {
  padding-left: .1em;
}
.mathquill-rendered-math sup.limit,
.mathquill-rendered-math sub.limit,
.mathquill-rendered-math sup.nthroot,
.mathquill-rendered-math sub.nthroot {
  /*font-size: 60%;*/
}
.mathquill-rendered-math sup .fraction,
.mathquill-rendered-math sub .fraction {
  vertical-align: -0.4em;
}
.mathquill-rendered-math sup .numerator,
.mathquill-rendered-math sub .numerator {
  padding-bottom: 0;
}
.mathquill-rendered-math sup .denominator,
.mathquill-rendered-math sub .denominator {
  padding-top: 0;
}
.mathquill-rendered-math sup {
  padding-left:3px;
  vertical-align: .4em;
}
.mathquill-rendered-math sup.limit,
.mathquill-rendered-math sup.nthroot {
  vertical-align: .8em;
}
.mathquill-rendered-math sup.nthroot {
  margin-right: -5px;
  margin-left: .2em;
  min-width: .5em;
  margin-right: 0\0;
  margin-left: .2em\0;
}
:root .mathquill-rendered-math sup.nthroot {
  margin-right: -5px\0;
  margin-left: .2em\0;
}
.mathquill-rendered-math sub {
  padding-left:3px;
  vertical-align: -0.4em;
}
.mathquill-rendered-math sub.limit {
  vertical-align: -0.6em;
}
.mathquill-rendered-math .paren {
  padding: 0 .1em;
  vertical-align: bottom;
  -webkit-transform-origin: bottom center;
  -moz-transform-origin: bottom center;
  -ms-transform-origin: bottom center;
  -o-transform-origin: bottom center;
  transform-origin: bottom center;
}
.mathquill-rendered-math .array {
  vertical-align: middle;
  text-align: center;
}
.mathquill-rendered-math .array > span {
  display: block;
}
.mathquill-rendered-math .non-italicized-function {
  font-family: Symbola, "Times New Roman", serif;
  line-height: .9;
  font-style: normal;
  padding-right: .2em;
}
.mathquill-rendered-math .fraction {
  font-size:100%;
  text-align: center;
  vertical-align: -.5em;
}
.mathquill-rendered-math .customl,
.mathquill-rendered-math .customm,
.mathquill-rendered-math .customk,
.mathquill-rendered-math .customj {
  vertical-align: -1em;
}
.mathquill-rendered-math .fraction,
.mathquill-rendered-math x:-moz-any-link {
  display: -moz-groupbox;
}
.mathquill-rendered-math .fraction,
.mathquill-rendered-math x:-moz-any-link,
.mathquill-rendered-math x:default {
  display: inline-block;
}
.mathquill-rendered-math .numerator,
.mathquill-rendered-math .denominator {
  display: block;
}
.mathquill-rendered-math .numerator {
  padding: .1em .1em 0 .1em;
  margin-bottom: 0.1em;
  width:100%;
  margin-left:-0.1em;
}
.mathquill-rendered-math .denominator {
  border-top: 1px solid;
  float: right;
  float: left\9;
  width: 100%;
  padding: .1em .1em 0 .1em;
  margin-right: -0.1em;
  margin-left: -0.1em;
}
.mathquill-rendered-math .sqrt-prefix {
  padding-top: 0;
  position: relative;
  top: .1em;
  top: 0\0; /*ie8 hack*/
  vertical-align: top;
  -webkit-transform-origin: top;
  -moz-transform-origin: top;
  -ms-transform-origin: top;
  -o-transform-origin: top;
  transform-origin: top;
}
:root .mathquill-rendered-math .sqrt-prefix {
  text-align:center;
  min-width:10px;
  top: .1em\0;
}
.mathquill-rendered-math .sqrt-stem {
  border-top: 1px solid;
  margin-top: 1px;
  padding-left: .15em;
  padding-right: .2em;
  margin-right: .1em;
}
.mathquill-rendered-math .overrightarrow-prefix {
  padding-top: 0;
  position: relative;
  top: .1em;
  vertical-align: top;
  -webkit-transform-origin: top;
  -moz-transform-origin: top;
  -ms-transform-origin: top;
  -o-transform-origin: top;
  transform-origin: top;
}
.mathquill-rendered-math .overrightarrow-stem {
  border-top: 1px solid;
  margin-top: 1px;
  padding-left: .15em;
  padding-right: .2em;
  margin-right: .1em;
}
.mathquill-rendered-math,
.mathquill-rendered-math .mathquill-editable {
  cursor: text;
  font-family: Symbola, "Times New Roman", serif;
}
.mathquill-rendered-math .selection,
.mathquill-editable .selection,
.mathquill-rendered-math .selection .non-leaf,
.mathquill-editable .selection .non-leaf,
.mathquill-rendered-math .selection .scaled,
.mathquill-editable .selection .scaled {
  background: #B4D5FE !important;
  background: Highlight !important;
  color: HighlightText;
  border-color: HighlightText;
}
.mathquill-rendered-math .selection .matrixed,
.mathquill-editable .selection .matrixed {
  background: #39F !important;
}
.mathquill-rendered-math .selection .matrixed-container,
.mathquill-editable .selection .matrixed-container {
  filter: progid:DXImageTransform.Microsoft.Chroma(color='#3399FF') !important;
}
.mathquill-rendered-math .selection.blur,
.mathquill-editable .selection.blur,
.mathquill-rendered-math .selection.blur .non-leaf,
.mathquill-editable .selection.blur .non-leaf,
.mathquill-rendered-math .selection.blur .matrixed,
.mathquill-editable .selection.blur .matrixed {
  background: #D4D4D4 !important;
  color: black;
  border-color: black;
}
.mathquill-rendered-math .selection.blur .matrixed-container,
.mathquill-editable .selection.blur .matrixed-container {
  filter: progid:DXImageTransform.Microsoft.Chroma(color='#D4D4D4') !important;
}
.mathquill-editable .textarea,
.mathquill-rendered-math .textarea {
  position: relative;
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
}
.mathquill-editable .textarea textarea,
.mathquill-rendered-math .textarea textarea,
.mathquill-editable .selectable,
.mathquill-rendered-math .selectable {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  position: absolute;
  clip: rect(1em 1em 1em 1em);
}
.mathquill-rendered-math .matrixed {
  background: white;
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-rendered-math .matrixed-container {
  filter: progid:DXImageTransform.Microsoft.Chroma(color='white');
  margin-top: -0.1em;
  margin-top: 0\0; /*ie8 hack*/
  margin-right: -1px\0; /*ie8 hack*/
}
:root .mathquill-rendered-math .matrixed-container{
  margin-top: -0.1em; /*ie9 hack*/
}
/*
 * Author: WuZhuo
 * Date: 2013-01-25
 */
.mathquill-rendered-math sup,
.mathquill-rendered-math sub {
  position: relative;
  font-size: 80%;
  line-height:80%;
}

/* customa 
.customa sub{
	left: 0; 
	margin-right: 0;
}
*/
/* customb 
.customb sup,
.customb sub,
.span_mid{
	position:relative;
	line-height:60%;
}
.customb sup{
	top:-0.5em;
}
.customb .span_mid{
	top:-0.25em;
	left: -0.45368421052631576em; 
	margin-right: -0.37368421052631573em;
}
.customb sub{
	top:0.1em;
	left: -0.47368421052631576em; 
	margin-right: -0.37368421052631573em;
}
*/
/* customc 
.customc sub{
	left: -0.47368421052631576em; 
	margin-right: -0.37368421052631573em;
}
*/
/* End */
.customd .up{
  font-size: 100%;
  line-height:1;
  display:block;
  padding:0 0.3em;
  margin:0 0.2em;
  text-align:center;
}
.customd .down{
  font-size: 100%;
  line-height:1;
  display:block;
  padding: .1em .1em 0 .1em;
  margin: 0.4px 0.1em 0;
  text-align:center;
}
/**/
.custome .up{
  padding-top:2px;
  font-size: 80%;
  line-height:90%;
  display:block;
  text-align:center;
  text-align:left\0;
  height:10px;
  font-weight:700;
}
:root .custome .up{text-align:center;}
.custome .down{
  font-size: 100%;
  line-height:1;
  display:block;
}

.custome .up .matrixed{
  height:10px;
  font-weight:700;
}
/**/
.customf {
	vertical-align:-0.5em;
}
.customf .symbol-lim{
  display:inline-block;
  font-size: 100%;
  line-height:100%;
  text-align:center;
  padding:0.1em 0 0 0.3em;
  margin:0 0.2em 0.1em;
}
.customf .symbol-lim p{
  line-height:90%;
  font-size: 60%;
}
.customf .symbol-lim-bt{
	display:block;
	font-size:80%;
	line-height:80%:
}
.customf span.non-leaf{vertical-align:5px;}
.customj{vertival-align:.5em;}
.customj .customli{text-align:center;}
.mathquill-rendered-math .customk,
.mathquill-rendered-math .customl,
.mathquill-rendered-math .customm{min-width:2em;overflow:hidden;}
.mathquill-rendered-math .customk,
.mathquill-rendered-math .customl{padding:.2em .7em .2em .2em;}
.mathquill-rendered-math .customm{padding:.2em .2em .2em .2em;}
.mathquill-rendered-math .customk .numerator,
.mathquill-rendered-math .customk .denominator,
.mathquill-rendered-math .customl .numerator,
.mathquill-rendered-math .customl .denominator{position:relative;float:none;padding:0 6px;}
.mathquill-rendered-math .customk .numerator,
.mathquill-rendered-math .customm .numerator{border-bottom:1px solid #000;}
.mathquill-rendered-math .customl .numerator{border-bottom:0;margin-bottom:0;}
.arrowr{position:absolute;right:-2px;bottom:-3px;bottom:-2px\9;font-size:9px;line-height:9px;}
.arrowl{position:absolute;left:-2px;top:-3px;font-size:9px;line-height:9px;}
.arrowright{position:absolute;right:-4px;font-size:14px;font-size:12px\9;top:-.45em;}
.customul{display: inline-block;}
.customli{float: none;display: block !important;font-size: 100%; padding:1px 2px 1px 0 !important; margin-top:1px;}
.customli_r{float: none;display: block !important;font-size: 100%; padding:1px 2px 1px 0 !important; margin-top:1px; text-align:left;}
.customdiv{width:auto;float:none;vertical-align:-0.5em;}
.customdiv .matrixed{}
:root .customdiv .matrixed{font-family:Symbola, "Times New Roman", serif;}
.customdiv_3{width:auto;float:none;vertical-align:-1em;}
.customdiv_3 .matrixed{position:relative;top:1px;}
:root .customdiv_3 .matrixed{font-family:Symbola, "Times New Roman", serif;}

.topmark{vertical-align:-0.5em;}
span.log{padding:1px 0;}
span.log .log_up{font-size: 100%;position: relative;top: -2px;margin-right:5px;}
span.log .log_down{font-size:60%;position: relative;top: 2px;}
.mathquill-rendered-math .customh_up{display:block;font-size:80%}
.mathquill-rendered-math .customh_down{display:block;font-size:80%}
.mathquill-rendered-math .customh_sum{}
.mathquill-rendered-math .customh{vertical-align: -0.8em;}