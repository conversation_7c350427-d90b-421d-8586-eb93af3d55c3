$(document).ready(function(){
	// 初始化公式显示区域
	$(".myeq").each(function(){
		initMyEq(this, true);
	});
});

/**
 * 初始化公式显示区域
 *
 * @param elt
 * @param readonly 前台显示时是否只读
 */
function initMyEq(elt, readonly) {
	$(elt).html(parseDataToHTML($(elt).attr('content'), true, readonly));
	$(elt).removeAttr('content');
}

/**
 * 将HTML解析为数据
 *
 * @param html
 * @param inDiv 是否在可编辑div中
 */
function parseHTMLToData(html, inDiv) {
	var result = "";
	// ie
	html = html.replace(/<p>/gi, '');
	html = html.replace(/<\/p>/gi, '<br>');
	// 去掉末尾<br>标记
	if (html.substring(html.length-4, html.length) == "<br>")
		html = html.substring(0, html.length-4);
	if (inDiv) {
		html = $.trim(html.replace(/&nbsp;/gi, " "));
	}

	//解析html中的数字公式
	var pattern = /<iframe.*?\slatex="(.*?)".*?<\/iframe>/gi; //匹配span开始标记和结束标记
	var posArr = []; //存放匹配元素位置的数组
	var latexArr = []; //存放latex的数组
	var tmpArr = null;
	while ((tmpArr = pattern.exec(html)) != null) {
		posArr.push(tmpArr.index);
		posArr.push(tmpArr.index + tmpArr[0].length);
		latexArr.push(tmpArr[1]);
	}
	posArr.unshift(0);
	posArr.push(html.length);
	for(var i=0,j=0;i<posArr.length;i=i+2,j++){
		result += html.substring(posArr[i],posArr[i+1]);
		if(latexArr[j]){
			result += "|math<|" + latexArr[j] + "|>math|";
		}
	}
	//解析html中的填空标记
	result = result.replace(/<img\s[^>]*?\/block_space.jpg\">/gi, "|space<||>space|");

	return result;
}

/**
 * 将数据解析为HTML
 *
 * @param data 原始值
 * @param isFront 是否用于前台显示
 * @param readonly 前台显示时是否可输入公式
 */
function parseDataToHTML(data, isFront, readonly) {
	if(data==null){
		return "";
	}
	var result = "";
	//找出特殊标志将其放入数组
	var pattern = /\|(math|space)\<\|(.*?)\|\>(math|space)\|/g;
	var specArr = [];
	var tmpArr = null;
	while (tmpArr = pattern.exec(data)) {
		var obj = {};
		obj.type = tmpArr[1];
		obj.originalStr = tmpArr[2];
		obj.startIndex = pattern.lastIndex - RegExp.lastMatch.length;
		obj.endIndex = pattern.lastIndex;
		specArr.push(obj);
	}
	//将特殊标志数组中的数据解析为html代码并存入数组
	var arrHtml = [];
	for (var i = 0; i < specArr.length; i++) {
		var obj = specArr[i];
		var html = null;
		if (obj.type == 'space') {
			if (isFront) {
				if (readonly) {
					html = '<div class="intoText">' + '</div>';
				} else {
					html = '<div contenteditable="true" onclick="getCursorPos(this);showMath(this);" onkeyup="getCursorPos(this);" class="intoText">' + '</div>';
				}
			} else {
				html = '<img src="' + '/ewebeditor/sysimage/editarea/block_space.jpg">';
			}
		} else {
			if (isFront) {
				var $el = $('<span style="vertical-align: middle;"></span>');
				$el.appendTo('body').mathquill().mathquill('write', obj.originalStr);
				html = $el.wrap('<div></div>').parent().html();
				$el.unwrap().remove();
			} else {
				html = createIframeHTML(obj.originalStr);
			}
		}
		arrHtml.push(html)
	}
	//将原数据中公式、空格等特殊标志替换成html并存入返回结果中
	var arrPostion = [];
	arrPostion.push(0);
	for (var i = 0; i < specArr.length; i++) {
		var obj = specArr[i];
		arrPostion.push(obj.startIndex, obj.endIndex);
	}
	arrPostion.push(data.length);
	for (var i = 0; i < arrPostion.length; i=i+2) {
		if (i > 0) {
			result += arrHtml[i/2-1];
		}
		result += data.substring(arrPostion[i], arrPostion[i+1]);
	}
	return result;
}

/**
 * 给指定对象添加公式
 *
 * @param obj
 * @param latex
 * @returns
 */
function addLatex(obj, latex) {
	$('<span></span>').appendTo(obj).mathquill().mathquill('write', latex);
}

/**
 * 获取光标在div中的位置
 */
function getCursorPos(obj) {
	if (!obj.id) {
		obj.id = "_autoid_" + utils.uuid();
	}
	var vname = "_ctrl_" + obj.id;
	if (eval('window.'+vname) == undefined) {
		eval('window.'+vname+'=new cursorControl(document.getElementById("' + obj.id + '"))');
		$(obj).trigger('keyup');
	}
	//ie8下处理
	if ($.browser.msie && parseInt($.browser.version)<=8) {
		var html = $(obj).html();
		if (html.length) {
			var sEnd = html.charAt(html.length-1);
			if (sEnd !== " ") {
				$(obj).append(" ");
			}
			var len = obj.innerText.length + getIFrameCount(obj);
			var pos = getCaretPos(obj) + getIFrameCount(obj);
			if (pos >= len) {
				setCaretPos(obj, len-1);
				$(obj).trigger('keyup');
			}
		}
	}
}

function getIFrameCount(obj) {
	var len = 0;
	var arr = $(obj).html().match(/<\/iframe>|<img/gi);
	 if (arr) {
		len = arr.length;
	 }
	return len;
}

/**
 * 将html字符串插入obj的当前位置
 *
 * @param obj
 * @param html
 */
function insertHTML(obj, html) {
	if (! obj)
		return;
	var vname = "_ctrl_" + obj.id;
	eval(vname+'.insertText(html)');
	setTimeout(function(){
		$(obj).toggleClass("repain");
	}, 500);
};

/**
 * 创建用于显示公式的iframe的HTML字符串
 *
 * @param latex
 * @returns
 */
function createIframeHTML(latex) {
	var frame = document.createElement("iframe");
	frame.setAttribute("frameBorder", "0");
	frame.setAttribute("latex", latex);
	frame.scrolling = "no";
	frame.src = "/myeq/formula.htm?r=" + Math.random();
	frame.style.margin = "0 1px";
	frame.style.padding = "0";
	frame.style.verticalAlign = "middle";
	var div = document.createElement('div');
	div.appendChild(frame);
	return div.innerHTML;
}

/**
 * 调整iframe宽度、高度
 *
 * @param obj
 */
function dynIframeSize(obj) {
	var count = 1;
	setTimeout(function t(){
		count++;
		if (count < 25) {
			var doc = obj.contentDocument || obj.Document;
			if (!obj || !doc) return;
			var box = doc.getElementsByTagName('span')[0].getBoundingClientRect();
			var w = box.right - box.left + 2
			var h = box.bottom - box.top;
			if (obj.width != w)	obj.width = w;
		    if (obj.height != h) obj.height = h;
		    setTimeout(t, 200);
		}
	}, 200);
}

/**
 * 显示段落解析
 *
 * @param obj
 */
function showComment(obj) {
	$(obj).prev().toggle();
}

/**
 * 显示段解、词解
 *
 * @param obj
 */
function showWordexplain(obj) {
	alert($(obj).text() + "--" + $(obj).attr("wordtype") + "--" + $(obj).attr("wordaction"));
}

/**
 * 初始化文章，处理其中的段解、词解
 *
 * @param id
 */
function initArticle(id) {
	var $el = $('#'+id);
	$el.find("table.paragraphcomment").each(function (index) {
		$(this).hide().after("<a href='javascript:void(0)' onclick='javascript:showComment(this);'>【段落解析】</a>");
	});
	$el.find("a.wordexplain").each(function (index) {
		$(this).click(function(){
			showWordexplain(this);
		});
	});
}

function filterPasteText(str) {
    str = str.replace(/\r\n|\n|\r/ig, "");
    str = str.replace(/<(!DOCTYPE)(\n|.)*?>/ig, "");
    str = str.replace(/<\/?\w+\b[^>]*>/ig, "");
    return str;
}
