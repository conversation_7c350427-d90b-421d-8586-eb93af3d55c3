{"meta": {"stableOrder": true}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@ohos/crypto-js@^2.0.4": "@ohos/crypto-js@2.0.4", "@ohos/flutter_ohos@../har/flutter.har": "@ohos/flutter_ohos@../har/flutter.har", "@ohos/lottie@^2.0.15": "@ohos/lottie@2.0.23", "@pdp/book@libs/book.har": "@pdp/book@libs/book.har", "@pdp/evaluation@^1.1.1": "@pdp/evaluation@1.1.3", "@pdp/swiper@^1.0.0": "@pdp/swiper@1.0.0", "@tencent/wechat_open_sdk@1.0.11": "@tencent/wechat_open_sdk@1.0.11", "audio_session@../har/audio_session.har": "audio_session@../har/audio_session.har", "audioplayers_ohos@../har/audioplayers_ohos.har": "audioplayers_ohos@../har/audioplayers_ohos.har", "bigdata@../oh_modules/.ohpm/@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=/oh_modules/@pdp/book/src/lib/bigdata.har": "bigdata@../oh_modules/.ohpm/@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=/oh_modules/@pdp/book/src/lib/bigdata.har", "class-transformer@^0.5.1": "class-transformer@0.5.1", "connectivity_plus@../har/connectivity_plus.har": "connectivity_plus@../har/connectivity_plus.har", "dayjs@^1.11.7": "dayjs@1.11.13", "device_info_plus@../har/device_info_plus.har": "device_info_plus@../har/device_info_plus.har", "flutter_image_compress_ohos@../har/flutter_image_compress_ohos.har": "flutter_image_compress_ohos@../har/flutter_image_compress_ohos.har", "flutter_inappwebview_ohos@../har/flutter_inappwebview_ohos.har": "flutter_inappwebview_ohos@../har/flutter_inappwebview_ohos.har", "flutter_sound@../har/flutter_sound.har": "flutter_sound@../har/flutter_sound.har", "fluwx@../har/fluwx.har": "fluwx@../har/fluwx.har", "image_picker_ohos@../har/image_picker_ohos.har": "image_picker_ohos@../har/image_picker_ohos.har", "just_audio_ohos@../har/just_audio_ohos.har": "just_audio_ohos@../har/just_audio_ohos.har", "libsingsound.so@../oh_modules/.ohpm/singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=/oh_modules/singsound/src/main/cpp/types/libsingsound": "libsingsound.so@../oh_modules/.ohpm/singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=/oh_modules/singsound/src/main/cpp/types/libsingsound", "mobile_scanner@../har/mobile_scanner.har": "mobile_scanner@../har/mobile_scanner.har", "package_info_plus@../har/package_info_plus.har": "package_info_plus@../har/package_info_plus.har", "path_provider_ohos@../har/path_provider_ohos.har": "path_provider_ohos@../har/path_provider_ohos.har", "permission_handler_ohos@../har/permission_handler_ohos.har": "permission_handler_ohos@../har/permission_handler_ohos.har", "reflect-metadata@^0.1.13": "reflect-metadata@0.2.1", "screen_brightness_ohos@../har/screen_brightness_ohos.har": "screen_brightness_ohos@../har/screen_brightness_ohos.har", "shared_preferences_ohos@../har/shared_preferences_ohos.har": "shared_preferences_ohos@../har/shared_preferences_ohos.har", "singsound@../oh_modules/.ohpm/@pdp+evaluation@1.1.3/oh_modules/@pdp/evaluation/src/lib/singsound-0.0.10.har": "singsound@libs/singsound-0.0.8.har", "singsound@libs/singsound-0.0.8.har": "singsound@libs/singsound-0.0.8.har", "url_launcher_ohos@../har/url_launcher_ohos.har": "url_launcher_ohos@../har/url_launcher_ohos.har", "video_player_ohos@../har/video_player_ohos.har": "video_player_ohos@../har/video_player_ohos.har"}, "packages": {"@ohos/crypto-js@2.0.4": {"name": "@ohos/crypto-js", "version": "2.0.4", "integrity": "sha512-589ur6oqU1UNibqefMly2cwEeEhkSoCAA3uc+oNUwRnYYtevn/kQnO+Coi36N+VJSeeg/uFzZk1K/wUMdovpOA==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/crypto-js/-/crypto-js-2.0.4.har", "registryType": "ohpm"}, "@ohos/flutter_ohos@../har/flutter.har": {"name": "@ohos/flutter_ohos", "version": "1.0.0-299265ba05", "resolved": "../har/flutter.har", "registryType": "local"}, "@ohos/lottie@2.0.23": {"name": "@ohos/lottie", "version": "2.0.23", "integrity": "sha512-SiVmsyllXLcvSrh98qsXtjSoVaxy1bm8sGZzmz94DZOjdQ2zSCnTzY627EevkCCFxAA5HIbqFPUWokhc8bp/3Q==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/lottie/-/lottie-2.0.23.har", "registryType": "ohpm"}, "@pdp/book@libs/book.har": {"name": "@pdp/book", "version": "1.0.0", "resolved": "libs/book.har", "registryType": "local", "dependencies": {"dayjs": "^1.11.7", "@ohos/lottie": "^2.0.15", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "@ohos/crypto-js": "^2.0.4", "@pdp/swiper": "^1.0.0", "@pdp/evaluation": "^1.1.1", "bigdata": "file:src/lib/bigdata.har"}}, "@pdp/evaluation@1.1.3": {"name": "@pdp/evaluation", "version": "1.1.3", "integrity": "sha512-yfPG3mqnFChWEWEvsBCZCjHjvhrYjNA4592h336eu3qDv8Q6l34WY7in1A7AR4osUWpDv+4WvqRSrSy6fkAlnQ==", "resolved": "https://ohpm.openharmony.cn/ohpm/@pdp/evaluation/-/evaluation-1.1.3.har", "registryType": "ohpm", "dependencies": {"class-transformer": "^0.5.1", "singsound": "file:src/lib/singsound-0.0.10.har"}}, "@pdp/swiper@1.0.0": {"name": "@pdp/swiper", "version": "1.0.0", "integrity": "sha512-mtAWILSkXRuYXeldCHSjytFeyVcNO2F8wLo0rqXxdVGCfrcW4WivAXpoQpBl9ZmUel50mCupjRUEWhcNzfoKIw==", "resolved": "https://ohpm.openharmony.cn/ohpm/@pdp/swiper/-/swiper-1.0.0.har", "registryType": "ohpm"}, "@tencent/wechat_open_sdk@1.0.11": {"name": "@tencent/wechat_open_sdk", "version": "1.0.11", "integrity": "sha512-c/Yy7ydBIy3d/nRmxMPfKhIYJb7UQmwFy7+6bW6JnLvzeQmFDPOT27Dsz55/nrR7H6eyv+1bi4Y2XtSLvEDuVw==", "resolved": "https://ohpm.openharmony.cn/ohpm/@tencent/wechat_open_sdk/-/wechat_open_sdk-1.0.11.har", "registryType": "ohpm"}, "audio_session@../har/audio_session.har": {"name": "audio_session", "version": "1.0.0", "resolved": "../har/audio_session.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "audioplayers_ohos@../har/audioplayers_ohos.har": {"name": "audioplayers_ohos", "version": "1.0.0", "resolved": "../har/audioplayers_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:har/flutter.har"}}, "bigdata@../oh_modules/.ohpm/@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=/oh_modules/@pdp/book/src/lib/bigdata.har": {"name": "bigdata", "version": "1.0.0", "resolved": "../oh_modules/.ohpm/@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=/oh_modules/@pdp/book/src/lib/bigdata.har", "registryType": "local"}, "class-transformer@0.5.1": {"name": "class-transformer", "version": "0.5.1", "integrity": "sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==", "resolved": "https://ohpm.openharmony.cn/ohpm/class-transformer/-/class-transformer-0.5.1.tgz", "shasum": "24147d5dffd2a6cea930a3250a677addf96ab336", "registryType": "ohpm"}, "connectivity_plus@../har/connectivity_plus.har": {"name": "connectivity_plus", "version": "1.0.0", "resolved": "../har/connectivity_plus.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./libs/flutter_embedding.har"}}, "dayjs@1.11.13": {"name": "dayjs", "version": "1.11.13", "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==", "resolved": "https://ohpm.openharmony.cn/ohpm/dayjs/-/dayjs-1.11.13.tgz", "shasum": "92430b0139055c3ebb60150aa13e860a4b5a366c", "registryType": "ohpm"}, "device_info_plus@../har/device_info_plus.har": {"name": "device_info_plus", "version": "1.0.0", "resolved": "../har/device_info_plus.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "flutter_image_compress_ohos@../har/flutter_image_compress_ohos.har": {"name": "flutter_image_compress_ohos", "version": "1.0.0", "resolved": "../har/flutter_image_compress_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "flutter_inappwebview_ohos@../har/flutter_inappwebview_ohos.har": {"name": "flutter_inappwebview_ohos", "version": "1.0.0", "resolved": "../har/flutter_inappwebview_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "flutter_sound@../har/flutter_sound.har": {"name": "flutter_sound", "version": "1.0.0", "resolved": "../har/flutter_sound.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "fluwx@../har/fluwx.har": {"name": "fluwx", "version": "1.0.0", "resolved": "../har/fluwx.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har", "@tencent/wechat_open_sdk": "1.0.11"}}, "image_picker_ohos@../har/image_picker_ohos.har": {"name": "image_picker_ohos", "version": "1.0.0", "resolved": "../har/image_picker_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "just_audio_ohos@../har/just_audio_ohos.har": {"name": "just_audio_ohos", "version": "1.0.0", "resolved": "../har/just_audio_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "libsingsound.so@../oh_modules/.ohpm/singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=/oh_modules/singsound/src/main/cpp/types/libsingsound": {"name": "libsingsound.so", "version": "1.0.0", "resolved": "../oh_modules/.ohpm/singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=/oh_modules/singsound/src/main/cpp/types/libsingsound", "registryType": "local"}, "mobile_scanner@../har/mobile_scanner.har": {"name": "mobile_scanner", "version": "1.0.0", "resolved": "../har/mobile_scanner.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "package_info_plus@../har/package_info_plus.har": {"name": "package_info_plus", "version": "1.0.0", "resolved": "../har/package_info_plus.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./libs/flutter.har"}}, "path_provider_ohos@../har/path_provider_ohos.har": {"name": "path_provider_ohos", "version": "1.0.0", "resolved": "../har/path_provider_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "permission_handler_ohos@../har/permission_handler_ohos.har": {"name": "permission_handler_ohos", "version": "1.0.0", "resolved": "../har/permission_handler_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "reflect-metadata@0.2.1": {"name": "reflect-metadata", "version": "0.2.1", "integrity": "sha512-i5lLI6iw9AU3Uu4szRNPPEkomnkjRTaVt9hy/bn5g/oSzekBSMeLZblcjP74AW0vBabqERLLIrz+gR8QYR54Tw==", "resolved": "https://ohpm.openharmony.cn/ohpm/reflect-metadata/-/reflect-metadata-0.2.1.tgz", "shasum": "8d5513c0f5ef2b4b9c3865287f3c0940c1f67f74", "registryType": "ohpm"}, "screen_brightness_ohos@../har/screen_brightness_ohos.har": {"name": "screen_brightness_ohos", "version": "1.0.0", "resolved": "../har/screen_brightness_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "shared_preferences_ohos@../har/shared_preferences_ohos.har": {"name": "shared_preferences_ohos", "version": "1.0.0", "resolved": "../har/shared_preferences_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "singsound@libs/singsound-0.0.8.har": {"name": "singsound", "version": "1.0.0", "resolved": "libs/singsound-0.0.8.har", "registryType": "local", "dependencies": {"libsingsound.so": "file:./src/main/cpp/types/libsingsound"}}, "url_launcher_ohos@../har/url_launcher_ohos.har": {"name": "url_launcher_ohos", "version": "1.0.0", "resolved": "../har/url_launcher_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "video_player_ohos@../har/video_player_ohos.har": {"name": "video_player_ohos", "version": "1.0.0", "resolved": "../har/video_player_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}}}