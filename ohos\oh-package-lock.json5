{"meta": {"stableOrder": true}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@ohos/flutter_ohos@har/flutter.har": "@ohos/flutter_ohos@har/flutter.har", "@ohos/hypium@1.0.6": "@ohos/hypium@1.0.6", "@ohos/mp4parser@2.0.3-rc.1": "@ohos/mp4parser@2.0.3-rc.1", "@types/libmp4parser_napi.so@oh_modules/.ohpm/@ohos+mp4parser@2.0.3-rc.1/oh_modules/@ohos/mp4parser/src/main/cpp/types/libmp4parser_napi": "@types/libmp4parser_napi.so@oh_modules/.ohpm/@ohos+mp4parser@2.0.3-rc.1/oh_modules/@ohos/mp4parser/src/main/cpp/types/libmp4parser_napi", "@umeng/analytics@^1.2.4": "@umeng/analytics@1.2.4", "@umeng/common@^1.1.3": "@umeng/common@1.1.3", "libcommon.so@oh_modules/.ohpm/@umeng+common@1.1.3/oh_modules/@umeng/common/src/main/cpp/types/libcommon": "libcommon.so@oh_modules/.ohpm/@umeng+common@1.1.3/oh_modules/@umeng/common/src/main/cpp/types/libcommon"}, "packages": {"@ohos/flutter_ohos@har/flutter.har": {"name": "@ohos/flutter_ohos", "version": "1.0.0-299265ba05", "resolved": "har/flutter.har", "registryType": "local"}, "@ohos/hypium@1.0.6": {"name": "@ohos/hypium", "version": "1.0.6", "integrity": "sha512-bb3DWeWhYrFqj9mPFV3yZQpkm36kbcK+YYaeY9g292QKSjOdmhEIQR2ULPvyMsgSR4usOBf5nnYrDmaCCXirgQ==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/hypium/-/hypium-1.0.6.tgz", "shasum": "3f5fed65372633233264b3447705b0831dfe7ea1", "registryType": "ohpm"}, "@ohos/mp4parser@2.0.3-rc.1": {"name": "@ohos/mp4parser", "version": "2.0.3-rc.1", "integrity": "sha512-HyasvauLEl7XBHi0thwVLmZBSbgWZrVVgo3wLb/PARVDF0YH+iHJI66ErIWHon5B9SzffBP+HQlOZSErFzubUg==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/mp4parser/-/mp4parser-2.0.3-rc.1.har", "registryType": "ohpm", "dependencies": {"@types/libmp4parser_napi.so": "file:./src/main/cpp/types/libmp4parser_napi"}}, "@types/libmp4parser_napi.so@oh_modules/.ohpm/@ohos+mp4parser@2.0.3-rc.1/oh_modules/@ohos/mp4parser/src/main/cpp/types/libmp4parser_napi": {"name": "libmp4parser_napi.so", "version": "0.0.0", "resolved": "oh_modules/.ohpm/@ohos+mp4parser@2.0.3-rc.1/oh_modules/@ohos/mp4parser/src/main/cpp/types/libmp4parser_napi", "registryType": "local"}, "@umeng/analytics@1.2.4": {"name": "@umeng/analytics", "version": "1.2.4", "integrity": "sha512-qth9hSUkHtgL9NBSZYHznmZpgU9uFK6LDKyRvqjih6+HRtOi7lXl6PblN8993x5i+hfBgSWMdDxqD0wgl7tWdQ==", "resolved": "https://ohpm.openharmony.cn/ohpm/@umeng/analytics/-/analytics-1.2.4.har", "registryType": "ohpm"}, "@umeng/common@1.1.3": {"name": "@umeng/common", "version": "1.1.3", "integrity": "sha512-0pYi/JuQzZ8Gsy97Bw5lqCwkTUIK+vImQC+4mgWYbZKH+OxKu4HOoKds0bz+9xbVIyfx88dR7lEdcGeQfR6eug==", "resolved": "https://ohpm.openharmony.cn/ohpm/@umeng/common/-/common-1.1.3.har", "registryType": "ohpm", "dependencies": {"libcommon.so": "./src/main/cpp/types/libcommon"}}, "libcommon.so@oh_modules/.ohpm/@umeng+common@1.1.3/oh_modules/@umeng/common/src/main/cpp/types/libcommon": {"name": "libcommon.so", "version": "1.0.0", "resolved": "oh_modules/.ohpm/@umeng+common@1.1.3/oh_modules/@umeng/common/src/main/cpp/types/libcommon", "registryType": "local"}}}