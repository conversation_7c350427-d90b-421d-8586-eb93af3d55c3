/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { BinaryMessenger, MethodCall } from '@ohos/flutter_ohos';
import MethodChannel, { Method<PERSON>allHandler, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { Context as Context } from "@ohos.abilityAccessCtrl";
export default class OhosAudioManager implements MethodCallHandler {
    private messenger;
    channel: MethodChannel | null;
    private singleton;
    constructor(applicationContext: Context, messenger: Binary<PERSON><PERSON><PERSON><PERSON>);
    onMethodCall(call: <PERSON><PERSON><PERSON>, result: MethodResult): void;
    dispose(): void;
}
