/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import media from '@ohos.multimedia.media';
import audio from '@ohos.multimedia.audio';
export default class AudioContextOhos {
    rendererFlags: number;
    usageType: number;
    isSpeakerphoneOn: boolean;
    audioScene: number;
    stayAwake: boolean;
    copy(): AudioContextOhos;
    setAttributesOnPlayer(mediaPlayer: media.AVPlayer): void;
    buildAttributes(): audio.AudioRendererInfo;
    equals(context: AudioContextOhos): boolean;
}
