/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import AudioContextOhos from '../AudioContextOhos';
import Source from '../source/Source';
import Player from './Player';
import WrappedPlayer from './WrappedPlayer';
import media from '@ohos.multimedia.media';
export default class MediaPlayerPlayer implements Player {
    private wrappedPlayer;
    private mediaPlayer?;
    private isLooping;
    private volume;
    private needPrepare;
    private stayAwake;
    private speed;
    constructor(wrappedPlayer: WrappedPlayer, context: AudioContextOhos);
    initMediaPlayer(): Promise<void>;
    setAVPlayerCallback(avPlayer: media.AVPlayer): void;
    getDuration(): number;
    getCurrentPosition(): number;
    isActuallyPlaying(): boolean;
    isLiveStream(): boolean;
    start(): Promise<void>;
    pause(): Promise<void>;
    stop(): Promise<void>;
    seekTo(position: number): void;
    release(): Promise<void>;
    setVolume(leftVolume: number, rightVolume: number): void;
    setRate(rate: number): void;
    setLooping(looping: boolean): void;
    private isReadyState;
    updateContext(context: AudioContextOhos): Promise<void>;
    setSource(source: Source): Promise<void>;
    prepare(): Promise<void>;
    private canPrepare;
    reset(): Promise<void>;
}
