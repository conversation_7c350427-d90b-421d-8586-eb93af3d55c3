import { StreamHandler, EventSink } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
import { Connectivity } from './Connectivity';
export declare class ConnectivityBroadcastReceiver implements StreamHandler {
    private connectivity;
    private context;
    private events;
    constructor(context: Context, connectivity: Connectivity);
    onListen(args: Object, events: EventSink): void;
    onCancel(args?: Object): void;
    private sendEvent;
}
