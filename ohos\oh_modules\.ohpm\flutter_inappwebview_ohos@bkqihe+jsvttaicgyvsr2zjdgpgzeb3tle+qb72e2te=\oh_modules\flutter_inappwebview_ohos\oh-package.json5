{"name": "flutter_inappwebview_ohos", "version": "1.0.0", "description": "Please describe the basic information.", "author": "", "license": "Apache-2.0", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}, "types": "index.d.ets", "artifactType": "obfuscation", "metadata": {"byteCodeHar": true, "sourceRoots": ["./src/main"], "debug": true, "dependencyPkgVersion": {"@ohos/flutter_ohos": "1.0.0-299265ba05", "@ohos/mp4parser": "2.0.3-rc.1", "@umeng/common": "1.1.3", "@umeng/analytics": "1.2.4"}, "declarationEntry": [], "nativeDebugSymbol": true, "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": false, "nativeComponents": [{"name": "libentry.so", "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "linkLibraries": []}]}