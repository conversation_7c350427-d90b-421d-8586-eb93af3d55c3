import { AbilityAware, AbilityPluginBinding, BinaryMessenger } from '@ohos/flutter_ohos';
import { FlutterAssets, FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { HeadlessInAppWebViewManager } from './headless_in_webview/HeadlessInAppWebViewManager';
import MyCookieManager from './MyCookieManager';
import PrintJobManager from './print_job/PrintJobManager';
import FlutterWebViewFactory from './webview/FlutterWebViewFactory';
import { InAppWebViewManager } from './webview/InAppWebViewManager';
import InAppBrowserManager from './in_app_browser/InAppBrowserManager';
import common from '@ohos.app.ability.common';
import UIAbility from "@ohos.app.ability.UIAbility";
export default class InAppWebViewFlutterPlugin implements FlutterPlugin, AbilityAware {
    flutterAssets: FlutterAssets | null;
    flutterWebViewFactory: FlutterWebViewFactory | null;
    messenger: BinaryMessenger | null;
    printJobManager: PrintJobManager | null;
    myCookieManager: MyCookieManager | null;
    headlessInAppWebViewManager: HeadlessInAppWebViewManager | null;
    inAppWebViewManager: InAppWebViewManager | null;
    inAppBrowserManager: InAppBrowserManager | null;
    applicationContext: common.Context | null;
    uiAbility: UIAbility | null;
    constructor();
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
}
