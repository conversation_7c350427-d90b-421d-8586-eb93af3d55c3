import { MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import { FindInteractionController } from './FindInteractionController';
export declare class FindInteractionChannelDelegate extends ChannelDelegateImpl {
    private findInteractionController;
    constructor(findInteractionController: FindInteractionController | null, channel: MethodChannel);
    onMethodCall(call: MethodCall, result: MethodResult): void;
    onFindResultReceived(activeMatchOrdinal: number, numberOfMatches: number, isDoneCounting: boolean): void;
    dispose(): void;
}
