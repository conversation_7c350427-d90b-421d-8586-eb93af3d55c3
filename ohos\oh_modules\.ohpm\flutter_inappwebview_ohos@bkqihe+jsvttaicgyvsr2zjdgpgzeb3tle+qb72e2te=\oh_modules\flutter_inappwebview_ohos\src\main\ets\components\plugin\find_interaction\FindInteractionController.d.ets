import { Any } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import { Disposable } from '../types/Disposable';
import InAppWebViewInterface from '../webview/InAppWebViewInterface';
import { FindInteractionChannelDelegate } from './FindInteractionChannelDelegate';
import { FindInteractionSettings } from './FindInteractionSettings';
export declare class FindInteractionController implements Disposable {
    webView: InAppWebViewInterface | null;
    channelDelegate: FindInteractionChannelDelegate | null;
    settings: FindInteractionSettings | null;
    searchText: string | null;
    constructor(webView: InAppWebViewInterface, plugin: InAppWebViewFlutterPlugin, id: Any, settings: FindInteractionSettings | null);
    prepare(): void;
    findAll(find: string | null): void;
    findNext(forward: boolean): void;
    clearMatches(): void;
    dispose(): void;
}
