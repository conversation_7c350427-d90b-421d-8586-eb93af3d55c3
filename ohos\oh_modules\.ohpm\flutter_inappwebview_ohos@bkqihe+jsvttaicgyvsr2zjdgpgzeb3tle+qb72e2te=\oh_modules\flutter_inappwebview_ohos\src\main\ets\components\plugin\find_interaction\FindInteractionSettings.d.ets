import { Any } from '@ohos/flutter_ohos';
import ISettings from '../ISettings';
import { FindInteractionController } from './FindInteractionController';
export declare class FindInteractionSettings implements ISettings<FindInteractionController> {
    parse(settings: Map<string, Any>): ISettings<FindInteractionController>;
    toMap(): Map<string, Any>;
    getRealSettings(obj: FindInteractionController): Map<string, Any>;
}
