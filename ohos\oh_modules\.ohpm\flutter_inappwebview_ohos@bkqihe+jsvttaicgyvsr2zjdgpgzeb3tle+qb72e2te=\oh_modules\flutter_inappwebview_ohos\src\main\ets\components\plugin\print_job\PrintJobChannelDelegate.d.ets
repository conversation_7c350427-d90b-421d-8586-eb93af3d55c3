import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import PrintJobController from './PrintJobController';
import { MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
export default class PrintJobChannelDelegate extends ChannelDelegateImpl {
    private printJobController;
    constructor(printJobController: PrintJobController, channel: MethodChannel);
    onMethodCall(call: Method<PERSON>all, result: MethodResult): void;
    dispose(): void;
}
