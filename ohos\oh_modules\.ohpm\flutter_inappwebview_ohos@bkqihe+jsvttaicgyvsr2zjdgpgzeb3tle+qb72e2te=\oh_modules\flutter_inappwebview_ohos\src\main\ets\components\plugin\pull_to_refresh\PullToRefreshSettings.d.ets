import { Any } from '@ohos/flutter_ohos';
import ISettings from '../ISettings';
import PullToRefreshLayout from './PullToRefreshLayout';
export default class PullToRefreshSettings implements ISettings<PullToRefreshLayout> {
    static LOG_TAG: string;
    enabled: boolean;
    color: string | null;
    backgroundColor: string | null;
    distanceToTriggerSync: number | null;
    slingshotDistance: number | null;
    size: number | null;
    parse(settings: Map<string, Any>): PullToRefreshSettings;
    toMap(): Map<string, any>;
    getRealSettings(pullToRefreshLayout: PullToRefreshLayout): Map<string, any>;
}
