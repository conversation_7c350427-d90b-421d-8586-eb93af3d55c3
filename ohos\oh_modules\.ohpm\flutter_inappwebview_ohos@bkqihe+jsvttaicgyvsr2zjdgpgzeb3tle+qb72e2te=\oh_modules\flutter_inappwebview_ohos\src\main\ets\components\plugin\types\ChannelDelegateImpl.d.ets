import { Method<PERSON>all, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { IChannelDelegate } from './IChannelDelegate';
export default class ChannelDelegateImpl implements IChannelDelegate {
    private channel;
    constructor(channel: MethodChannel);
    getChannel(): MethodChannel;
    dispose(): void;
    onMethodCall(call: MethodCall, result: MethodResult): void;
}
