import { Any } from '@ohos/flutter_ohos';
export default class DownloadStartRequest {
    private url;
    private userAgent;
    private contentDisposition;
    private mimeType;
    private contentLength;
    private suggestedFilename;
    private textEncodingName;
    constructor(url: string, userAgent: string, contentDisposition: string, mimeType: string, contentLength: number, suggestedFilename: string | null, textEncodingName: string | null);
    toMap(): Map<string, Any>;
    getUrl(): string;
    setUrl(url: string): void;
    getUserAgent(): string;
    setUserAgent(userAgent: string): void;
    getContentDisposition(): string;
    setContentDisposition(contentDisposition: string): void;
    getMimeType(): string;
    setMimeType(mimeType: string): void;
    getContentLength(): number;
    setContentLength(contentLength: number): void;
    getSuggestedFilename(): string | null;
    setSuggestedFilename(suggestedFilename: string): void;
    getTextEncodingName(): string | null;
    setTextEncodingName(textEncodingName: string): void;
}
