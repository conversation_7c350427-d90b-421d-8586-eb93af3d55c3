import web_webview from '@ohos.web.webview';
import { Any } from '@ohos/flutter_ohos';
export default class HitTestResult {
    private type;
    private extra;
    private static unknownType;
    private static phoneType;
    private static geoType;
    private static emailType;
    private static imageType;
    private static SRC_ANCHOR_TYPE;
    private static srcImageAnchorType;
    private static editTextType;
    constructor(type: number, extra: string | null);
    static getFlutterHitTestResultType(ohosTitTestResultType: web_webview.WebHitTestType): number;
    static fromWebViewHitTestResult(hitTestResult: web_webview.HitTestValue | null): HitTestResult | null;
    getType(): number;
    setType(type: number): void;
    getExtra(): string | null;
    setExtra(extra: string | null): void;
    toMap(): Map<string, Any>;
    equals(o: Any): boolean;
    hashCode(): number;
    toString(): string;
}
