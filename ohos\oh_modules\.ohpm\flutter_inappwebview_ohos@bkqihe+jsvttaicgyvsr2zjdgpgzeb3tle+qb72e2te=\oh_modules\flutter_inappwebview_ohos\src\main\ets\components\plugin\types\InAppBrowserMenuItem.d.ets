import { Any } from '@ohos/flutter_ohos';
export default class InAppBrowserMenuItem {
    private id;
    private title;
    private order;
    private icon;
    private iconColor;
    private showAsAction;
    constructor(id: number, title: string, order: number | null, icon: Any, iconColor: string | null, showAsAction: boolean);
    static fromMap(map: Map<string, Any>): InAppBrowserMenuItem | null;
    getId(): number;
    setId(id: number): void;
    getTitle(): string;
    setTitle(title: string): void;
    getOrder(): number;
    setOrder(order: number): void;
    getIcon(): Any;
    setIcon(icon: Any): void;
    getIconColor(): string;
    setIconColor(iconColor: string): void;
    isShowAsAction(): boolean;
    setShowAsAction(showAsAction: boolean): void;
    equals(o: Any): boolean;
    hashCode(): number;
    toString(): string;
}
