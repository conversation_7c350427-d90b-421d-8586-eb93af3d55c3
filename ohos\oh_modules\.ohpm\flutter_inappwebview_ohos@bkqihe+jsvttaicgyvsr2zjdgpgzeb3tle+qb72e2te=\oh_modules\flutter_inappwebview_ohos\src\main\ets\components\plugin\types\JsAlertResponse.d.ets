import { Any } from '@ohos/flutter_ohos';
export default class JsAlertResponse {
    private message;
    private confirmButtonTitle;
    private handledByClient;
    private action;
    constructor(message: string, confirmButtonTitle: string, handledByClient: boolean, action: number | null);
    static fromMap(map: Map<string, Any>): JsAlertResponse | null;
    getMessage(): string;
    setMessage(message: string): void;
    getConfirmButtonTitle(): string;
    setConfirmButtonTitle(confirmButtonTitle: string): void;
    isHandledByClient(): boolean;
    setHandledByClient(handledByClient: boolean): void;
    getAction(): number | null;
    setAction(action: number | null): void;
    equals(o: Any): boolean;
    hashCode(): number;
    toString(): string;
}
