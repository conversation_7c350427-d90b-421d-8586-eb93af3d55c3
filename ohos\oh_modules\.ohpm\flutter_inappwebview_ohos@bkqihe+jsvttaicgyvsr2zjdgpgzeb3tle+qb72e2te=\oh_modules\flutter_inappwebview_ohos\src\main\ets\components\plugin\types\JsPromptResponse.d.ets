import { Any } from '@ohos/flutter_ohos';
export default class JsPromptResponse {
    private message;
    private defaultValue;
    private confirmButtonTitle;
    private cancelButtonTitle;
    private handledByClient;
    private value;
    private action;
    constructor(message: string, defaultValue: string, confirmButtonTitle: string, cancelButtonTitle: string, handledByClient: boolean, value: string | null, action: number | null);
    static fromMap(map: Map<string, Any>): JsPromptResponse | null;
    getMessage(): string;
    setMessage(message: string): void;
    getDefaultValue(): string;
    setDefaultValue(defaultValue: string): void;
    getConfirmButtonTitle(): string;
    setConfirmButtonTitle(confirmButtonTitle: string): void;
    getCancelButtonTitle(): string;
    setCancelButtonTitle(cancelButtonTitle: string): void;
    isHandledByClient(): boolean;
    setHandledByClient(handledByClient: boolean): void;
    getValue(): string | null;
    setValue(value: string | null): void;
    getAction(): number | null;
    setAction(action: number | null): void;
    equals(o: Any): boolean;
    hashCode(): number;
    toString(): string;
}
