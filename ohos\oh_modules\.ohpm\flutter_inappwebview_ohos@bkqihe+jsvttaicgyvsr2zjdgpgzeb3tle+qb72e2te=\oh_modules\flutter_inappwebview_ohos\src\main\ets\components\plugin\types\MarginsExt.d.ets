export default class MarginsExt {
    private top;
    private right;
    private bottom;
    private left;
    constructor(top: number, right: number, bottom: number, left: number);
    static fromMap(map: Map<String, Object>): MarginsExt | null;
    private milsToPixels;
    private pixelsToMils;
    toMap(): Map<String, Object>;
    getTop(): number;
    setTop(top: number): void;
    getRight(): number;
    setRight(right: number): void;
    getBottom(): number;
    setBottom(bottom: number): void;
    getLeft(): number;
    setLeft(left: number): void;
    equals(o: Object): boolean;
    hashCode(): number;
    toString(): string;
}
