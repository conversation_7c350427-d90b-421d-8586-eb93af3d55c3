import { Any } from '@ohos/flutter_ohos';
import ContentWorld from './ContentWorld';
import UserScript from './UserScript';
import { UserScriptInjectionTime } from './UserScriptInjectionTime';
export default class PluginScript extends UserScript {
    private requiredInAllContentWorlds;
    constructor(groupName: string, source: string, injectionTime: UserScriptInjectionTime, contentWorld: ContentWorld | null, requiredInAllContentWorlds: boolean, allowedOriginRules: Set<string> | null);
    isRequiredInAllContentWorlds(): boolean;
    setRequiredInAllContentWorlds(requiredInAllContentWorlds: boolean): void;
    equals(o: Any): boolean;
    hashCode(): number;
    toString(): string;
}
