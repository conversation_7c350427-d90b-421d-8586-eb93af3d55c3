export default class ResolutionExt {
    private id;
    private label;
    private verticalDpi;
    private horizontalDpi;
    constructor(id: string, label: string, verticalDpi: number, horizontalDpi: number);
    static fromMap(map: Map<String, Object>): ResolutionExt | null;
    toMap(): Map<String, Object>;
    getId(): string;
    setId(id: string): void;
    getLabel(): string;
    setLabel(label: string): void;
    getVerticalDpi(): number;
    setVerticalDpi(verticalDpi: number): void;
    getHorizontalDpi(): number;
    setHorizontalDpi(horizontalDpi: number): void;
    equals(o: Object): boolean;
    hashCode(): number;
    toString(): string;
}
