import { Any } from '@ohos/flutter_ohos';
import BaseCallbackResultImpl from './BaseCallbackResultImpl';
export default class SyncBaseCallbackResultImpl<T> extends BaseCallbackResultImpl<T> {
    result: T | null;
    responsed: boolean;
    defaultBehaviour(result: T | null): void;
    success(obj: Any): void;
    waitResponse(): void;
    error(errorCode: string, errorMessage: string, errorDetails: Any): void;
    notImplemented(): void;
}
