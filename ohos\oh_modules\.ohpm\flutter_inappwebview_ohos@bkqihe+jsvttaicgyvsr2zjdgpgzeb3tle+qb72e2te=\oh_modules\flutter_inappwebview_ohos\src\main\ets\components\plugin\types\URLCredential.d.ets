import { Any } from '@ohos/flutter_ohos';
export default class URLCredential {
    private id;
    private username;
    private password;
    private protectionSpaceId;
    constructor(id: number | null, username: string | null, password: string | null, protectionSpaceId: number | null);
    toMap(): Map<string, Any>;
    getId(): number | null;
    setId(id: number | null): void;
    getUsername(): string | null;
    setUsername(username: string | null): void;
    getPassword(): string | null;
    setPassword(password: string | null): void;
    getProtectionSpaceId(): number | null;
    setProtectionSpaceId(protectionSpaceId: number | null): void;
    toString(): string;
}
