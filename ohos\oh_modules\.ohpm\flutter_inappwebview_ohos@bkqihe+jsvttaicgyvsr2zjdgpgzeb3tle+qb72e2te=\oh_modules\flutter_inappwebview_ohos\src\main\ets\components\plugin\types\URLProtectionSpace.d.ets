import cert from "@ohos.security.cert";
export default class URLProtectionSpace {
    private id;
    private host;
    private protocol;
    private realm;
    private port;
    private sslCertificate;
    private sslError;
    constructor(host: string, protocol: string, realm: string | null, port: number, id?: number | null, sslCertificate?: cert.X509Cert | null, sslError?: SslError | null);
    toMap(): Promise<Map<string, any>>;
    getId(): number | null;
    setId(id: number | null): void;
    getHost(): string;
    setHost(host: string): void;
    getProtocol(): string;
    setProtocol(protocol: string): void;
    getRealm(): string | null;
    setRealm(realm: string): void;
    getPort(): number;
    setPort(port: number): void;
    getSslCertificate(): cert.X509Cert | null;
    setSslCertificate(sslCertificateExt: cert.X509Cert | null): void;
    getSslError(): SslError | null;
    setSslError(sslError: SslError | null): void;
    toString(): string;
}
