import InAppWebView from '../webview/in_app_webview/InAppWebView';
import ContentWorld from './ContentWorld';
import { Disposable } from './Disposable';
import PluginScript from './PluginScript';
import UserScript from './UserScript';
import { UserScriptInjectionTime } from './UserScriptInjectionTime';
import HashSet from "@ohos.util.HashSet";
export default class UserContentController implements Disposable {
    protected static LOG_TAG: string;
    private contentWorlds;
    private inAppWebView;
    private userOnlyScripts;
    private pluginScripts;
    private isSupportedDocumentStartScript;
    private allStartScriptItem;
    private contentWorldsCreatorScript;
    private scriptHandlerMap;
    constructor(inAppWebView: InAppWebView);
    generateWrappedCodeForDocumentStart(): string;
    generateCodeForDocumentStart(): string;
    generatePluginScriptsCodeAt(injectionTime: UserScriptInjectionTime): string;
    generateUserOnlyScriptsCodeAt(injectionTime: UserScriptInjectionTime): string;
    getPluginScriptsAt(injectionTime: UserScriptInjectionTime): HashSet<PluginScript>;
    getUserOnlyScriptsAt(injectionTime: UserScriptInjectionTime): HashSet<UserScript>;
    wrapSourceCodeInContentWorld(contentWorld: ContentWorld, source: string): string;
    static escapeContentWorldName(name: string): string;
    generateWrappedCodeForDocumentEnd(): string;
    static escapeCode(code: string): string;
    private updateContentWorldsCreatorScript;
    generateContentWorldsCreatorCode(): string;
    addPluginScript(pluginScript: PluginScript): boolean;
    addPluginScripts(pluginScripts: Array<PluginScript>): void;
    removePluginScript(pluginScript: PluginScript): boolean;
    removeAllUserOnlyScripts(): void;
    removeAllPluginScripts(): void;
    addUserOnlyScript(userOnlyScript: UserScript): boolean;
    getUserOnlyScriptAsList(): HashSet<UserScript>;
    getPluginScriptAsList(): HashSet<PluginScript>;
    removeUserOnlyScript(userOnlyScript: UserScript): boolean;
    removeUserOnlyScriptAt(index: number, injectionTime: UserScriptInjectionTime): boolean;
    removeUserOnlyScriptsByGroupName(groupName: string): void;
    addUserOnlyScripts(userOnlyScripts: Array<UserScript>): void;
    resetContentWorlds(): void;
    getPluginScriptsRequiredInAllContentWorlds(): HashSet<PluginScript>;
    generateCodeForScriptEvaluation(source: string, contentWorld: ContentWorld | null): string;
    private hasContentWorld;
    dispose(): void;
    private addDocumentStartJavaScript;
    getStartScripts(): ScriptItem[];
    private onDocumentStartChanged;
}
