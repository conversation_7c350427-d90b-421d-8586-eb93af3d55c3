import { Any } from '@ohos/flutter_ohos';
import ContentWorld from './ContentWorld';
import { UserScriptInjectionTime } from './UserScriptInjectionTime';
export default class UserScript {
    private groupName;
    private source;
    private injectionTime;
    private contentWorld;
    private allowedOriginRules;
    constructor(groupName: string, source: string, injectionTime: UserScriptInjectionTime, contentWorld: ContentWorld | null, allowedOriginRules: Set<string> | null);
    static fromMap(map: Map<string, Any>): UserScript | null;
    getGroupName(): string;
    setGroupName(groupName: string): void;
    getSource(): string;
    setSource(source: string): void;
    getInjectionTime(): UserScriptInjectionTime;
    setInjectionTime(injectionTime: UserScriptInjectionTime): void;
    getContentWorld(): ContentWorld;
    setContentWorld(contentWorld: ContentWorld): void;
    getAllowedOriginRules(): Set<string>;
    getAllowedOriginRulesArray(): Array<string>;
    setAllowedOriginRules(allowedOriginRules: Set<string>): void;
    equals(o: Any): boolean;
    private checkSetEquals;
}
