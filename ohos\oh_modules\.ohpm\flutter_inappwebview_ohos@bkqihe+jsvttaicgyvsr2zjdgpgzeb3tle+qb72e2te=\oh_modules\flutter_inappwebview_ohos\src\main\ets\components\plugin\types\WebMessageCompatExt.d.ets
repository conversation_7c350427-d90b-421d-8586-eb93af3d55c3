import WebMessagePortCompatExt from './WebMessagePortCompatExt';
import List from "@ohos.util.List";
import { Any } from '@ohos/flutter_ohos';
import web_webview from '@ohos.web.webview';
export default class WebMessageCompatExt {
    private data;
    private type;
    private ports;
    constructor(data: Any, type: number, ports: List<WebMessagePortCompatExt> | null);
    static fromMapWebMessageCompat(message: web_webview.WebMessageExt): WebMessageCompatExt;
    static fromMap(map: Map<string, Any> | null): WebMessageCompatExt | null;
    toMap(): Map<string, Any>;
    getData(): Any;
    setData(data: Any): void;
    getType(): number;
    setType(type: number): void;
    getPorts(): List<WebMessagePortCompatExt>;
    setPorts(ports: List<WebMessagePortCompatExt>): void;
    equals(o: Any): boolean;
    toString(): string;
}
