import { WebMessageChannel } from '../webview/web_message/WebMessageChannel';
import { ValueCallback } from './ValueCallback';
import WebMessage from './WebMessage';
export default class WebMessagePort {
    name: string;
    webMessageChannel: WebMessageChannel | null;
    isClosed: boolean;
    isTransferred: boolean;
    isStarted: boolean;
    constructor(name: string, webMessageChannel: WebMessageChannel | null);
    setWebMessageCallback(callback: ValueCallback<void>): void;
    postMessage(message: WebMessage, callback: ValueCallback<void>): void;
    close(callback: ValueCallback<void>): void;
    dispose(): void;
}
