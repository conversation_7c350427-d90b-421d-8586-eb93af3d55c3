import { Any } from '@ohos/flutter_ohos';
export default class WebMessagePortCompatExt {
    private index;
    private webMessageChannelId;
    constructor(index: number, webMessageChannelId: string);
    static fromMap(map: Map<string, Any>): WebMessagePortCompatExt | null;
    toMap(): Map<string, Any>;
    getIndex(): number;
    setIndex(index: number): void;
    getWebMessageChannelId(): string;
    setWebMessageChannelId(webMessageChannelId: string): void;
    equals(o: Any): boolean;
    hashCode(): number;
    toString(): string;
}
