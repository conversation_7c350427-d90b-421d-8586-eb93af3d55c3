import { Any } from '@ohos/flutter_ohos';
export default class WebResourceErrorExt {
    private type;
    private description;
    constructor(type: number, description: string);
    toMap(): Map<string, Any>;
    static fromWebResourceError(error: WebResourceError): WebResourceErrorExt;
    getType(): number;
    setType(type: number): void;
    getDescription(): string;
    setDescription(description: string): void;
    toString(): string;
}
