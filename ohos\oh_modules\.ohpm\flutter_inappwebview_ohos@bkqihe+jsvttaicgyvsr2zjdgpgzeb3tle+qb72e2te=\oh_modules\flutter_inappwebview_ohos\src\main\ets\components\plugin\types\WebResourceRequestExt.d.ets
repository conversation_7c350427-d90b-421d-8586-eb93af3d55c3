import { Any } from '@ohos/flutter_ohos';
export default class WebResourceRequestExt {
    private url;
    private headers;
    private isRedirect;
    private hasGesture;
    private isForMainFrame;
    private method;
    constructor(url: string, headers: Map<string, string>, isRedirect: boolean, hasGesture: boolean, isForMainFrame: boolean, method: string);
    static fromWebResourceRequest(request: WebResourceRequest): WebResourceRequestExt;
    toMap(): Map<string, Any>;
    getUrl(): string;
    setUrl(url: string): void;
    getHeaders(): Map<string, string>;
    setHeaders(headers: Map<string, string>): void;
    getRedirect(): boolean;
    setRedirect(redirect: boolean): void;
    isHasGesture(): boolean;
    setHasGesture(hasGesture: boolean): void;
    getForMainFrame(): boolean;
    setForMainFrame(forMainFrame: boolean): void;
    getMethod(): string;
    setMethod(method: string): void;
    toString(): string;
}
