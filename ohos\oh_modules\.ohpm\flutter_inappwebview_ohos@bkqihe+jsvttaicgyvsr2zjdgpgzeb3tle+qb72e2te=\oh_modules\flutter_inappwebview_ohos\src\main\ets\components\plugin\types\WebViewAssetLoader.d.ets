import uri from '@ohos.uri';
export interface PathHandler {
    handle(path: string): WebResourceResponse;
}
export declare class AssetsPathHandler implements PathHandler {
    private context;
    constructor(context: Context);
    handle(path: string): WebResourceResponse;
}
export declare class ResourcesPathHandler implements PathHandler {
    private context;
    constructor(context: Context);
    handle(path: string): WebResourceResponse;
}
export declare class InternalStoragePathHandler implements PathHandler {
    private static FORBIDDEN_DATA_DIRS;
    private context;
    private directory;
    constructor(context: Context, directory: string);
    handle(path: string): WebResourceResponse;
}
export declare class PathMatcher {
    static HTTP_SCHEME: string;
    static HTTPS_SCHEME: string;
    mHttpEnabled: boolean;
    mAuthority: string | null;
    mPath: string | null;
    mHandler: PathHandler | null;
    constructor(authority: string, path: string, httpEnabled: boolean, handler: PathHandler);
    match(uri: uri.URI): PathHandler | null;
    getSuffixPath(path: string): string;
}
export default class WebViewAssetLoader {
    static DEFAULT_DOMAIN: string;
    private mMatchers;
    constructor(pathMatchers: Array<PathMatcher>);
    shouldInterceptRequest(url: uri.URI): WebResourceResponse;
}
export declare class WebViewAssetLoaderBuilder {
    private mHttpAllowed;
    private mDomain;
    private mHandlerList;
    setDomain(domain: string): WebViewAssetLoaderBuilder;
    setHttpAllowed(httpAllowed: boolean): WebViewAssetLoaderBuilder;
    addPathHandler(path: string, handler: PathHandler): this;
    build(): WebViewAssetLoader;
}
