import { Any, MethodChannel } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import BaseCallbackResultImpl from './BaseCallbackResultImpl';
import ChannelDelegateImpl from './ChannelDelegateImpl';
import { Disposable } from './Disposable';
import WebResourceResponseExt from './WebResourceResponseExt';
import WebViewAssetLoader, { PathHandler } from './WebViewAssetLoader';
export default class WebViewAssetLoaderExt implements Disposable {
    loader: WebViewAssetLoader;
    customPathHandlers: Array<PathHandlerExt>;
    constructor(loader: WebViewAssetLoader, customPathHandlers: Array<PathHandlerExt>);
    static fromMap(map: Map<string, Any> | null, plugin: InAppWebViewFlutterPlugin, context: Context): WebViewAssetLoaderExt | null;
    dispose(): void;
}
declare class PathHandlerExt implements PathHandler, Disposable {
    protected staticLOG_TAG: string;
    static METHOD_CHANNEL_NAME_PREFIX: string;
    id: string;
    channelDelegate: PathHandlerExtChannelDelegate | null;
    constructor(id: string, plugin: InAppWebViewFlutterPlugin);
    handle(path: string): WebResourceResponse;
    dispose(): void;
}
declare class PathHandlerExtChannelDelegate extends ChannelDelegateImpl {
    private pathHandler;
    constructor(pathHandler: PathHandlerExt, channel: MethodChannel);
    handle(path: string, callback: HandleCallback): void;
    handleSync(path: string): Promise<WebResourceResponseExt>;
    dispose(): void;
}
declare class HandleCallback extends BaseCallbackResultImpl<WebResourceResponseExt> {
    decodeResult(obj: Any): WebResourceResponseExt | null;
}
export {};
