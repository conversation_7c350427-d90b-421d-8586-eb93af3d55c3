import { MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import { FlutterWebView } from './in_app_webview/FlutterWebView';
import { Context as Context } from "@ohos.abilityAccessCtrl";
import web_webview from '@ohos.web.webview';
export declare class InAppWebViewManager extends ChannelDelegateImpl {
    private plugin;
    keepAliveWebViews: Map<string, FlutterWebView | undefined>;
    controller: web_webview.WebviewController;
    windowWebViewMessages: Map<number, ControllerHandler>;
    windowAutoincrementId: number;
    debuggingEnabled: boolean;
    constructor(plugin: InAppWebViewFlutterPlugin);
    onMethodCall(call: MethodCall, result: MethodResult): void;
    disposeKeepAlive(keepAliveId: string): void;
    clearAllCache(context: Context, includeDiskFiles: boolean): void;
    dispose(): void;
}
