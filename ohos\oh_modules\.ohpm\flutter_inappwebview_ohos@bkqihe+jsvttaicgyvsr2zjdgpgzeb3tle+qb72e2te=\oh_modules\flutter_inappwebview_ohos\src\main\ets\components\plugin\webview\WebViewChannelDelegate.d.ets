import { Any, MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import InAppWebView from './in_app_webview/InAppWebView';
import List from "@ohos.util.List";
import HitTestResult from '../types/HitTestResult';
import DownloadStartRequest from '../types/DownloadStartRequest';
import BaseCallbackResultImpl from '../types/BaseCallbackResultImpl';
import JsAlertResponse from '../types/JsAlertResponse';
import JsConfirmResponse from '../types/JsConfirmResponse';
import JsPromptResponse from '../types/JsPromptResponse';
import JsBeforeUnloadResponse from '../types/JsBeforeUnloadResponse';
import CreateWindowAction from '../types/CreateWindowAction';
import GeolocationPermissionShowPromptResponse from '../types/GeolocationPermissionShowPromptResponse';
import PermissionResponse from '../types/PermissionResponse';
import WebResourceRequestExt from '../types/WebResourceRequestExt';
import WebResourceErrorExt from '../types/WebResourceErrorExt';
import WebResourceResponseExt from '../types/WebResourceResponseExt';
import HttpAuthResponse from '../types/HttpAuthResponse';
import HttpAuthenticationChallenge from '../types/HttpAuthenticationChallenge';
import CustomSchemeResponse from '../types/CustomSchemeResponse';
import { NavigationActionPolicy } from '../types/NavigationActionPolicy';
import NavigationAction from '../types/NavigationAction';
import ServerTrustAuthResponse from '../types/ServerTrustAuthResponse';
import ServerTrustChallenge from '../types/ServerTrustChallenge';
import ClientCertResponse from '../types/ClientCertResponse';
import ClientCertChallenge from '../types/ClientCertChallenge';
import SafeBrowsingResponse from '../types/SafeBrowsingResponse';
export default class WebViewChannelDelegate extends ChannelDelegateImpl {
    private webView;
    constructor(webView: InAppWebView, channel: MethodChannel);
    onMethodCall(call: MethodCall, result: MethodResult): Promise<void>;
    onFindResultReceived(activeMatchOrdinal: number, numberOfMatches: number, isDoneCounting: boolean): void;
    onLongPressHitTestResult(hitTestResult: HitTestResult): void;
    onScrollChanged(x: number, y: number): void;
    onDownloadStartRequest(downloadStartRequest: DownloadStartRequest): void;
    onCreateContextMenu(hitTestResult: HitTestResult): void;
    onOverScrolled(scrollX: number, scrollY: number, clampedX: boolean, clampedY: boolean): void;
    onContextMenuActionItemClicked(itemId: number, itemTitle: string): void;
    onHideContextMenu(): void;
    onEnterFullscreen(): void;
    onExitFullscreen(): void;
    onJsAlert(url: string, message: string, isMainFrame: boolean | null, callback: JsAlertCallback): void;
    onJsConfirm(url: string, message: string, isMainFrame: boolean | null, callback: JsConfirmCallback): void;
    onJsPrompt(url: string, message: string, defaultValue: string, isMainFrame: boolean | null, callback: JsPromptCallback): void;
    onJsBeforeUnload(url: string, message: string, callback: JsBeforeUnloadCallback): void;
    onCreateWindow(createWindowAction: CreateWindowAction, callback: CreateWindowCallback): void;
    onCloseWindow(): void;
    onGeolocationPermissionsShowPrompt(origin: string, callback: GeolocationPermissionsShowPromptCallback): void;
    onGeolocationPermissionsHidePrompt(): void;
    onConsoleMessage(message: string, messageLevel: number): void;
    onProgressChanged(progress: number): void;
    onTitleChanged(title: string): void;
    onReceivedIcon(icon: ArrayBuffer): void;
    onReceivedTouchIconUrl(url: string, precomposed: boolean): void;
    onPermissionRequest(origin: string, resources: Array<string>, frame: Any | null, callback: PermissionRequestCallback): void;
    onPermissionRequestCanceled(origin: string, resources: List<string>): void;
    onLoadStart(url: string): void;
    onLoadStop(url: string): void;
    onUpdateVisitedHistory(url: string, isReload: boolean): void;
    onRequestFocus(): void;
    onReceivedError(request: WebResourceRequestExt, error: WebResourceErrorExt): void;
    onReceivedHttpError(request: WebResourceRequestExt, errorResponse: WebResourceResponseExt): void;
    onReceivedHttpAuthRequest(challenge: HttpAuthenticationChallenge, callback: ReceivedHttpAuthRequestCallback): Promise<void>;
    onReceivedServerTrustAuthRequest(challenge: ServerTrustChallenge, callback: ReceivedServerTrustAuthRequestCallback): Promise<void>;
    onReceivedClientCertRequest(challenge: ClientCertChallenge, callback: ReceivedClientCertRequestCallback): Promise<void>;
    shouldInterceptRequest(request: WebResourceRequestExt): WebResourceResponseExt;
    onLoadResourceWithCustomScheme(request: WebResourceRequestExt): Promise<CustomSchemeResponse>;
    shouldOverrideUrlLoading(navigationAction: NavigationAction, callback: ShouldOverrideUrlLoadingCallback): void;
    onCallJsHandler(handlerName: string, args: string, callback: CallJsHandlerCallback): void;
    onPrintRequest(url: string, printJobId: string, callback: PrintRequestCallback): void;
    onZoomScaleChanged(oldScale: number, newScale: number): void;
    onRenderProcessGone(didCrash: boolean, rendererPriorityAtExit: number): void;
    onPageCommitVisible(url: string): void;
    onFormResubmission(url: string, callback: FormResubmissionCallback): void;
    onSafeBrowsingHit(url: string, threatType: ThreatType, callback: SafeBrowsingHitCallback): void;
    onRenderProcessUnresponsive(url: string, callback: RenderProcessUnresponsiveCallback): void;
    onRenderProcessResponsive(url: string, callback: RenderProcessResponsiveCallback): void;
}
export declare class JsAlertCallback extends BaseCallbackResultImpl<JsAlertResponse> {
    decodeResult(obj: Any): JsAlertResponse | null;
}
export declare class JsConfirmCallback extends BaseCallbackResultImpl<JsConfirmResponse> {
    decodeResult(obj: Any): JsConfirmResponse | null;
}
export declare class JsPromptCallback extends BaseCallbackResultImpl<JsPromptResponse> {
    decodeResult(obj: Any): JsPromptResponse | null;
}
export declare class JsBeforeUnloadCallback extends BaseCallbackResultImpl<JsBeforeUnloadResponse> {
    decodeResult(obj: Any): JsBeforeUnloadResponse | null;
}
export declare class CreateWindowCallback extends BaseCallbackResultImpl<Boolean> {
    decodeResult(obj: Any): Boolean | null;
}
export declare class GeolocationPermissionsShowPromptCallback extends BaseCallbackResultImpl<GeolocationPermissionShowPromptResponse> {
    decodeResult(obj: Any): GeolocationPermissionShowPromptResponse | null;
}
export declare class PermissionRequestCallback extends BaseCallbackResultImpl<PermissionResponse> {
    decodeResult(obj: Any): PermissionResponse | null;
}
export declare class ReceivedHttpAuthRequestCallback extends BaseCallbackResultImpl<HttpAuthResponse> {
    decodeResult(obj: Any): HttpAuthResponse | null;
}
export declare class ShouldOverrideUrlLoadingCallback extends BaseCallbackResultImpl<NavigationActionPolicy> {
    decodeResult(obj: Any): NavigationActionPolicy;
}
export declare class CallJsHandlerCallback extends BaseCallbackResultImpl<Any> {
    decodeResult(obj: Any): Any;
}
export declare class PrintRequestCallback extends BaseCallbackResultImpl<boolean> {
    decodeResult(obj: Any): boolean;
}
export declare class ReceivedServerTrustAuthRequestCallback extends BaseCallbackResultImpl<ServerTrustAuthResponse> {
    decodeResult(obj: Any): ServerTrustAuthResponse | null;
}
export declare class ReceivedClientCertRequestCallback extends BaseCallbackResultImpl<ClientCertResponse> {
    decodeResult(obj: Any): ClientCertResponse | null;
}
export declare class FormResubmissionCallback extends BaseCallbackResultImpl<number> {
    decodeResult(obj: Any): number | null;
}
export declare class SafeBrowsingHitCallback extends BaseCallbackResultImpl<SafeBrowsingResponse> {
    decodeResult(obj: Any): SafeBrowsingResponse | null;
}
export declare class RenderProcessUnresponsiveCallback extends BaseCallbackResultImpl<number> {
    decodeResult(obj: Any): number | null;
}
export declare class RenderProcessResponsiveCallback extends BaseCallbackResultImpl<number> {
    decodeResult(obj: Any): number | null;
}
