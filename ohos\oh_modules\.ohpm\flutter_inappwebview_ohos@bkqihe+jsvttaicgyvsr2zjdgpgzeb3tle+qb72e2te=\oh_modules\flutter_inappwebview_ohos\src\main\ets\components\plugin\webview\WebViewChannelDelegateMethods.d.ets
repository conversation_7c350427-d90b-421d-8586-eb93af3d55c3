export declare enum WebViewChannelDelegateMethods {
    getUrl = 0,
    getTitle = 1,
    getProgress = 2,
    loadUrl = 3,
    postUrl = 4,
    loadData = 5,
    loadFile = 6,
    evaluateJavascript = 7,
    injectJavascriptFileFromUrl = 8,
    injectCSSCode = 9,
    injectCSSFileFromUrl = 10,
    reload = 11,
    goBack = 12,
    canGoBack = 13,
    goForward = 14,
    canGoForward = 15,
    goBackOrForward = 16,
    canGoBackOrForward = 17,
    stopLoading = 18,
    isLoading = 19,
    takeScreenshot = 20,
    setSettings = 21,
    getSettings = 22,
    close = 23,
    show = 24,
    hide = 25,
    isHidden = 26,
    getCopyBackForwardList = 27,
    startSafeBrowsing = 28,
    /**
     * @deprecated
     */
    clearCache = 29,
    clearSslPreferences = 30,
    /**
     * @deprecated
     */
    findAll = 31,
    findNext = 32,
    clearMatches = 33,
    scrollTo = 34,
    scrollBy = 35,
    pause = 36,
    resume = 37,
    pauseTimers = 38,
    resumeTimers = 39,
    printCurrentPage = 40,
    getContentHeight = 41,
    getContentWidth = 42,
    zoomBy = 43,
    getOriginalUrl = 44,
    getZoomScale = 45,
    getSelectedText = 46,
    getHitTestResult = 47,
    pageDown = 48,
    pageUp = 49,
    saveWebArchive = 50,
    zoomIn = 51,
    zoomOut = 52,
    clearFocus = 53,
    setContextMenu = 54,
    requestFocusNodeHref = 55,
    requestImageRef = 56,
    getScrollX = 57,
    getScrollY = 58,
    getCertificate = 59,
    clearHistory = 60,
    addUserScript = 61,
    removeUserScript = 62,
    removeUserScriptsByGroupName = 63,
    removeAllUserScripts = 64,
    callAsyncJavaScript = 65,
    isSecureContext = 66,
    createWebMessageChannel = 67,
    postWebMessage = 68,
    addWebMessageListener = 69,
    canScrollVertically = 70,
    canScrollHorizontally = 71,
    isInFullscreen = 72,
    clearFormData = 73
}
