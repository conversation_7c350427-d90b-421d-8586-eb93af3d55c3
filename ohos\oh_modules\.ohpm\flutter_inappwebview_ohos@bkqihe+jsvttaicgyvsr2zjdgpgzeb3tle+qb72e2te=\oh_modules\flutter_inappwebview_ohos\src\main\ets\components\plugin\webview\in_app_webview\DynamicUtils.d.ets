import { Any } from '@ohos/flutter_ohos';
import { DVModelParameters } from '@ohos/flutter_ohos/src/main/ets/view/DynamicView/dynamicView';
export declare class DynamicUtils {
    static getParams(params: DVModelParameters, element: string): string | Any;
    static setParams(params: DVModelParameters, key: string, element: Any): void;
}
export declare class DVModelJson {
    compType: string;
    children: Array<Any>;
    attributes: Any;
    events: Any;
    build: Any;
    constructor(compType: string, children: Array<Any>, attributes: Any, events: Any, build?: Any);
}
