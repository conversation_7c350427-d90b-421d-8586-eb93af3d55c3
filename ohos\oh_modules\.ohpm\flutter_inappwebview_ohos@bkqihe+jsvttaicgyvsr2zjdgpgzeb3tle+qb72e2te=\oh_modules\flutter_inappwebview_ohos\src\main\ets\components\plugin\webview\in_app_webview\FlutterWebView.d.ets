import { DVModel } from '@ohos/flutter_ohos/src/main/ets/view/DynamicView/dynamicView';
import { Params } from '@ohos/flutter_ohos/src/main/ets/plugin/platform/PlatformView';
import PlatformWebView from '../PlatformWebView';
import InAppWebViewFlutterPlugin from '../../InAppWebViewFlutterPlugin';
import InAppWebView from './InAppWebView';
import { Any } from '@ohos/flutter_ohos';
import PullToRefreshLayout from '../../pull_to_refresh/PullToRefreshLayout';
export declare class FlutterWebView extends PlatformWebView {
    private webView;
    pullToRefreshLayout: PullToRefreshLayout | null;
    keepAliveId: string | null;
    constructor(plugin: InAppWebViewFlutterPlugin, context: Context, id: number, params: Map<string, Any>);
    getView(): WrappedBuilder<[Params]>;
    getController(): import("flutter_inappwebview_ohos/../../../../../../../../softs/DevEco/DevEco Studio/sdk/default/openharmony/ets/api/@ohos.web.webview").default.WebviewController;
    getWebView(): InAppWebView;
    makeInitialLoad(params: Map<string, Any>): void;
    dispose(): void;
    onInputConnectionLocked(): void;
    onInputConnectionUnlocked(): void;
    onFlutterViewAttached(flutterView: DVModel): void;
    onFlutterViewDetached(): void;
}
