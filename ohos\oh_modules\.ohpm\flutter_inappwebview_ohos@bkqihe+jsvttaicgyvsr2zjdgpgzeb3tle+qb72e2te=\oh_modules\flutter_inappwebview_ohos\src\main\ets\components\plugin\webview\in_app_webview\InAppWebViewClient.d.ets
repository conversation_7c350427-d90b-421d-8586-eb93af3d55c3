import { Any } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../../InAppWebViewFlutterPlugin';
import InAppWebView from './InAppWebView';
import URLCredential from '../../types/URLCredential';
import List from "@ohos.util.List";
export default class InAppWebViewClient {
    static previousAuthRequestFailureCount: number;
    static credentialsProposed: List<URLCredential> | null;
    private plugin;
    private inAppWebView;
    private inAppBrowserDelegate;
    private fullScreenExitHandler;
    private isOriginalLayoutFullScreen;
    private isOriginalFullScreen;
    progress: number;
    private permissionValues;
    constructor(plugin: InAppWebViewFlutterPlugin, inAppWebView: InAppWebView);
    onAlert: (event: Any) => boolean;
    onBeforeUnload: (event: Any) => boolean;
    onConfirm: (event: Any) => boolean;
    onPrompt: (event: Any, uiObject: Any) => boolean;
    onConsole: (event: Any) => boolean;
    onDownloadStart: (event: Any) => void;
    onErrorReceive: (event: Any) => void;
    onHttpErrorReceive: (event: Any) => void;
    onPageBegin: (event: Any) => void;
    onPageEnd: (event: Any) => void;
    onProgressChange: (event: Any) => void;
    onTitleReceive: (event: Any) => void;
    onRefreshAccessedHistory: (event: Any) => void;
    onRenderExited: (event: Any) => void;
    onShowFileSelector: (event: Any) => boolean;
    onResourceLoad: (event: Any) => void;
    onScaleChange: (event: Any) => void;
    onInterceptRequest: (event: Any) => WebResourceResponse;
    onHttpAuthRequest: (event: Any) => boolean;
    onSslErrorEventReceive: (event: Any) => void;
    onClientAuthenticationRequest: (event: Any) => void;
    onPermissionRequest: (event: Any) => void;
    onContextMenuShow: (event: Any) => boolean;
    onContextMenuHide: () => void;
    onScroll: (event: Any) => void;
    onGeolocationShow: (event: Any) => void;
    onGeolocationHide: () => void;
    onFullScreenEnter: (event: Any) => void;
    onFullScreenExit: () => void;
    onWindowNew: (event: Any) => void;
    onWindowExit: () => void;
    onSearchResultReceive: (event: Any) => void;
    onDataResubmitted: (event: Any) => void;
    onPageVisible: (event: Any) => void;
    onInterceptKeyEvent: (event: Any) => boolean;
    onTouchIconUrlReceived: (event: Any) => void;
    onFaviconReceived: (event: Any) => Promise<void>;
    onAudioStateChanged: (event: Any) => void;
    onFirstContentfulPaint: (event: Any) => void;
    onLoadIntercept: (event: Any) => boolean;
    onOverrideUrlLoading: (request: WebResourceRequest) => boolean;
    onRequestSelected: () => void;
    onScreenCaptureRequest: (event: Any) => void;
    onOverScroll: (event: Any) => void;
    onNavigationEntryCommitted: () => void;
    onSafeBrowsingCheckResult: (threatType: ThreatType) => void;
    onNativeEmbedLifecycleChange: (callback: NativeEmbedDataInfo) => void;
    onNativeEmbedGestureEvent: (callback: NativeEmbedTouchInfo) => void;
    onRenderProcessNotResponding: (data: RenderProcessNotRespondingData) => void;
    onRenderProcessResponding: () => void;
    private requestPermissions;
    private checkPermissionGrant;
    private onShouldOverrideUrlLoading;
    private onReceivedHttpAuthRequest;
    private onReceivedSslErrorEventReceive;
    private onReceivedClientAuthenticationRequest;
    private loadCustomJavaScriptOnPageStarted;
    private loadCustomJavaScriptOnPageFinished;
    private needsCameraPermission;
    private acceptsVideo;
    private acceptsImages;
    private getAcceptedMimeType;
    private acceptsAny;
    private arrayContainsString;
    private isArrayEmpty;
}
