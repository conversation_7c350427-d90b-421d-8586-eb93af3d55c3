import { Any } from '@ohos/flutter_ohos';
import ISettings from '../../ISettings';
import InAppWebViewInterface from '../InAppWebViewInterface';
export default class InAppWebViewSettings implements ISettings<InAppWebViewInterface> {
    domStorageAccess: boolean;
    fileAccess: boolean;
    allowFileAccessFromFileURLs: boolean;
    imageAccess: boolean;
    javaScriptAccess: boolean;
    overScrollMode: OverScrollMode;
    mixedMode: MixedMode;
    onlineImageAccess: boolean;
    zoomAccess: boolean;
    overviewModeAccess: boolean;
    databaseAccess: boolean;
    geolocationAccess: boolean;
    mediaPlayGestureAccess: boolean;
    multiWindowAccess: boolean;
    horizontalScrollBarAccess: boolean;
    verticalScrollBarAccess: boolean;
    cacheMode: CacheMode;
    textZoomRatio: number;
    initialScale: number;
    blockNetwork: boolean;
    defaultFixedFontSize: number;
    defaultFontSize: number;
    minFontSize: number;
    minLogicalFontSize: number;
    webFixedFont: string;
    webSansSerifFont: string;
    webSerifFont: string;
    webStandardFont: string;
    webFantasyFont: string;
    webCursiveFont: string;
    darkMode: WebDarkMode;
    forceDarkAccess: boolean;
    pinchSmooth: boolean;
    allowWindowOpenMethod: boolean;
    layoutMode: WebLayoutMode;
    enableNativeEmbedMode: boolean;
    userAgent: string;
    useHybridComposition: boolean;
    useOnLoadResource: boolean;
    useOnDownloadStart: boolean;
    useShouldInterceptAjaxRequest: boolean;
    interceptOnlyAsyncAjaxRequests: boolean;
    useShouldInterceptFetchRequest: boolean;
    useShouldInterceptRequest: boolean;
    resourceCustomSchemes: Array<string> | null;
    webViewAssetLoader: Map<string, Any> | null;
    contentBlockers: Array<Map<string, Map<string, Any>>>;
    useShouldOverrideUrlLoading: boolean;
    regexToCancelSubFramesLoading: string | null;
    disableDefaultErrorPage: boolean;
    useOnRenderProcessGone: boolean;
    thirdPartyCookiesEnabled: boolean;
    applicationNameForUserAgent: string | null;
    transparentBackground: boolean;
    incognito: boolean;
    cacheEnabled: boolean;
    parse(settings: Map<string, Any>): ISettings<InAppWebViewInterface>;
    toMap(): Map<string, Any>;
    getRealSettings(inAppWebView: InAppWebViewInterface): Map<string, Any>;
    private convertOverScrollModeToDart;
    private convertMixedModeToDart;
    private convertCacheModeToDart;
    private convertDartModeToDart;
    private convertLayoutModeToDart;
}
