import { Any } from '@ohos/flutter_ohos';
import { Disposable } from '../../types/Disposable';
import WebMessagePort from '../../types/WebMessagePort';
import InAppWebViewInterface from '../InAppWebViewInterface';
import { WebMessageChannelChannelDelegate } from './WebMessageChannelChannelDelegate';
import List from "@ohos.util.List";
import { ValueCallback } from '../../types/ValueCallback';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import WebMessageCompatExt from '../../types/WebMessageCompatExt';
import web_webview from '@ohos.web.webview';
export declare class WebMessageChannel implements Disposable {
    id: string;
    channelDelegate: WebMessageChannelChannelDelegate | null;
    compatPorts: web_webview.WebMessagePort[];
    ports: List<WebMessagePort>;
    webView: InAppWebViewInterface | null;
    constructor(id: string, webView: InAppWebViewInterface);
    initJsInstance(webView: InAppWebViewInterface, callback: ValueCallback<WebMessageChannel>): void;
    setWebMessageCallbackForInAppWebView(index: number, message: WebMessageCompatExt | null, result: MethodResult): void;
    postMessageForInAppWebView(index: number, message: WebMessageCompatExt, result: MethodResult): void;
    closeForInAppWebView(index: number, result: MethodResult): void;
    onMessage(index: number, message: WebMessageCompatExt | null): void;
    toMap(): Map<string, Any>;
    dispose(): void;
}
