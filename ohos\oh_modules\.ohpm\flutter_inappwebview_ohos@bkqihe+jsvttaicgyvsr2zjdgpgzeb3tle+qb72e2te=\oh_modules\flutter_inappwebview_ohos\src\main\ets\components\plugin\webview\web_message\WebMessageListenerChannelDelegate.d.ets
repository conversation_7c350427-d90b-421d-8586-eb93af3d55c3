import { Method<PERSON>all, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../../types/ChannelDelegateImpl';
import WebMessageCompatExt from '../../types/WebMessageCompatExt';
import WebMessageListener from './WebMessageListener';
export default class WebMessageListenerChannelDelegate extends ChannelDelegateImpl {
    private webMessageListener;
    constructor(webMessageListener: WebMessageListener, channel: MethodChannel);
    onMethodCall(call: MethodCall, result: MethodResult): void;
    onPostMessage(message: WebMessageCompatExt, sourceOrigin: string, isMainFrame: boolean): void;
    dispose(): void;
}
