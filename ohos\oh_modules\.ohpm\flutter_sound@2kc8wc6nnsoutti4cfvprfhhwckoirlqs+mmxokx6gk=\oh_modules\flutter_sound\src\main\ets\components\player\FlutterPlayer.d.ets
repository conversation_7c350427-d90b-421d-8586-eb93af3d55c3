/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Any } from '@ohos/flutter_ohos';
import { FlutterSoundPlayerCallback } from '../plugin/FlutterSoundPlayerCallback';
import { t_CODEC, t_PLAYER_STATE } from '../plugin/FlutterSoundTypes';
export declare class FlutterPlayer {
    private player;
    private latentVolume;
    private latentSpeed;
    private latentSeek;
    private myPlayerId;
    private intervalID;
    static currentPlayerID: number;
    static ERR_UNKNOWN: string;
    m_callback: FlutterSoundPlayerCallback | null;
    private subsDurationMillis;
    constructor(callback: FlutterSoundPlayerCallback);
    openPlayer(): boolean;
    closePlayer(): Promise<void>;
    getPlayerState(): t_PLAYER_STATE;
    startPlayerFromMic(numChannels: number, sampleRate: number, blockSize: number): Promise<boolean>;
    startPlayer(codec: t_CODEC, fromURI: string, dataBuffer: ArrayBuffer, numChannels: number, sampleRate: number, bufferSize: number): Promise<boolean>;
    stopPlayer(): Promise<void>;
    pausePlayer(): Promise<boolean>;
    resumePlayer(): Promise<boolean>;
    seekToPlayer(millis: number): boolean;
    setVolume(volume: number): boolean;
    setSpeed(speed: number): boolean;
    setSubscriptionDuration(duration: number): void;
    getProgress(): Map<string, Any>;
    feed(data: ArrayBuffer): number;
    isDecoderSupported(codec: t_CODEC): boolean;
    private stop;
    private getTempFileName;
    private deleteTempFile;
    private temporayFile;
    private getPath;
    private setTimer;
    private playerSettings;
}
