/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { BinaryMessenger, MethodCall } from '@ohos/flutter_ohos';
import { MethodCallHandler, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { FlutterSoundManager } from './FlutterSoundManager';
import common from "@ohos.app.ability.common";
export declare class FlutterSoundPlayerManager extends FlutterSoundManager implements MethodCallHandler {
    static context: common.UIAbilityContext | undefined;
    static flutterSoundPlayerPlugin: FlutterSoundPlayerManager;
    private isStartContinuousTask;
    private session?;
    static attachFlautoPlayer(ctx: common.UIAbilityContext | undefined, messager: BinaryMessenger | undefined): void;
    getManager(): FlutterSoundPlayerManager;
    onMethodCall(call: MethodCall, result: MethodResult): void;
    startContinuousTask(): Promise<void>;
    stopContinuousTask(): Promise<void>;
}
