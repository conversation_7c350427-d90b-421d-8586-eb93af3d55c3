/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { FlutterSoundManager } from './FlutterSoundManager';
import { t_LOG_LEVEL } from './FlutterSoundTypes';
export declare abstract class FlutterSoundSession {
    slotNo: number;
    constructor(call: MethodCall);
    init(slot: number): void;
    abstract getPlugin(): FlutterSoundManager;
    releaseSession(): void;
    abstract getStatus(): number;
    abstract reset(call: MethodCall, result: MethodResult): void;
    invokeMethodWithString(methodName: string, success: boolean, arg: string): void;
    invokeMethodWithInteger(methodName: string, success: boolean, arg: number): void;
    invokeMethodWithBoolean(methodName: string, success: boolean, arg: boolean): void;
    invokeMethodWithMap(methodName: string, success: boolean, dic: Map<string, ESObject>): void;
    log(level: t_LOG_LEVEL, msg: string): void;
}
