/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export declare enum t_PLAYER_STATE {
    PLAYER_IS_STOPPED = 0,
    PLAYER_IS_PLAYING = 1,
    PLAYER_IS_PAUSED = 2
}
export declare enum t_LOG_LEVEL {
    VERBOSE = 0,
    DBG = 1,
    INFO = 2,
    WARNING = 3,
    ERROR = 4,
    WTF = 5,
    NOTHING = 6
}
export declare enum t_CODEC {
    defaultCodec = 0,
    aacADTS = 1,
    opusOGG = 2,
    opusCAF = 3,
    mp3 = 4,
    vorbisOGG = 5,
    pcm16 = 6,
    pcm16WAV = 7,
    pcm16AIFF = 8,
    pcm16CAF = 9,
    flac = 10,
    aacMP4 = 11,
    amrNB = 12,
    amrWB = 13,
    pcm8 = 14,
    pcmFloat32 = 15,
    pcmWebM = 16,
    opusWebM = 17,
    vorbisWebM = 18
}
export declare enum t_RECORDER_STATE {
    RECORDER_IS_STOPPED = 0,
    RECORDER_IS_PAUSED = 1,
    RECORDER_IS_RECORDING = 2
}
export declare enum t_AUDIO_SOURCE {
    defaultSource = 0,
    microphone = 1,
    voiceDownlink = 2,
    camCorder = 3,
    remote_submix = 4,
    unprocessed = 5,
    voice_call = 6,
    voice_communication = 7,
    voice_performance = 8,
    voice_recognition = 9,
    voiceUpLink = 10,
    bluetoothHFP = 11,
    headsetMic = 12,
    lineIn = 13
}
export declare enum t_PLAY_BACK_SPEED {
    SPEED_0_125_X = 0.125,
    SPEED_0_25_X = 0.25,
    SPEED_0_5_X = 0.5,
    SPEED_0_75_X = 0.75,
    SPEED_1_0_X = 1,
    SPEED_1_25_X = 1.25,
    SPEED_1_5_X = 1.5,
    SPEED_1_75_X = 1.75,
    SPEED_2_0_X = 2
}
