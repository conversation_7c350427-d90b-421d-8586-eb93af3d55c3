import { <PERSON><PERSON>all, <PERSON><PERSON>han<PERSON>, MethodResult } from "@ohos/flutter_ohos";
import { IDiffDevOA<PERSON>, OAuthCallback, OAuthErrCode } from "@tencent/wechat_open_sdk";
export declare class FluwxAuthHandler implements OAuthCallback {
    private channel;
    private diffDevOauth;
    constructor(channel: MethodChannel);
    onGotQRCode: (base64JpegImageBuffer: string) => void;
    onQRCodeScanned: () => void;
    onAuthFinish: (authCode: string) => void;
    onAuthError: (errCode: OAuthErrCode, errMsg: string) => void;
    getDiffDevOauth(): IDiffDevOAuth;
    sendAuth(call: Method<PERSON><PERSON>, result: MethodResult): Promise<void>;
    authByQRCode(call: MethodCall, result: MethodResult): void;
    stopAuthByQRCode(result: MethodResult): void;
}
