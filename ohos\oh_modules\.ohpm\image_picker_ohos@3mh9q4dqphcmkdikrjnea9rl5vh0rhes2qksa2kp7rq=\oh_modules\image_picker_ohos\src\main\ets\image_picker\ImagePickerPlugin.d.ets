import AbilityAware from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityAware';
import { AbilityPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityPluginBinding';
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import common from '@ohos.app.ability.common';
import Ability from '@ohos.app.ability.Ability';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import ImagePickerDelegate from './ImagePickerDelegate';
import { GeneralOptions, ImagePickerApi, ImageSelectionOptions, SourceSpecification, Result, MediaSelectionOptions, CacheRetrievalResult, VideoSelectionOptions } from './Messages';
import UIAbility from '@ohos.app.ability.UIAbility';
import ArrayList from '@ohos.util.ArrayList';
export default class ImagePickerPlugin implements FlutterPlugin, AbilityAware {
    private static TAG;
    private pluginBinding;
    private state;
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    setup(messenger: BinaryMessenger, context: common.Context, ability: UIAbility, binding: AbilityPluginBinding): void;
    constructor(delegate?: ImagePickerDelegate, ability?: UIAbility);
    pickImages(source: SourceSpecification, options: ImageSelectionOptions, generalOptions: GeneralOptions, result: Result<ArrayList<string>>): void;
    pickVideos(source: SourceSpecification, options: VideoSelectionOptions, generalOptions: GeneralOptions, result: Result<ArrayList<string>>): void;
    pickMedia(mediaSelectionOptions: MediaSelectionOptions, generalOptions: GeneralOptions, result: Result<ArrayList<string>>): void;
    retrieveLostResults(): Promise<CacheRetrievalResult | null>;
    getAbilityState(): AbilityState | null;
    static constructorDelegate(setupAbility: UIAbility): ImagePickerDelegate;
    getImagePickerDelegate(): ImagePickerDelegate | null;
    setCameraDevice(delegate: ImagePickerDelegate, source: SourceSpecification): void;
}
declare class AbilityState {
    private ability;
    private context;
    private abilityBinding;
    private messenger;
    private delegate;
    private abilityLifecycleCallback;
    constructor(ability: UIAbility, delegate?: ImagePickerDelegate | null, messenger?: BinaryMessenger, handler?: ImagePickerApi, abilityBinding?: AbilityPluginBinding, context?: common.Context);
    release(): void;
    getAbility(): Ability | null;
    getDelegate(): ImagePickerDelegate | null;
}
export {};
