import common from '@ohos.app.ability.common';
import ExifDataCopier from './ExifDataCopier';
export default class ImageResizer {
    private readonly context;
    private readonly exifDataCopier;
    constructor(context: common.Context, exifDataCopier: ExifDataCopier);
    resizeImageIfNeeded(imagePath: string, maxWidth: number, maxHeight: number, imageQuality: number): Promise<string>;
    private calculateTargetSize;
    private createImageOnExternalDirectory;
}
