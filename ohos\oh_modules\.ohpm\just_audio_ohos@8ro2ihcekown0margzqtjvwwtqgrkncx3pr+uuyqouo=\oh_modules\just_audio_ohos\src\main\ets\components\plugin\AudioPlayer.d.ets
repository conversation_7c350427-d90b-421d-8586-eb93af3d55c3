/**
 * Copyright (C) 2024 Huawei Device Co., Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR AInvoke avPlayer NY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 * */
import { MethodCallHandler, MethodCall, MethodResult } from '@ohos/flutter_ohos';
import { BinaryMessenger } from '@ohos/flutter_ohos';
import { PlaybackState, onChangeMediaAvPlayerState } from './MediaAvPlayer';
export declare class AudioPlayer implements MethodCallHandler, onChangeMediaAvPlayerState {
    private pendingPlaybackEvent;
    private methodChannel;
    private eventChannel;
    private dataEventChannel;
    private bufferedPosition;
    static TIME_UNSET: number;
    static AUDIO_SESSION_ID_UNSET: number;
    private initialPos;
    private initialIndex;
    private prepareResult;
    private playResult;
    private seekResult;
    private player;
    private mediaSource;
    private mediaSources;
    private currentIndex;
    private audioSessionId;
    private processingState;
    private seekPos;
    private updatePosition;
    private updateTime;
    private intervalID;
    constructor(messenger: BinaryMessenger, id: string);
    onUpdateCurrentIndex(updateIndex: number): void;
    broadcastImmediatePlaybackEvent(): void;
    completeSeek(): void;
    setAudioSessionId(audioSessionId: number): void;
    onAudioSessionIdChanged(audioSessionId: number): void;
    onPlaybackStateChanged(state: PlaybackState): void;
    private updatePositionIfChanged;
    play(result: MethodResult): void;
    pause(result: MethodResult): void;
    private setVolume;
    private setSpeed;
    private seek;
    private abortSeek;
    onMethodCall(call: MethodCall, result: MethodResult): Promise<void>;
    onStartBuffering(): void;
    onEndBuffering(): void;
    private setShuffleOrder;
    dispose(): void;
    private decodeAudioSource;
    private getAudioSourcesArray;
    private getAudioSources;
    private mapGet;
    private getAudioSource;
    private load;
    abortExistingConnection(): void;
    private sendError;
    onUpdatePosition(): void;
    onUpdateBufferedPosition(bufferedPosition: number): void;
    getCurrentPosition(): number;
    completeInitial(): void;
    enqueuePlaybackEvent(): void;
    private createPlaybackEvent;
    private getDuration;
    private broadcastPendingPlaybackEvent;
}
