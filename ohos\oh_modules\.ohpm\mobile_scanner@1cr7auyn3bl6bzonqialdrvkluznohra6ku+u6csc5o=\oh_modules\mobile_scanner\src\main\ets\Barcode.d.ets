import scanCore from "@hms.core.scan.scanCore";
import { Point } from '@ohos.UiTest';
export declare enum BarcodeFormat {
    unknown = -1,
    all = 0,
    code128 = 1,
    code39 = 2,
    code93 = 4,
    codabar = 8,
    dataMatrix = 16,
    ean13 = 32,
    ean8 = 64,
    itf = 128,
    qrCode = 256,
    upcA = 512,
    upcE = 1024,
    pdf417 = 2048,
    aztec = 4096
}
export declare enum BarcodeType {
    unknown = 0,
    contactInfo = 1,
    email = 2,
    isbn = 3,
    phone = 4,
    product = 5,
    sms = 6,
    text = 7,
    url = 8,
    wifi = 9,
    geo = 10,
    calendarEvent = 11,
    driverLicense = 12
}
export declare class Barcode {
    displayValue: string;
    rawValue: string;
    format: BarcodeFormat;
    type: BarcodeType;
    corners: Array<Point>;
    static convertScanType(value: scanCore.ScanType): BarcodeFormat;
    static convertToScanType(value: BarcodeFormat): scanCore.ScanType;
}
