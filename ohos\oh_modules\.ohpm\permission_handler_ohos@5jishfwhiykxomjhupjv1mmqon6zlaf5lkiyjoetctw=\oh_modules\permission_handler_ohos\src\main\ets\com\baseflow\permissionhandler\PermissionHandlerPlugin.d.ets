import AbilityAware from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityAware';
import { AbilityPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityPluginBinding';
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
/**
 * Platform implementation of the permission_handler Flutter plugin.
 *
 * <p>Instantiate this in an add to app scenario to gracefully handle ability and context changes.
 * See {@code com.example.permissionhandlerexample.MainAbility} for an example.
 *
 */
export default class PermissionHandlerPlugin implements FlutterPlugin, AbilityAware {
    private permissionManager;
    private methodChannel;
    private methodCallHandler;
    private pluginBinding;
    constructor();
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    private startListening;
    private stopListening;
    private startListeningToAbility;
    private stopListeningToAbility;
}
