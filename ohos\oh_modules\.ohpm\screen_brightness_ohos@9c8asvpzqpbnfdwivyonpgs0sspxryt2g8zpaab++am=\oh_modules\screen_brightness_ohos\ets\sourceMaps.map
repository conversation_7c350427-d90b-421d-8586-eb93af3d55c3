{"screen_brightness_ohos|screen_brightness_ohos|1.0.0|build/default/generated/profile/default/ModuleInfo.js": {"version": 3, "file": "ModuleInfo.ts", "sources": ["ohos/build/default/generated/profile/default/ModuleInfo.ts"], "names": [], "mappings": "AACA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;AACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;", "entry-package-info": "screen_brightness_ohos|1.0.0"}, "screen_brightness_ohos|screen_brightness_ohos|1.0.0|Index.ts": {"version": 3, "file": "Index.ets", "sourceRoot": "", "sources": ["ohos/Index.ets"], "names": [], "mappings": "OAAO,0BAA0B;AAEjC,eAAe,0BAA0B,CAAA", "entry-package-info": "screen_brightness_ohos|1.0.0"}, "screen_brightness_ohos|screen_brightness_ohos|1.0.0|src/main/ets/com/aaassseee/screen_brightness_ohos/ScreenBrightnessOhosPlugin.ts": {"version": 3, "file": "ScreenBrightnessOhosPlugin.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/aaassseee/screen_brightness_ohos/ScreenBrightnessOhosPlugin.ets"], "names": [], "mappings": "OAAO,EAIL,aAAa,EAIb,cAAc,EACd,GAAG,EACH,YAAY,EACb;cAVC,aAAa,EACb,oBAAoB,EACpB,UAAU,EAEV,YAAY,EACZ,oBAAoB,EACpB,iBAAiB;OAKZ,QAAQ;YACN,MAAM;OACR,oCAAoC;AAE3C,MAAM,GAAG,GAAG,4BAA4B,CAAA;AAExC,MAAM,CAAC,OAAO,OAAO,0BAA2B,YAAW,aAAa,EAAE,iBAAiB;IACzF,OAAO,CAAC,aAAa,CAAC,EAAE,aAAa,CAAC;IACtC,OAAO,CAAC,sBAAsB,CAAC,EAAE,MAAM,CAAC;IACxC,OAAO,CAAC,2BAA2B,CAAC,EAAE,MAAM,CAAC;IAC7C,OAAO,CAAC,oBAAoB,EAAE,oBAAoB,GAAG,IAAI,GAAG,IAAI,CAAC;IACjE,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IAChD,OAAO,CAAC,yCAAyC,CAAC,EAAE,YAAY,CAAC;IACjE,OAAO,CAAC,0CAA0C,EAAE,oCAAoC,GAAG,IAAI,GAAG,IAAI,CAAC;IACvG,OAAO,CAAC,8CAA8C,CAAC,EAAE,YAAY,CAAC;IACtE,OAAO,CAAC,+CAA+C,EAAE,oCAAoC,GAAG,IAAI,GAAG,IAAI,CAAC;IAC5G,OAAO,CAAC,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC;IACpC,OAAO,CAAC,SAAS,EAAE,OAAO,GAAG,IAAI,CAAC;IAElC,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACxD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;QACD,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,2BAA2B;gBAC9B,IAAI,CAAC,yCAAyC,CAAC,MAAM,CAAC,CAAC;gBACvD,MAAM;YACR,KAAK,2BAA2B;gBAC9B,IAAI,CAAC,yCAAyC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC7D,MAAM;YACR,KAAK,gCAAgC;gBACnC,IAAI,CAAC,8CAA8C,CAAC,MAAM,CAAC,CAAC;gBAC5D,MAAM;YACR,KAAK,gCAAgC;gBACnC,IAAI,CAAC,8CAA8C,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAClE,MAAM;YACR,KAAK,kCAAkC;gBACrC,IAAI,CAAC,gDAAgD,CAAC,MAAM,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,uCAAuC;gBAC1C,IAAI,CAAC,qDAAqD,CAAC,MAAM,CAAC,CAAC;gBACnE,MAAM;YACR,KAAK,aAAa;gBAChB,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;gBACzC,MAAM;YACR,KAAK,cAAc;gBACjB,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAChD,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;gBACvC,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC9C,MAAM;YACR,KAAK,2BAA2B;gBAC9B,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;gBAC7C,MAAM;YACR;gBACE,MAAM,CAAC,cAAc,EAAE,CAAC;gBAAC,MAAM;SAClC;IACH,CAAC;IAED,kBAAkB,IAAI,MAAM;QAC1B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,kBAAkB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACrD,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,wCAAwC,CAAC,CAAC;QAC/G,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,CAAC,8CAA8C,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE,EACjG,uEAAuE,CAAC,CAAC;QAE3E,IAAI,CAAC,yCAAyC,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAC5F,kEAAkE,CAAC,CAAC;QAEtE,IAAI;YACF,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;SAChE;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;SACf;QAED,IAAI,CAAC,0CAA0C,GAAG,IAAI,oCAAoC,CAAC,IAAI,CAAC,CAAC;QACjG,IAAI,CAAC,yCAAyC,CAAC,gBAAgB,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAEtH,IAAI,CAAC,+CAA+C,GAAG,IAAI,oCAAoC,CAAC,IAAI,CAAC,CAAC;QACtG,IAAI,CAAC,8CAA8C,CAAC,gBAAgB,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;IAC7H,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACvD,IAAI,CAAC,aAAa,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,OAAO,CAAC,SAAS,IAAI,IAAI;QACvB,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,WAAW,EAAE;aAC3C,cAAc,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;aAC1G,iBAAiB,EAAE,CAAC;IACzB,CAAC;IAED,OAAO,CAAC,yBAAyB,IAAI,MAAM;QACzC,IAAI,KAAK,GAAG,QAAQ,CAAC,YAAY,CAC/B,UAAU,CAAC,IAAI,CAAC,EAChB,QAAQ,CAAC,OAAO,CAAC,wBAAwB,EACzC,KAAK,CACN,CAAC;QACF,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;IACjC,CAAC;IAED,OAAO,CAAC,yCAAyC,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI;QAC3E,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAC9C,CAAC;IAED,OAAO,CAAC,yCAAyC,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY;QACtF,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,2CAA2C,EAAE,IAAI,CAAC,CAAC;IACxE,CAAC;IAED,OAAO,CAAC,8CAA8C,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI;QAChF,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,mBAAmB,EAAE,CAAC,UAAU,CAAC;QACnE,IAAI,UAAU,IAAI,SAAS,IAAI,UAAU,IAAI,CAAC,EAAE;YAC9C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3B,OAAO;SACR;QAED,IAAI;YACF,UAAU,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SAC5B;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,+CAA+C,EAAE,IAAI,CAAC,CAAC;SAC5E;IACH,CAAC;IAED,OAAO,CAAC,8CAA8C,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAClG,IAAI,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAA;QACxD,IAAI,UAAU,IAAI,IAAI,EAAE;YACtB,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,qCAAqC,EAAE,IAAI,CAAC,CAAC;YAChE,OAAO;SACR;QACD,IAAI;YACF,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,UAAU,CAAC,CAAC;YACjD,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,UAAU,EAAE,mBAAmB,EAAE,CAAC,UAAU,CAAC;YACrF,IAAI,CAAC,wCAAwC,CAAC,UAAU,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtB;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,gDAAgD,EAAE,IAAI,CAAC,CAAC;SAC5E;IACH,CAAC;IAED,OAAO,CAAC,gDAAgD,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI;QAClF,IAAI;YACF,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,2BAA2B,GAAG,CAAC,CAAC,CAAC;YACtC,IAAI,CAAC,wCAAwC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;YAC5E,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtB;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,gDAAgD,EAAE,IAAI,CAAC,CAAC;SAC5E;IACH,CAAC;IAED,OAAO,CAAC,wCAAwC,CAAC,UAAU,EAAE,MAAM;QACjE,IAAI,CAAC,+CAA+C,EAAE,8BAA8B,CAAC,UAAU,CAAC,CAAC;IACnG,CAAC;IAED,OAAO,CAAC,qDAAqD,CAAC,MAAM,EAAE,YAAY;QAChF,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED,OAAO,CAAC,2BAA2B,CAAC,MAAM,EAAE,YAAY;QACtD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAClC,CAAC;IAED,OAAO,CAAC,4BAA4B,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY;QACzE,IAAI,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QACxD,IAAI,WAAW,IAAI,IAAI,EAAE;YACvB,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,sCAAsC,EAAE,IAAI,CAAC,CAAC;YACjE,OAAO;SACR;QAED,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAED,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,YAAY;QACpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC;IAED,OAAO,CAAC,0BAA0B,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY;QACvE,IAAI,SAAS,EAAE,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,oCAAoC,EAAE,IAAI,CAAC,CAAC;YAC/D,OAAO;SACR;QAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAED,OAAO,CAAC,+BAA+B,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI;QACjE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;CACF", "entry-package-info": "screen_brightness_ohos|1.0.0"}, "screen_brightness_ohos|screen_brightness_ohos|1.0.0|src/main/ets/com/aaassseee/screen_brightness_ohos/stream_handler/BaseStreamHandler.ts": {"version": 3, "file": "BaseStreamHandler.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/aaassseee/screen_brightness_ohos/stream_handler/BaseStreamHandler.ets"], "names": [], "mappings": "cAAS,GAAG;cACH,aAAa,EAAE,SAAS;AAEjC,MAAM,OAAO,iBAAkB,YAAW,aAAa;IACrD,SAAS,CAAC,SAAS,EAAE,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;IAE7C,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAG,IAAI;QAC1C,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,QAAQ,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI;QACvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;CACF", "entry-package-info": "screen_brightness_ohos|1.0.0"}, "screen_brightness_ohos|screen_brightness_ohos|1.0.0|src/main/ets/com/aaassseee/screen_brightness_ohos/stream_handler/ScreenBrightnessChangedStreamHandler.ts": {"version": 3, "file": "ScreenBrightnessChangedStreamHandler.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/aaassseee/screen_brightness_ohos/stream_handler/ScreenBrightnessChangedStreamHandler.ets"], "names": [], "mappings": "cAAS,GAAG;cACH,SAAS;OACX,EAAE,iBAAiB,EAAE;AAE5B,KAAK,qBAAqB,GAAG,CAAC,SAAS,EAAE,SAAS,KAAK,IAAI,CAAC;AAE5D,MAAM,CAAC,OAAO,OAAO,oCAAqC,SAAQ,iBAAiB;IACjF,OAAO,CAAC,aAAa,EAAE,qBAAqB,GAAG,IAAI,CAAC;IAEpD,YAAY,aAAa,EAAE,qBAAqB,GAAG,IAAI;QACrD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAG,IAAI;QAC1C,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,OAAO;SACR;QACD,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED,8BAA8B,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;QACtD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,OAAO;SACR;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACrC,CAAC;CACF", "entry-package-info": "screen_brightness_ohos|1.0.0"}}