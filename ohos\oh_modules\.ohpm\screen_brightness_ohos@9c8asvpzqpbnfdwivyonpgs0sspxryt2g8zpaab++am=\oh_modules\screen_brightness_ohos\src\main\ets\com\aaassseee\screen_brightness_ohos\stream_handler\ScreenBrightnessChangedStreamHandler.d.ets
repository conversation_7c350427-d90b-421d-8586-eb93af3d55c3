import { Any } from '@ohos/flutter_ohos';
import { EventSink } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
import { BaseStreamHandler } from './BaseStreamHandler';
type OnListenStartCallback = (eventSink: EventSink) => void;
export default class ScreenBrightnessChangedStreamHandler extends BaseStreamHandler {
    private onListenStart;
    constructor(onListenStart: OnListenStartCallback | null);
    onListen(args: Any, events: EventSink): void;
    addScreenBrightnessToEventSink(brightness: number): void;
}
export {};
