import { EventSink } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
export declare class QueuingEventSink implements EventSink {
    private delegate;
    private eventQueue;
    private done;
    setDelegate(delegate: EventSink | null): void;
    success(event: Object): void;
    error(code: string, message: string, details: Object): void;
    endOfStream(): void;
    private enqueue;
    maybeFlush(): void;
}
