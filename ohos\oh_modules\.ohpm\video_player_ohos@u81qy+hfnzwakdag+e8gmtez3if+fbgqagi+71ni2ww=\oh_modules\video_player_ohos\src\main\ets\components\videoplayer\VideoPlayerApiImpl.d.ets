import { AbilityPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityPluginBinding';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { CreateMessage, LoopingMessage, MixWithOthersMessage, PlaybackSpeedMessage, PositionMessage, TextureMessage, VolumeMessage } from './Messages';
import { FlutterState } from './VideoPlayerPlugin';
import common from '@ohos.app.ability.common';
export declare class VideoPlayerApiImpl {
    private videoPlayers;
    private flutterState;
    private binding;
    private AudioFocus;
    private pixelMaps;
    constructor(flutterState: FlutterState | null, binding: AbilityPluginBinding | null);
    private disposeAllPlayers;
    initialize(): void;
    detach(): void;
    getContext(): common.UIAbilityContext;
    create(arg: CreateMessage): Promise<TextureMessage>;
    dispose(arg: TextureMessage): void;
    setLooping(arg: LoopingMessage): void;
    setVolume(arg: VolumeMessage): void;
    setPlaybackSpeed(arg: PlaybackSpeedMessage): void;
    play(arg: TextureMessage): void;
    position(arg: TextureMessage): PositionMessage;
    seekTo(arg: PositionMessage): void;
    pause(arg: TextureMessage): void;
    setMixWithOthers(arg: MixWithOthersMessage): void;
    setup(binaryMessenger: BinaryMessenger): void;
}
