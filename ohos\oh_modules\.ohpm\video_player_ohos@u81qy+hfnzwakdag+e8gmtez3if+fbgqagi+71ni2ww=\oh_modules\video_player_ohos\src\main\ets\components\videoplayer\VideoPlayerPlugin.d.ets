import AbilityAware from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityAware';
import { AbilityPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityPluginBinding';
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { TextureRegistry } from '@ohos/flutter_ohos/src/main/ets/view/TextureRegistry';
export declare class VideoPlayerPlugin implements FlutterPlugin, AbilityAware {
    private pluginBinding;
    private videoPlayerApi;
    private flutterState;
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
}
export declare class FlutterState {
    private binaryMessenger;
    private textureRegistry;
    constructor(binaryMessenger: BinaryMessenger, textureRegistry: TextureRegistry);
    getBinaryMessenger(): BinaryMessenger;
    getTextureRegistry(): TextureRegistry;
}
