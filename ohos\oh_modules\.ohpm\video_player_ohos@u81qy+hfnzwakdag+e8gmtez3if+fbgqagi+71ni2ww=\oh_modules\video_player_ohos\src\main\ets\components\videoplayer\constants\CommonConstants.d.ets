import { VideoItem } from '../VideoItem';
import { VideoSpeed } from '../VideoSpeed';
/**
 * Common constants for all features.
 */
export declare class CommonConstants {
    /**
     * Full percent.
     */
    static readonly FULL_PERCENT: string;
    /**
     * Ninety percent.
     */
    static readonly NINETY_PERCENT: string;
    /**
     * Fifty percent.
     */
    static readonly FIFTY_PERCENT: string;
    /**
     * Playback page path.
     */
    static readonly PAGE: string;
    /**
     * Local video ID.
     */
    static readonly TYPE_LOCAL: number;
    /**
     * Network video ID.
     */
    static readonly TYPE_INTERNET: number;
    /**
     * Start playing.
     */
    static readonly STATUS_START: number;
    /**
     * Playing Pause.
     */
    static readonly STATUS_PAUSE: number;
    /**
     * Stop Playing.
     */
    static readonly STATUS_STOP: number;
    /**
     * Width-height ratio.
     */
    static readonly ASPECT_RATIO: number;
    /**
     * One hundred.
     */
    static readonly ONE_HUNDRED: number;
    /**
     * A thousand.
     */
    static readonly A_THOUSAND: number;
    /**
     * Attribute Value is 'yes'
     */
    static readonly YES: string;
    /**
     * Speed set.
     */
    static readonly SPEED_ARRAY: VideoSpeed[];
    /**
     * time system, Hour-minute-second conversion.
     */
    static readonly TIME_UNIT: number;
    /**
     * Initial Time UNIT.
     */
    static readonly INITIAL_TIME_UNIT: string;
    /**
     * Zero padding, 2 bits.
     */
    static readonly PADDING_LENGTH: number;
    /**
     * String zero padding.
     */
    static readonly PADDING_STR: string;
    /**
     * Breath screen status.
     */
    static readonly SCREEN_OFF: string;
    /**
     * Operation status of video player 4.
     */
    static readonly OPERATE_STATE: Array<string>;
}
/**
 * Player component status.
 */
export declare enum AvplayerStatus {
    IDLE = "idle",
    INITIALIZED = "initialized",
    PREPARED = "prepared",
    PLAYING = "playing",
    PAUSED = "paused",
    COMPLETED = "completed",
    STOPPED = "stopped",
    RELEASED = "released",
    ERROR = "error"
}
/**
 * AVPlayer binding event.
 */
export declare enum Events {
    STATE_CHANGE = "stateChange",
    TIME_UPDATE = "timeUpdate",
    BUFFER_UPDATE = "bufferingUpdate",
    ERROR = "error"
}
/**
 * Slider mode.
 */
export declare enum SliderMode {
    MOVING = 1,
    END = 2,
    CLICK = 3
}
/**
 * Video object collection.
 */
export declare const VIDEO_DATA: VideoItem[];
