import 'package:lib_base/config/app_config.dart';
import 'package:lib_base/config/env/env_config.dart';
import 'package:lib_base/config/net/interceptors/http_extra_key.dart';

enum HostType {
  baseHost("https://apiv4"),
  ossHost("https://oss"),
  payHost("https://pay"),
  webHost("https://m"),
  assetHost("https://book"),

  domainhost("http://***************"),
  domainhostOfDev("http://**************:82"),
  domainhostOfTest("http://**************:81"),
  domainhostOfStable("http://**************"),

  logHost("https://log"),
  ;

  final String url;

  const HostType(this.url);

  static HostType getTypeByName(String name) {
    switch (name) {
      case HostName.ossHost:
        return HostType.ossHost;
      case HostName.webHost:
        return HostType.webHost;
      case HostName.payHost:
        return HostType.payHost;
      case HostName.assetHost:
        return HostType.assetHost;
      case HostName.domainhost:
        EnvConfig config = Env.envConfig;
        String env = config.env;
        if (env == EnvName.dev) {
          return HostType.domainhostOfDev;
        } else if (env == EnvName.test) {
          return HostType.domainhostOfTest;
        } else if (env == EnvName.stable) {
          return HostType.domainhostOfStable;
        } else {
          return HostType.domainhost;
        }
      case HostName.loghost:
        return HostType.logHost;
      default:
        return HostType.baseHost;
    }
  }
}

class BaseApi {
  // static const String baseHost = 'https://apiv3';
  // static const String ossHost = 'https://oss';
  // static const String payHost = 'https://pay';
  // static const String webHost = 'https://m';
  // static const String assetHost = 'https://book';

  //address = (-dev开发环境)，(-ceshi测试环境),(-stable预发布),(正式环境,address = "")
  static const String devPre = "-dev";
  static const String testPre = "-ceshi";
  static const String stablePre = "-stable";
  static const String releasePre = "";

  static const String port = ":444";

  static String privateAgreeUrl =
      "/html/kfzx/index.html?envType=EYYB&appId=${AppConfig.YYB_STUDENT_APPID}&type=8";
  static String userAgreeUrl =
      "/html/kfzx/index.html?envType=EYYB&appId=${AppConfig.YYB_STUDENT_APPID}&type=4";

  ///web资源包
  static const String eyyb_app_version = "/v2/eyyb_app/eyyb_app_version.json";

  //账号登录
  static const String login = '/user-api/api/yyb/v1/login/api/login';

  //发送验证码
  static const String sendMsgCode = '/api/yyb/news/v4_1/sendMsgCode';

  //重置密码
  static const String verifyResetPass = '/api/yyb/v1/user/api/verifyResetPass';
  //注册
  static const String register = '/api/yyb/v1/login/api/register';

  //获取用户信息
  static const String getPuserInfo = '/api/yyb/v1/user/api/getPuserInfo';

  //
  static const String getUserWithOtherInfo =
      "/api/yyb/v1/user/api/getUserWithOtherInfo";

  // 签到信息
  static const String getSignInfo = "/user-api/api/yyb/v1/sign/api/getSignInfo";

  // 钱包信息
  static const String findWallet =
      "/pay-api/api/yyb/v1/usermoney/api/findWallet";

  ///查询我的资源
  static const String queryMyResouce =
      "/order-api/api/yyb/v1/userbuy/api/queryMyResouce";

  /**
   * 查询语气词
   * @return
   */
  static const String selectwordtonelist =
      "/api/textbook/v50/manmachinetalkapi/selectwordtonelist";

  ///查询广告
  static const String queryAvailableList =
      "/advertise-api/api/yyb/v1/advertise/info/api/queryAvailableList";

  ///查询区域
  static const String queryAllRegions =
      "/school-api/api/yyb/v1/schoolApi/queryAllRegions";

  ///更新区域
  static const String upuserarea = "/api/user/v50/user/api/upuserarea";

  // 更新用户
  static const String updateUser = "/api/yyb/v1/user/api/updateUser";

  /**
   * 查询管理 创建  加入班级数
   *
   * @return
   */
  static const String queryClazzNum =
      "/user-api/api/yyb/v1/clazzTeacher/queryClazzNum";

  ///查询教材
  static const String querybook =
      "/textbook-api/api/textbook/v50/book/querybook";

  /**
   * 743判断学生用户是否是进入的新会员页面
   * @param userId
   * @return
   */
  static const String checknewvippe = "/api/textbook/v50/hk/checknewvippe";

  /**
   * 学生查询加入的班级列表
   *
   * @return
   */
  static const String getJoinedClazzList =
      "/user-api/api/yyb/v1/clazzStudent/getJoinedClazzList";

  /**
   * 学生保存默认班级
   *
   * @return
   */
  static const String updatePriority =
      "/user-api/api/yyb/v1/clazzStudent/updatePriority";

  /**
   * 班级详情
   *
   * @return
   */
  static const String getClazzInfo =
      "/user-api/api/yyb/v1/clazzStudent/getClazzInfo";

  /**
   * 学生加入班级前根据班级编号查询班级信息
   *
   * @param clazzNo
   * @return
   */
  static const String queryclazzbyclazznonew =
      "/user-api/api/yyb/v1/clazzStudent/queryclazzbyclazznonew";

  static const String checksydata =
      "/sensitivity-api/api/sensitivity/v50/sy/checksydata";

  /**
   * 退出班级
   *
   * @param clazzNo
   * @return
   */
  static const String exitClazz = "/user-api/api/yyb/v1/clazzStudent/exitClazz";

  /**
   * 学生查询班级成员
   *
   * @param clazzId   班级ID
   * @param pageIndex 页数，默认为1
   * @param pageSize  每页显示多少条，默认为10
   * @param orderBy   排序规则，默认加入时间降序 name desc
   * @return
   */
  static const String queryMemberByStudent =
      "/user-api/api/yyb/v1/clazzStudent/queryMemberByStudent";

  /**
   * V7.0.0版本班级管理-添加动态点赞
   */
  static const String dynamicUplike =
      "/datacenter-api/api/datacenter/v50/dynamic/uplike";

  /**
   * 人教点读教材列表
   *
   * @return
   */
  static const String queryBookRelationMap =
      "/order-api/api/yyb/clicklearn/v1/queryBookRelationMap";

  ///查询配置
  static const String getbook = "/api/textbook/v51/book/getbook";

  ///810学生三科首页模块
  static const String onebookdata = "/api/textbook/v51/book/onebookdata";

  ///810学生英语首页对应资源
  static const String listenresourcecolumn =
      "/api/interest/v50/bookread/listenresourcecolumn";

  /**
   * 根据积分规则增加/减少积分
   *
   * @param body
   * @return
   */
  static const String updatePoint = "/api/yyb/v2/point/api/updatePoint";

  static const String queryrecommendList =
      "/advertise-api/api/advertise/v50/recommend/queryrecommendList";

  /**
   * 7.1.0语音评测-跟读(再试一次)
   *
   * @return
   */
  static const String readresultnew =
      "/studyrecord-api/api/oralunit/v70/info/readresultnew";

  /**
   * 去练习试卷
   *
   * @return
   */
  static const String queryappexampaper =
      "/api/question/v50/exampaper/queryappexampaper";

  /**
   * 提交考前突破模块对应测试卷
   *
   * @return
   */
  static const String submitexam =
      "/api/studyrecord/v50/breakthrough/submitexam";

  /**
   * 查询全部答完的练习或测试卷详情数据
   *
   * @return
   */
  static const String queryprogressdata =
      "/api/studyrecord/v50/breakthrough/queryprogressdata";

  /**
   * 查询部分完成测试卷详情
   *
   * @return
   */
  static const String querypartexamprogress =
      "/api/studyrecord/v50/breakthrough/querypartexamprogress";

  /**
   * 查询今日待订正错题，一次固定20条
   *
   * @return
   */
  static const String querytodayerrorqn =
      "/api/questionrecord/v50/errorqn/querytodayerrorqn";

  /**
   * 查询未订正错题
   *
   * @return
   */
  static const String querynorevisionerrorqn =
      "/api/questionrecord/v50/errorqn/querynorevisionerrorqn";

  /**
   * 提交不需要上传资源的错题本中答题数据
   *
   * @return
   */
  static const String submiterrorquestions =
      "/api/questionrecord/v50/errorqn/submiterrorquestions";

  /**
   * 考前突破-查询用户练习档位
   *
   * @return
   */
  static const String querybreakthroughlevel =
      "/api/textbook/v50/breakthrough/querybreakthroughlevel";

  /**
   * 去练习试题列表
   *
   * @return
   */
  static const String queryapplevelquestion =
      "/api/textbook/v50/breakthrough/queryapplevelquestion";

  /**
   * 提交考前突破模块对应练习
   *
   * @return
   */
  static const String submitpractice =
      "/api/studyrecord/v50/breakthrough/submitpractice";

  /**
   * 700根据对应单元id的单词名称查询对应单词数据
   *
   * @return
   */
  static const String findworddata = "/api/textbook/v50/unitword/findworddata";

  /**
   * V8.0.1查询点读券
   *
   * @return
   */
  static const String queryClickLearnTicket =
      "/order-api/api/yyb/clicklearn/v1/queryClickLearnTicket";
  /**
   * 获取家长密码信息
   *
   * @return
   */
  static const String getUserWithOtherInfoForH5 =
      "/user-api/api/yyb/v1/user/api/getUserWithOtherInfoForH5";

  /**
   * 验证家长密码是否正确
   *
   * @return
   */
  static const String verifyManagePassword =
      "/user-api/api/yyb/v1/user/api/verifyManagePassword";

  /**
   * 修改家长密码
   *
   * @return
   */
  static const String changeManagePass =
      "/user-api/api/yyb/v1/user/api/changeManagePass";

  /**
   * 重置家长密码
   *
   * @return
   */
  static const String verifyResetManagePass =
      "/user-api/api/yyb/v1/user/api/verifyResetManagePass";

  /**
   * 兑换人教点读券
   *
   * @return
   */
  static const String redeemClickLearnTicket =
      "/order-api/api/yyb/clicklearn/v1/redeemClickLearnTicket";

  /**
   * 查询单元练习计划是否开启与单元练习计划的最新发布前三天的这个单元
   *
   * @return
   */
  static const String querysemesterdata =
      "/api/homework/v50/semester/querysemesterdata";

  /**
   * 使用头像
   */
  static const String usehead = "/api/user/v50/user/api/usehead";

  // /**
  //    * 保存用户启动记录
  //    * log接口需要绕过https自签名认证
  //    * @return
  //    */
  //   @POST("/behavior-api/api/yyb/v1/startthe/save")
  static const String savestartthe = "/behavior-api/api/yyb/v1/startthe/save";

  // @GET("/appconfig-api/api/yyb/v1/dict/phone/getDictTitle")
  static const String getDictTitle =
      "/appconfig-api/api/yyb/v1/dict/phone/getDictTitle";

  //   /**
  //  * 新增学生修改自定义作业
  //  * @return data
  //  */
  // @POST("/api/homework/v50/student/updateprodata")
  static const String updateprodata = '/api/homework/v50/student/updateprodata';

  //   /**
  //  * 学生提交练习新后台
  //  * @return data
  //  */
  // @POST("/api/homework/v50/student/submithk")
  static const String submithk = '/api/homework/v50/student/submithk';

  /**
     * 修改教材题库对应审核状态
     *
     * @return
     */
  // @POST("/api/textbook/v50/syncquestion/updateappstatus")
  static const String updateappstatus =
      "/api/textbook/v50/syncquestion/updateappstatus";

  /**
     * 更新题数据结果
     *
     * @param body
     * @return
     */
  // @POST("/api/questionrecord/v50/hkrevision/updatetfresults")
  static const String updatetfresults =
      "/api/questionrecord/v50/hkrevision/updatetfresults";

  ///查询模块
  static const String queryAllModules =
      "/homework-api/api/yyb/v1/commonApi/queryAllModules";

  /**
     * 提交统测活动对应测试卷
     *
     * @return
     */
  // @POST("/activity-api/api/activity/v50/unifiedtestsum/submitunifiedtestexam")
  static const String submitunifiedtestexam =
      "/activity-api/api/activity/v50/unifiedtestsum/submitunifiedtestexam";

  /**
     * 查询部分完成详情
     *
     * @return
     */
  // @GET("/activity-api/api/activity/v50/unifiedtestsum/querypartunifiedtestprogress")
  static const String querypartunifiedtestprogress =
      "/activity-api/api/activity/v50/unifiedtestsum/querypartunifiedtestprogress";

  /**
     * 获取题目列表
     * @param activityId 活动id
     * @param ids 0、邀请好友获得参赛次数、1、邀请好友获得积分、2、分享获得参赛次数
     * @param wordIds
     * @return
     */
  // @GET("/activity-api/api/yyb/v1/activity/api/queryQuestions")
  static const String queryQuestions =
      "/activity-api/api/yyb/v1/activity/api/queryQuestions";

  /**
     * 答题
     * @return
     */
  // @POST("/activity-api/api/yyb/v1/activity/api/submitAnsersInfo")
  // Call<JSONObject> submitAnsersInfo(@Body RequestBody body);
  static const String submitAnsersInfo =
      "/activity-api/api/yyb/v1/activity/api/submitAnsersInfo";

  /**
     * 答题中错误的单词提交到措词本中（ui接口）
     * @return
     */
  // @POST("/word-api/api/yyb/v1/qiaoxue/phone/saveErrorWord")
  static const String saveErrorWord =
      "/word-api/api/yyb/v1/qiaoxue/phone/saveErrorWord";

  /**
     * 提交不需要上传资源的公共模块题提交接口
     *
     * @param body
     * @return
     */
  // @POST("/api/questionrecord/v50/errorqn/addpubquestionpro")
  static const String addpubquestionpro =
      "/api/questionrecord/v50/errorqn/addpubquestionpro";

  // 历史最佳成绩接口
  static const String getMyBestResult =
      "/activity-api/api/yyb/v1/activity/api/getMyBestResult";

  // domain
  static const String domain = "/domain";

  /**
     * 600查询部分答完的口语模测详情数据
     *
     * @return
     */
  // @GET("/api/textbook/v50/breakthrough/querypartoralmtestpro")
  static const String querypartoralmtestpro =
      "/api/textbook/v50/breakthrough/querypartoralmtestpro";

  /**
     * 600提交考前口语模测
     *
     * @return
     */
  // @POST("/api/studyrecord/v50/breakthrough/submitoralmtest")
  static const String submitoralmtest =
      "/api/studyrecord/v50/breakthrough/submitoralmtest";

  /**
     * 查询生词本id
     *
     * @return
     */
  // @GET("/api/textbook/v50/independent/findindependenttype")
  static const String findindependenttype =
      "/api/textbook/v50/independent/findindependenttype";

  /**
     * 5.8.0版本二维码-播放音频
     *
     * @param type 类型(examPaperAudio:单元测试卷)
     * @param id   音频Id
     * @return
     */
  // @GET("/api/textbook/v50/scan")
  // Call<JSONObject> queryQRCodeAudio(@Query("type") String type, @Query("id") String id);
  static const String queryQRCodeAudio = "/api/textbook/v50/scan";

  /**
     * 641人机对话查询练习试题
     *
     * @return
     */
  // @Headers({"url_name:newhost"})
  // @GET("/api/textbook/v50/manmachinetalkapi/selectquestionbypractice")
  // Call<JSONObject> selectquestionbypractice(@Query("practiceId") String practiceId);
  static const String selectquestionbypractice =
      "/api/textbook/v50/manmachinetalkapi/selectquestionbypractice";

  /**
     * 641人机对话查询用户练习报告
     *
     * @return
     */
  // @GET("/api/textbook/v50/manmachinetalkapi/selectpracticereport")
  // Call<JSONObject> selectpracticereport(@Query("breakthroughId") String breakthroughId,
  //                                       @Query("unitId") String unitId,
  //                                       @Query("userId") String userId,
  //                                       @Query("practiceId") String practiceId);
  static const String selectpracticereport =
      "/api/textbook/v50/manmachinetalkapi/selectpracticereport";

  /**
     * 641人机对话添加练习题目记录
     *
     * @param body
     * @return
     */
  // @POST("/api/textbook/v50/manmachinetalkapi/submitpracticeinfo")
  static const String submitpracticeinfo =
      "/api/textbook/v50/manmachinetalkapi/submitpracticeinfo";

  /**
     * 7.7.1版本-数学课程营视频学习信息
     *
     * @param gmManageId 主题Id
     * @return
     */
  // @GET("/api/word/v50/train/camp/course/videooptions")
  static const String videooptions =
      "/api/word/v50/train/camp/course/videooptions";

  /**
     * 7.7.1版本-提交数学课程营视频学习记录
     */
  // @POST("/api/studyrecord/v50/train/camp/course/submitwatch")
  // Call<JSONObject> submitMathLearnCourseVideoWatchRecord(@Body RequestBody body);
  static const String submitwatch =
      "/api/studyrecord/v50/train/camp/course/submitwatch";

  /**
     * 语音评测明细日志提交
     *
     * @return
     */
  // @POST("/behavior-api/api/yyb/v1/record/api/addSentenceRecord")
  static const String addSentenceRecord =
      "/behavior-api/api/yyb/v1/record/api/addSentenceRecord";

  /**
     * 签到
     *
     * @param body
     * @return
     */
  // @POST("/user-api/api/yyb/v1/sign/api/sign")
  static const String sign = "/user-api/api/yyb/v1/sign/api/sign";

  /**
     * 单元自检提交
     *
     * @param body
     * @return
     */
  // @POST("/api/studyrecord/v50/820/bookunit/saveuserunitexampapercompletionprog")
  static const String saveuserunitexampapercompletionprog =
      "/api/studyrecord/v50/820/bookunit/saveuserunitexampapercompletionprog";

  /**
     * 课文配音提交
     *
     * @param body
     * @return
     */
  // @POST("/api/studyrecord/v50/820/bookunit/saveuserkwpycompletionprog")
  static const String saveuserkwpycompletionprog =
      "/api/studyrecord/v50/820/bookunit/saveuserkwpycompletionprog";

  
}
