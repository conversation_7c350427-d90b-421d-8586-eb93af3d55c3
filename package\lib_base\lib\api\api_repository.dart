import 'package:lib_base/api/app_api.dart';
import 'package:lib_base/config/app_config.dart';
import 'package:lib_base/config/env/env_config.dart';
import 'package:lib_base/config/net/base_response.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/enums/sms_code_type.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/http/activity_question_response_model/activity_question_response_model.dart';
import 'package:lib_base/model/http/ad_info.dart';
import 'package:lib_base/model/http/app_exam_paper_model.dart';
import 'package:lib_base/model/http/area_info.dart';
import 'package:lib_base/model/http/book_info.dart';
import 'package:lib_base/model/http/book_info_detail.dart';
import 'package:lib_base/model/http/break_through_level_model.dart';
import 'package:lib_base/model/http/class_detail_info_model.dart';
import 'package:lib_base/model/http/class_info_model.dart';
import 'package:lib_base/model/http/class_member_list_response_model.dart';
import 'package:lib_base/model/http/clazz_dic_info_model.dart';
import 'package:lib_base/model/http/click_learn_ticket_model.dart';
import 'package:lib_base/model/http/common_point_response_model.dart';
import 'package:lib_base/model/http/dic_item_model.dart';
import 'package:lib_base/model/http/dict_title_model/dict_title_model.dart';
import 'package:lib_base/model/http/error_word_bean.dart';
import 'package:lib_base/model/http/eyyb_app_version_model.dart';
import 'package:lib_base/model/http/find_word_data_model.dart';
import 'package:lib_base/model/http/learn_course_video_info.dart';
import 'package:lib_base/model/http/listenre_source_column_model.dart';
import 'package:lib_base/model/http/new_class_info_model.dart';
import 'package:lib_base/model/http/part_exam_progress_model.dart';
import 'package:lib_base/model/http/practice_report_model.dart';
import 'package:lib_base/model/http/qc_code_audio_info.dart';
import 'package:lib_base/model/http/query_progress_data_model.dart';
import 'package:lib_base/model/http/recommend_model.dart';
import 'package:lib_base/model/http/rjdd_book_relative_model.dart';
import 'package:lib_base/model/http/semester_data_model/semester_data_model.dart';
import 'package:lib_base/model/http/sign_info.dart';
import 'package:lib_base/model/http/statistics_test_submint_item/statistics_test_submint_item.dart';
import 'package:lib_base/model/http/submit_answer_info_model.dart';
import 'package:lib_base/model/http/user_other_info_model.dart';
import 'package:lib_base/model/http/user_with_other_info_for_h5_model.dart';
import 'package:lib_base/model/http/wallet_info.dart';
import 'package:lib_base/model/question_list.dart';
import 'package:lib_base/model/user_info_model.dart';
import 'package:lib_base/pages/exam/page/rjdh/model/submit_practice_info_bean.dart';
import 'package:lib_base/resource/common_constant.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';
import 'package:lib_base/utils/business/app_util.dart';
import 'package:lib_base/utils/business/http_util.dart';
import 'package:lib_base/utils/platforms.dart';
import 'package:lib_base/utils/rsa_util.dart';

import '../model/http/query_my_resource_model.dart';

class BaseApiRepository {
  static Future<BaseResponse<List<EyybAppVersionModel>>> appVersionList() {
    return BaseAppApi.instance().eyyb_app_version();
  }

  static Future<BaseResponse<String>> accountLogin(
      String loginName, String pwd) async {
    String encodePwd =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, pwd);
    return BaseAppApi.instance().login(
        loginName,
        encodePwd,
        await AppUtil.getDeviceId(),
        HttpUtil.phonePlatform(),
        AppConfig.YYB_STUDENT_APP_SOURCE,
        AppConfig.YYB_STUDENT_APPID);
  }

  static Future<BaseResponse> sendSmsCode(
      String userPhoneNo, SendMsgCodeType codeType) async {
    //商户号 1、E英语宝 2、爱智学习
    String businessId = '1';
    // userTYpe: 1 学生端  2  老师端
    var response = await BaseAppApi.instance()
        .sendMsgCode(userPhoneNo, codeType.code, 1, businessId);
    return response;
  }

  static Future<bool> verifyResetPass(
      String username, String password, String vcode) async {
    String encodePwd =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, password);
    // userTYpe: 1 学生端  2  老师端
    var response = await BaseAppApi.instance().verifyResetPass(
        username, encodePwd, vcode, 1, AppConfig.YYB_STUDENT_APPID);
    return response.isSuccess;
  }

  static Future<BaseResponse> register(
      {required String username,
      required String password,
      required String verifyCode}) async {
    String encodePwd =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, password);
    String deviceId = await AppUtil.getDeviceId();
    String pPlatform = HttpUtil.phonePlatform();
    String appSource = AppConfig.YYB_STUDENT_APP_SOURCE.toString();
    String appId = AppConfig.YYB_STUDENT_APPID.toString();
    // 身份（1=学生、2=教师、3=家长）
    String userType = AppConfig.USER_TYPE_STUDENT.toString();
    return BaseAppApi.instance().register(username, encodePwd, deviceId,
        pPlatform, appSource, appId, verifyCode, userType);
  }

  static Future<BaseResponse<PUserInfoEntity>> getUserInfo() {
    return BaseAppApi.instance().getPuserInfo();
  }

  static Future<BaseResponse<SignInfo>> getSignInfo(
      {required String userId, required String? years}) {
    return BaseAppApi.instance().getSignInfo(userId, years);
  }

  static Future<BaseResponse<WalletInfo>> findWallet({
    required String userId,
  }) {
    return BaseAppApi.instance().findWallet(userId);
  }

  static Future<BaseResponse<QueryMyResourceModel>> queryMyResouce(
      {List<String>? types,
      List<String>? resourceIds,
      List<String>? moduleIds,
      String expired = "0",
      required String puserId,
      int pageSize = 100,
      int pageIndex = 1}) {
    types ??= ["8"];
    resourceIds ??= [];
    moduleIds ??= [];
    return BaseAppApi.instance().queryMyResouce(
        types, resourceIds, moduleIds, expired, puserId, pageSize, pageIndex);
  }

  static Future<BaseResponse<List<DicItemModel>>> selectwordtonelist() async {
    var result = await BaseAppApi.instance().selectwordtonelist();
    if (result.isSuccess && result.isDataNotNull) {
      StorageManager.sharedPreferences
          .setJsonList(SharedPreferencesKeys.wordtonelist, result.dataNotNull);
    }
    return result;
  }

  static Future<BaseResponse<List<AdInfo>>> queryAvailableList(
      {required String positionId}) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    return BaseAppApi.instance().queryAvailableList(positionId,
        userInfoModel.obj?.memberCode ?? "", AppConfig.buildNumber.toString());
  }

  static Future<BaseResponse<List<AreaInfo>>> queryAllRegions() {
    return BaseAppApi.instance().queryAllRegions();
  }

  static Future<BaseResponse> upuserarea({String? areaCode}) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    return BaseAppApi.instance()
        .upuserarea(areaCode, userInfoModel.obj?.uid ?? "");
  }

  static Future<BaseResponse<UserOtherInfoModel>> getUserWithOtherInfo() {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    return BaseAppApi.instance()
        .getUserWithOtherInfo(userInfoModel.obj?.uid ?? "");
  }

  static Future<BaseResponse> updateUser(
      {String? schoolId,
      String? gradeId,
      String? gradeName,
      String? enVersion,
      String? areaCode,
      String? segment,
      String? nickName,
      String? sex,
      String? mathVersion}) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    String userId = userInfoModel.obj?.uid ?? "";
    return BaseAppApi.instance().updateUser(userId, schoolId, gradeId,
        gradeName, enVersion, areaCode, segment, nickName, sex, mathVersion);
  }

  static Future<BaseResponse<List<ClazzDicInfoModel>>> queryClazzNum({
    required String teacherId,
  }) {
    return BaseAppApi.instance().queryClazzNum(teacherId);
  }

  ///version 不传表示查询所有书本(wyxbz 外研社 xsb2 湘少版 xlb 湘鲁版 slb 陕旅版 pep 人教版 )
  ///grade   年级，通用/数据字典接口可以查询到年级列表，不传表示查询所有年级的书本
  static Future<BaseResponse<List<BookInfo>>> queryBook(
      {String? version,
      String? grade,
      String? objective,
      required String subject}) {
    return BaseAppApi.instance().querybook(version, grade, objective, subject);
  }

  static Future<BaseResponse> checknewvippe(
      {required String userId,
      required String areaCode,
      required String schoolId}) async {
    return BaseAppApi.instance()
        .checknewvippe(userId, areaCode, schoolId)
        .then((value) {
      if (value.isSuccess) {
        String newVipStatus = "0";
        if (value.isDataNotNull) {
          newVipStatus = value.dataNotNull.toString();
        }
        StorageManager.sharedPreferences
            .setString(SharedPreferencesKeys.newVipStatus, newVipStatus);
      }
      return value;
    });
  }

  static Future<BaseResponse<List<ClassInfoModel>>> getJoinedClazzList(
      {required String userId}) async {
    return BaseAppApi.instance().getJoinedClazzList(userId);
  }

  static Future<BaseResponse> updatePriority({required String classId}) async {
    return BaseAppApi.instance().updatePriority(classId);
  }

  static Future<BaseResponse<ClassDetailInfoModel>> getClazzInfo(
      {required String classId}) async {
    return BaseAppApi.instance().getClazzInfo(classId);
  }

  static Future<BaseResponse<NewClassInfoModel>> queryclazzbyclazznonew(
      {required String classNo}) async {
    return BaseAppApi.instance().queryclazzbyclazznonew(classNo);
  }

  static Future<BaseResponse> checksydata(
      {required List<String> contentList}) async {
    return BaseAppApi.instance().checksydata(contentList, "");
  }

  static Future<BaseResponse> exitClazz(
      {required String clazzId, required String studentId}) async {
    return BaseAppApi.instance().exitClazz(clazzId, studentId);
  }

  /**
    * * @param clazzId       班级Id
    * @param orderBy       排序方式(默认按照加入时间排序;realName:根据姓名排序)
    */
  static Future<BaseResponse<ClassMemberListResponseModel>>
      queryMemberByStudent(
          {required String clazzId,
          required String userId,
          required int pageIndex,
          required int pageSize,
          String? orderBy}) {
    return BaseAppApi.instance()
        .queryMemberByStudent(clazzId, userId, pageIndex, pageSize, orderBy);
  }

  static Future<BaseResponse> dynamicUplike({
    required String dynamicId,
    required String userId,
  }) {
    return BaseAppApi.instance().dynamicUplike(userId, dynamicId);
  }

  static Future<BaseResponse<List<RjddBookRelativeModel>>>
      queryBookRelationMap() {
    return BaseAppApi.instance().queryBookRelationMap();
  }

  static Future<BaseResponse<BookInfoDetail>> getbook(
      {required String bookId, required String userId}) {
    return BaseAppApi.instance().getbook(
        bookId,
        AppConfig.buildNumber.toString(),
        AppConfig.YYB_STUDENT_APPID.toString(),
        userId);
  }

  static Future<BaseResponse<BookInfoDetail>> onebookdata(
      {required String bookId, required String userId}) {
    return BaseAppApi.instance().onebookdata(
        bookId,
        AppConfig.buildNumber.toString(),
        AppConfig.YYB_STUDENT_APPID.toString(),
        userId);
  }

  static Future<BaseResponse<List<ListenreSourceColumnModel>>>
      listenresourcecolumn({
    required String grade,
  }) {
    return BaseAppApi.instance().listenresourcecolumn(grade);
  }

  // ruleCode
  // moduleId
  // detailId
  static Future<BaseResponse<String>> updatePoint(
      {required String ruleCode,
      required String moduleId,
      required String detailId}) {
    return BaseAppApi.instance().updatePoint(ruleCode, moduleId, detailId);
  }

  static Future<BaseResponse<List<RecommendModel>>> queryrecommendList(
      {required String subject,
      String recommendPoint = "xxzx",
      String? bookId,
      String? grade}) async {
    return BaseAppApi.instance().queryrecommendList(
        subject, recommendPoint, bookId, grade, AppConfig.buildNumber);
  }

  static Future<BaseResponse> readresultnew(
      {required String oralunitId, required String answers}) async {
    return BaseAppApi.instance().readresultnew(oralunitId, answers);
  }

  static Future<BaseResponse<AppExamPaperModel>> queryappexampaper({
    required String id,
  }) async {
    return BaseAppApi.instance().queryappexampaper(id);
  }

  static Future<BaseResponse> submitexam(
      {required String id,
      required String moduleId,
      required String? examPaperId,
      required String? examName,
      required String? examTypeName,
      required String? jointExamineName,
      required String tf,
      required int trueCount,
      required int falseCount,
      required int halfCount,
      required num tfRate,
      required List<String> results,
      required List<String> rightResults,
      required int costSecond,
      required String everyCostSecond,
      required List<String> questionIds,
      List<String>? images,
      List<String>? vedios,
      List<String>? audios,
      required String? dedicatedRegion,
      required num? totalScore,
      required num? score,
      required num completionRate,
      required String examCard}) async {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    String? provinceCity = userInfoModel.obj?.areaCode;
    String grade = userInfoModel.gradeId;
    String userId = userInfoModel.userId;
    String subject = "english";
    return BaseAppApi.instance().submitexam(
        provinceCity,
        grade,
        userId,
        moduleId,
        examPaperId,
        examName,
        examTypeName,
        jointExamineName,
        tf,
        trueCount,
        falseCount,
        halfCount,
        tfRate,
        results,
        rightResults,
        costSecond,
        everyCostSecond,
        questionIds,
        images,
        vedios,
        audios,
        dedicatedRegion,
        totalScore,
        score,
        completionRate,
        examCard,
        id,
        subject);
  }

  static Future<BaseResponse> saveuserunitexampapercompletionprog({
    required String bookId,
    required String bookUnitId,
    String? paramId,
    required String subId,
    required String resourceId,
    required String userId,
    required int studyTime,
    required int qusNum,
    required String remark,
  }) {
    return BaseAppApi.instance().saveuserunitexampapercompletionprog(
      bookId,
      bookUnitId,
      paramId ?? "",
      subId,
      resourceId,
      userId,
      studyTime,
      qusNum,
      remark,
    );
  }

  static Future<BaseResponse> saveuserkwpycompletionprog({
    required String bookId,
    String? paramId,
    required String subId,
    required String userId,
    required int studyTime,
    required String remark,
  }) {
    return BaseAppApi.instance().saveuserkwpycompletionprog(
      bookId,
      paramId ?? "",
      subId,
      userId,
      studyTime,
      remark,
    );
  }

  static Future<BaseResponse<QueryProgressDataModel>> queryprogressdata(
      {String? id, String? type, String subject = "english"}) {
    return BaseAppApi.instance().queryprogressdata(id, type, subject);
  }

  static Future<BaseResponse<PartExamProgressModel>> querypartexamprogress(
      {String? id, String subject = "english"}) {
    return BaseAppApi.instance().querypartexamprogress(id, subject);
  }

  static Future<BaseResponse<List<QuestionList>>> querytodayerrorqn(
      {String? userId, String subject = "english"}) {
    return BaseAppApi.instance().querytodayerrorqn(userId, subject);
  }

  static Future<BaseResponse<List<QuestionList>>> querynorevisionerrorqn(
      {String? userId,
      String subject = "english",
      String? type,
      String? bookId,
      String? unitId,
      String? qyType,
      String? labelId}) {
    return BaseAppApi.instance().querynorevisionerrorqn(
        userId, subject, type, bookId, unitId, qyType, labelId);
  }

  static Future<BaseResponse> submiterrorquestions(
      {String? userId,
      String subject = "english",
      String? provinceCity,
      String? grade,
      String? tf,
      String? everyCostSecond,
      required List<String> results,
      required List<String> questionIds,
      required List<String> resourceIds}) {
    return BaseAppApi.instance().submiterrorquestions(
        provinceCity,
        grade,
        userId,
        subject,
        results,
        tf,
        questionIds,
        everyCostSecond,
        resourceIds);
  }

  static Future<BaseResponse<List<BreakThroughLevelModel>>>
      querybreakthroughlevel({String? userId, String? id, String? moduleId}) {
    return BaseAppApi.instance().querybreakthroughlevel(id, userId, moduleId);
  }

  static Future<BaseResponse<List<QuestionList>>> queryapplevelquestion(
      {String? userId, String? id, String? questionNum}) {
    return BaseAppApi.instance().queryapplevelquestion(id, userId, questionNum);
  }

  static Future<BaseResponse> submitpractice(
      {required String userId,
      String? provinceCity,
      String? grade,
      required String moduleId,
      String? breakthroughUnitId,
      String? breakthroughLevelId,
      String? exerciseUnitName,
      String? gearName,
      String? levelName,
      int? trueCount,
      int? falseCount,
      int? halfCount,
      num? tfRate,
      required List<String> results,
      required List<String> rightResults,
      int? costSecond,
      String? everyCostSecond,
      required List<String> questionIds,
      List<String>? images,
      List<String>? vedios,
      List<String>? audios,
      String? tf,
      String subject = "english"}) {
    return BaseAppApi.instance().submitpractice(
        provinceCity,
        grade,
        userId,
        moduleId,
        breakthroughUnitId,
        breakthroughLevelId,
        exerciseUnitName,
        gearName,
        levelName,
        trueCount,
        falseCount,
        halfCount,
        tfRate,
        results,
        rightResults,
        costSecond,
        everyCostSecond,
        questionIds,
        images,
        vedios,
        audios,
        tf,
        subject);
  }

  static Future<BaseResponse<FindWordDataModel>> findworddata({
    required String? wordName,
    required String? unitId,
  }) {
    return BaseAppApi.instance().findworddata(
      wordName,
      unitId,
    );
  }

  static Future<BaseResponse<List<ClickLearnTicketModel>>>
      queryClickLearnTicket({
    required String? userId,
    required String? bookId,
    required String? useFlag,
    required String? overdueFlag,
  }) {
    return BaseAppApi.instance().queryClickLearnTicket(
      userId,
      bookId,
      useFlag,
      overdueFlag,
    );
  }

  static Future<BaseResponse<UserWithOtherInfoForH5Model>>
      getUserWithOtherInfoForH5({
    required String? userId,
  }) {
    return BaseAppApi.instance().getUserWithOtherInfoForH5(
      userId,
    );
  }

  static Future<BaseResponse> verifyManagePassword({
    required String? userId,
    required String password,
  }) async {
    password =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, password);
    return BaseAppApi.instance().verifyManagePassword(
      userId,
      password,
      null,
    );
  }

  static Future<BaseResponse> changeManagePass({
    required String? userId,
    required String password,
  }) async {
    String encodePwd =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, password);
    return BaseAppApi.instance().changeManagePass(
      userId,
      encodePwd,
      null,
    );
  }

  static Future<BaseResponse> verifyResetManagePass({
    required String? userId,
    required String password,
    required String phone,
    required String verifyCode,
  }) async {
    String encodePwd =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, password);
    return BaseAppApi.instance()
        .verifyResetManagePass(userId, encodePwd, phone, verifyCode);
  }

  static Future<BaseResponse> redeemClickLearnTicket({
    required String? userId,
    required String? bookId,
    required String? id,
    required String? phonePlatform,
  }) async {
    return BaseAppApi.instance()
        .redeemClickLearnTicket(userId, bookId, id, phonePlatform);
  }

  static Future<BaseResponse<SemesterDataModel>> querysemesterdata({
    required String userId,
    String? subject = CommonConstant.BZZY_ENGLISH,
  }) async {
    return BaseAppApi.instance().querysemesterdata(userId, subject);
  }

  static Future<BaseResponse> usehead(
      {required String headId, String? glory}) async {
    String appId = AppConfig.YYB_STUDENT_APPID.toString();
    if (glory != "1") {
      glory = null;
    }
    return BaseAppApi.instance().usehead(appId, headId, glory);
  }

  static Future<BaseResponse> savestartthe() async {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    String userId = userInfoModel.userId;
    if (userId.isEmpty) {
      Logger.error("上报信息出错， userId为空");
    }
    String appVersion = AppConfig.buildNumber.toString();
    String? phoneName = AppConfig.brand;
    String? phoneModel = AppConfig.model;
    String? platformName = PlatForms.getPlatform();
    String? platformVersion = AppConfig.version;
    String? osName = AppConfig.version;
    String? deviceId = "-";
    String? userType = userInfoModel.obj?.type?.toString();
    String? appId = AppConfig.YYB_STUDENT_APPID;
    String? address = userInfoModel.obj?.address;
    String? source = "-";
    return BaseAppApi.instance().savestartthe(
      userId,
      appVersion,
      phoneName,
      phoneModel,
      platformName,
      platformVersion,
      osName,
      deviceId,
      userType,
      appId,
      address,
      source,
    );
  }

  static Future<BaseResponse<List<DictTitleModel>>> getDictTitle(
      String type) async {
    return BaseAppApi.instance().getDictTitle(type);
  }

  static Future<BaseResponse> updateprodata({
    required String? homeworkId,
    required List<String>? images,
    required List<String>? vedios,
    required List<String>? audios,
    required List<String>? results,
    required String? moduleId,
    required String? clazzId,
    required String? id,
  }) {
    return BaseAppApi.instance().updateprodata(
      homeworkId,
      images,
      vedios,
      audios,
      results,
      moduleId,
      clazzId,
      id,
    );
  }

  static Future<BaseResponse> submithk({
    required String? provinceCity,
    required String? grade,
    required String? userId,
    required String? type,
    required String? detailId,
    required String? homeworkId,
    required String? tf,
    required String? everyCostSecond,
    required List<String>? images,
    required String? costSecond,
    required List<String>? vedios,
    required List<String>? audios,
    required List<String>? results,
    required List<String>? rightResults,
    required String? moduleId,
    required String? tfRate,
    required String? receiveVal,
    required String? academicYear,
    required String? fascicule,
    required List<String>? noQns,
    required String? clazzId,
  }) {
    return BaseAppApi.instance().submithk(
      provinceCity,
      grade,
      userId,
      type,
      detailId,
      homeworkId,
      tf,
      everyCostSecond,
      images,
      costSecond,
      vedios,
      audios,
      results,
      rightResults,
      moduleId,
      tfRate,
      receiveVal,
      academicYear,
      fascicule,
      noQns,
      clazzId,
    );
  }

  static Future<BaseResponse> updateappstatus({
    required String? auditStatus,
    required String? reason,
    required String? id,
    required String? userId,
  }) {
    return BaseAppApi.instance().updateappstatus(
      auditStatus,
      reason,
      id,
      userId,
    );
  }

  static Future<BaseResponse> updatetfresults({
    required String? dateName,
    required List<String>? ids,
    required List<String>? tfResults,
    required String? clazzId,
    required String? userId,
    required String? homeworkId,
    required String? homeworkType,
    required List<String>? questionIds,
  }) {
    return BaseAppApi.instance().updatetfresults(
      dateName,
      ids,
      tfResults,
      clazzId,
      userId,
      homeworkId,
      homeworkType,
      questionIds,
    );
  }

  static Future<BaseResponse> queryAllModules(String userId) {
    return BaseAppApi.instance().queryAllModules(userId);
  }

  static Future<BaseResponse> submitunifiedtestexam({
    required String? provinceCity,
    required String? grade,
    required String? id,
    required String? examName,
    required String? examPaperId,
    required List<String>? results,
    required String? userId,
    required int? falseCount,
    required int? trueCount,
    required int? costSecond,
    required String? everyCostSecond,
    required List<String>? questionIds,
    required List<String>? questionScore,
    required List<String>? answerScore,
    required int? tfRate,
    required List<String>? rightResults,
    required String? resourcesId,
    required String? clazzId,
    required int? halfCount,
    required String? seriesName,
    required String? seriesId,
    required String? tf,
    required String? areaCode,
    required String? schoolId,
    required String? examCard,
    required int? totalScore,
    required double? score,
    required int? completionRate,
    required int? examDuration,
  }) {
    return BaseAppApi.instance().submitunifiedtestexam(
      provinceCity,
      grade,
      id,
      examName,
      examPaperId,
      results,
      userId,
      falseCount,
      trueCount,
      costSecond,
      everyCostSecond,
      questionIds,
      questionScore,
      answerScore,
      tfRate,
      rightResults,
      resourcesId,
      clazzId,
      halfCount,
      seriesName,
      seriesId,
      tf,
      areaCode,
      schoolId,
      examCard,
      totalScore,
      score,
      completionRate,
      examDuration,
    );
  }

  static Future<BaseResponse<StatisticsTestSubmintItem>>
      querypartunifiedtestprogress({
    required String? id,
  }) {
    return BaseAppApi.instance().querypartunifiedtestprogress(id);
  }

  static Future<BaseResponse<ActivityQuestionResponseModel>> queryQuestions({
    required String? activityId,
    required String? ids,
    required String? wordIds,
    required String? versionId,
    required String? userId,
  }) {
    return BaseAppApi.instance().queryQuestions(
      activityId,
      ids,
      wordIds,
      versionId,
      userId,
    );
  }

  static Future<BaseResponse<SubmitAnswerInfoModel>> submitAnsersInfo({
    required String? activityId,
    required String? answers,
    required String? totalQuesCount,
    required String? successQuesCount,
    required String? rightRate,
    required String? spendSeconds,
    required String? startTime,
    required String? endTime,
    required String? faildQuesCount,
  }) {
    return BaseAppApi.instance().submitAnsersInfo(
      activityId,
      answers,
      totalQuesCount,
      successQuesCount,
      rightRate,
      spendSeconds,
      startTime,
      endTime,
      faildQuesCount,
    );
  }

  static Future<BaseResponse> saveErrorWord({
    required List<ErrorWordBean>? errorWords,
  }) {
    return BaseAppApi.instance().saveErrorWord(errorWords, "");
  }

  static Future<BaseResponse> addpubquestionpro({
    required String? provinceCity,
    required String? grade,
    required String? userId,
    required String? subject,
    required List<String>? results,
    required String? tf,
    required List<String>? questionIds,
    required String? everyCostSecond,
    required String? moduleId,
    required String? sourceType,
    required String? specificType,
  }) {
    return BaseAppApi.instance().addpubquestionpro(
      provinceCity,
      grade,
      userId,
      subject,
      results,
      tf,
      questionIds,
      everyCostSecond,
      moduleId,
      sourceType,
      specificType,
    );
  }

  static Future<BaseResponse> getMyBestResult(
      {required String userId, required String activeId}) {
    return BaseAppApi.instance().getMyBestResult(userId, activeId);
  }

  static Future<String> domain() {
    return BaseAppApi.instance().domain();
  }

  static Future<BaseResponse<StatisticsTestSubmintItem>> querypartoralmtestpro({
    required String? id,
  }) {
    return BaseAppApi.instance().querypartoralmtestpro(id, "english");
  }

  static Future<BaseResponse> submitoralmtest({
    required String? userId,
    required String? moduleId,
    required String? examPaperId,
    required String? examName,
    required String? examTypeName,
    required String? jointExamineName,
    required String? tf,
    required int? falseCount,
    required int? trueCount,
    required int? halfCount,
    required num? tfRate,
    required List<String>? results,
    required List<String>? rightResults,
    required int? costSecond,
    required String? everyCostSecond,
    required List<String>? questionIds,
    required String? dedicatedRegion,
    required num? totalScore,
    required num? score,
    required num? completionRate,
    required String? examCard,
    required String? id,
    required String? subject,
    required List<String>? questionScore,
    required int? examDuration,
    required List<String>? answerScore,
  }) {
    return BaseAppApi.instance().submitoralmtest(
      userId,
      moduleId,
      examPaperId,
      examName,
      examTypeName,
      jointExamineName,
      tf,
      falseCount,
      trueCount,
      halfCount,
      tfRate,
      results,
      rightResults,
      costSecond,
      everyCostSecond,
      questionIds,
      dedicatedRegion,
      totalScore,
      score,
      completionRate,
      examCard,
      id,
      subject,
      questionScore,
      examDuration,
      answerScore,
    );
  }

//  Future<BaseResponse<String>> findindependenttype(
//       @Query("type") String? type,
//     );
  static Future<BaseResponse<String>> findindependenttype({
    required String? type,
  }) {
    return BaseAppApi.instance().findindependenttype(type);
  }

  static Future<BaseResponse<QcCodeAudioInfo>> queryQRCodeAudio({
    required String? type,
    required String? id,
  }) {
    return BaseAppApi.instance().queryQRCodeAudio(type, id);
  }

  static Future<BaseResponse<List<QuestionList>>> selectquestionbypractice({
    required String? practiceId,
  }) {
    return BaseAppApi.instance().selectquestionbypractice(practiceId);
  }

  static Future<BaseResponse<PracticeReportModel>> selectpracticereport({
    required String? breakthroughId,
    required String? unitId,
    required String? userId,
    required String? practiceId,
  }) {
    return BaseAppApi.instance()
        .selectpracticereport(breakthroughId, unitId, userId, practiceId);
  }

  static Future<BaseResponse<ResultInfoBean>> submitpracticeinfo({
    required String? breakthroughId,
    required String? breakthroughExerciseUnitId,
    required String? practiceId,
    required List<SubmitPracticeInfoBean>? recordList,
    required num? completeRate,
  }) {
    return BaseAppApi.instance().submitpracticeinfo(breakthroughId,
        breakthroughExerciseUnitId, practiceId, recordList, completeRate);
  }

  static Future<BaseResponse<LearnCourseVideoInfo>> videooptions({
    required String? gmManageId,
  }) {
    return BaseAppApi.instance().videooptions(gmManageId);
  }

  static Future<BaseResponse<CommonPointResponseModel>> submitwatch({
    required String? trainingCampId,
    required String? tcManageId,
    required String? manageId,
    required String? userId,
    required int? watchSpeed,
    required int? watchTime,
  }) {
    return BaseAppApi.instance().submitwatch(
        trainingCampId, tcManageId, manageId, userId, watchSpeed, watchTime);
  }

  static Future<BaseResponse> addSentenceRecord({
    required String? contentId,
    required String? content,
    required String? type,
    required String? bookId,
    required String? remarks,
    required String? referenceId,
    required String? requestId,
  }) {
    return BaseAppApi.instance().addSentenceRecord(
        contentId, content, type, bookId, remarks, referenceId, requestId);
  }

  static Future<BaseResponse<int>> sign() {
    return BaseAppApi.instance().sign(UserInfoModel.loadDefault().userId);
  }

  /**
   * 根据一级单元id查询视频资源
   */
  static Future<BaseResponse<Map<String, dynamic>>> selectvideoresourcelist({
    required String bookId,
    required String oneUnitId,
    required String userId,
  }) {
    return BaseAppApi.instance().selectvideoresourcelist(bookId, oneUnitId, userId);
  }
}
