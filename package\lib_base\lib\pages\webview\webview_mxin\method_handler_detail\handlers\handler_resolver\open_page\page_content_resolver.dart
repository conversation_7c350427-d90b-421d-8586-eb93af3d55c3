import 'dart:convert';

import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/api/api_repository.dart';
import 'package:lib_base/config/app_config.dart';
import 'package:lib_base/config/route_name.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/exam_info.dart';
import 'package:lib_base/model/feedback_page_param.dart';
import 'package:lib_base/model/http/activity_question_response_model/activity_question_response_model.dart';
import 'package:lib_base/model/http/app_exam_paper_model.dart';
import 'package:lib_base/model/http/book_info_detail.dart';
import 'package:lib_base/model/http/learn_course_video_info.dart';
import 'package:lib_base/model/http/part_exam_progress_model.dart';
import 'package:lib_base/model/http/practice_report_model.dart';
import 'package:lib_base/model/http/query_progress_data_model.dart';
import 'package:lib_base/model/http/statistics_test_submint_item/statistics_test_submint_item.dart';
import 'package:lib_base/model/interactive_reading_page_param.dart';
import 'package:lib_base/model/js_bridge_data_bean.dart';
import 'package:lib_base/model/listen_course_page_param.dart';
import 'package:lib_base/model/pan_listen_audio_res_info.dart';
import 'package:lib_base/model/param/course_animation_page_param.dart';
import 'package:lib_base/model/question_list.dart';
import 'package:lib_base/model/speech_evaluating_page_param.dart';
import 'package:lib_base/model/user_info_model.dart';
import 'package:lib_base/pages/exam/controller/exam_model.dart';
import 'package:lib_base/pages/exam/page/exercise/page/exercise_exam/exercise_exam_page.dart';
import 'package:lib_base/pages/exam/page/exercise/page/report/controller/exercise_report_page_controller.dart';
import 'package:lib_base/pages/exam/page/exercise/page/report/exercise_report_page.dart';
import 'package:lib_base/pages/exam/page/not_volume_structure/not_volume_structure_page.dart';
import 'package:lib_base/pages/exam/page/rjdh/model/submit_practice_info_bean.dart';
import 'package:lib_base/pages/exam/page/rjdh/page/rjdh_report_page/rjdh_report_page.dart';
import 'package:lib_base/pages/exam/page/rjdh/rjdh_page.dart';
import 'package:lib_base/pages/exam/page/statistics_test/statistics_test_page.dart';
import 'package:lib_base/pages/exam/page/volume_structure/page/report/unit_evaluation_report/unit_evaluation_report_page.dart';
import 'package:lib_base/pages/exam/page/volume_structure/volume_structure_page.dart';
import 'package:lib_base/pages/exam/page/word_match/word_match_page.dart';
import 'package:lib_base/pages/exam/page/wrong_exercise/wrong_exercise_page.dart';
import 'package:lib_base/pages/video_page/learn_course_video_play/learn_course_video_play_page.dart';

import 'package:lib_base/pages/webview/webview_mxin/method_handler_detail/method_handler_detail.dart';
import 'package:lib_base/providers/books/english/english_book_info_provider.dart';
import 'package:lib_base/providers/rjdd_provider.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';
import 'package:lib_base/src/utils/asset_utils.dart';
import 'package:lib_base/utils/business/book_util.dart';
import 'package:lib_base/utils/business/http_util.dart';
import 'package:lib_base/utils/business/stu_util.dart';
import 'package:lib_base/utils/permission_request_util.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/utils/wx_util.dart';
import 'package:lib_base/widgets/animation/image_switch_animation.dart';
import 'package:lib_base/widgets/dialog/simple_alert_dialog.dart';

// import 'package:ai_intelligent_flutter/model/user_info_model.dart'
//     as aiUserInfoModel;
//     import 'package:ai_intelligent_flutter/config/route/route_name.dart'
//     as aiIntelligentRouter;
//     import 'package:ai_intelligent_flutter/resource/plugin/app_config.dart';

part 'to_exam.dart';

class PageContentResolver {
  handlePageContent(InAppWebViewController webViewController,
      JsBridgeDataBean data, String content, WidgetRef ref) {
    content = content.substring("page:".length);
    List<String> strs = content.split(",");
    if ("mixture" == strs[0]) {
      mixture(webViewController, data, content);
    } else if ("walkmanPlayer" == strs[0]) {
      walkmanPlayer(webViewController, data, content);
    } else if ("goToAnswer" == strs[0]) {
      //考试
      goToAnswer(webViewController, data, content);
    } else if ("specialReport" == strs[0]) {
      //报告
      specialReport(webViewController, data, content);
    } else if ("wrongExamPage" == strs[0]) {
      //错题本
      toInteractWebviewModulePage(moduleId: "44", showBack: true, othermap: {
        "userId": ref.read(userInfoNotifierProvider).userId,
        "subject": "english"
      });
    } else if ("goWrongExercise" == strs[0]) {
      //待订正错题做题-英语
      goWrongExercise(webViewController, data, content, ref);
    } else if ("goToExercise" == strs[0]) {
      goToExercise(webViewController, data, content, ref);
    } else if ("englishclickread" == strs[0]) {
      //英语课本点读
      goEnglishClickRead(webViewController, data, content, ref);
    } else if ('tcStuToAnswer' == strs[0]) {
      //统测活动
      toStuToAnswer(webViewController, data, content, ref);
    } else if ('wordmatch' == strs[0]) {
      // 巧记单词
      toWordMatch(webViewController, data, content, ref);
    } else if ('wordcourseguide' == strs[0]) {
      // 全脑记词
      toPage(RouteName.brainWordMemory, extra: {
        'courseId': data.courseId,
        'freeCount': data.freeCount ?? 1,
        'periodId': data.periodId
      });
    } else if ('mcStuToAnswer' == strs[0]) {
      // 口语训练
      mcStuToAnswer(webViewController, data, content, ref);
    }
    // else if ('exerciseCorrect' == strs[0]) {
    //   //作业批改
    //   toPage(aiIntelligentRouter.RouteName.exerciseCorrectPage, );
    // } else if ('idiomChain' == strs[0]) {
    //   //成语接龙
    //   toPage(aiIntelligentRouter.RouteName.idiomChain, );
    // } else if ('englishCorrectEssay' == strs[0]) {
    //   //英语作文批改
    //   toPage(aiIntelligentRouter.RouteName.exerciseCorrectEssayStreamPage, );
    // } else if ('chineseCorrectEssay' == strs[0]) {
    //   //语文作文批改
    //   toPage(aiIntelligentRouter.RouteName.exerciseCorrectEssayStreamPage, );
    // }
    else if ('tbydReadBook' == strs[0]) {
      //todo 去美文阅读
      showToast("敬请期待");
      toPage(RouteName.intensiveListening);
    } else if ('commFeedback' == strs[0]) {
      //反馈
      toPage(RouteName.feedback,
          extra: FeedbackPageParam(
              moduleName: data.moduleName,
              type: data.type?.toString(),
              moduleId: data.moduleConfigId,
              bookInfoItem: data.bookInfoItem != null
                  ? SimpleBookInfoModel.fromJson(
                      jsonDecode(jsonEncode(data.bookInfoItem!)),
                    )
                  : null));
    } else if ('personInfoPage' == strs[0]) {
      //  个人信息;
      toPage(RouteName.userInfo);
    } else if ('openMyClazz' == strs[0]) {
      //  我的班级
      toPage(RouteName.myClass);
    } else if ('chineseclickread' == strs[0]) {
      //  课文点读
      showToast("敬请期待");
      toPage(RouteName.textReadingPoints);
    } else if ('openPoetryPage' == strs[0]) {
      //  诗词大会
      showToast("敬请期待");
    } else if ('tbydReadIndex' == strs[0]) {
      //todo 同步阅读
      showToast("敬请期待");
    } else if ('ztydReadIndex' == strs[0]) {
      //todo 主题阅读
      showToast("敬请期待");
    } else if ('openTextBookPage' == strs[0]) {
      //  听课文
      BookInfoDetail? bookInfo =
          ref.read(englishBookInfoProviderProvider.notifier).bookInfoDetail;
      ListenCoursePageParam param = ListenCoursePageParam(
        bookId: bookInfo?.id ?? "4241caaa912a44d09fd2831d91b328ea",
        modelId: "32",
        bookInfoDetail: bookInfo,
      );
      toPage(
        RouteName.listenCourse,
        extra: param,
      );
    } else if ('openTextVideoPage' == strs[0]) {
      //  课文动画
      BookInfoDetail? bookInfo =
          ref.read(englishBookInfoProviderProvider.notifier).bookInfoDetail;
      CourseAnimationPageParam param = CourseAnimationPageParam(
        bookId: bookInfo?.id ?? "4241caaa912a44d09fd2831d91b328ea",
        bookName: bookInfo?.name ?? "湘少版三年级上册",
        modelId: "13",
        modelName: "",
        bookInfoDetail: bookInfo,
      );
      toPage(
        RouteName.courseAnimation,
        extra: param,
      );
    } else if ('englishclickread' == strs[0]) {
      //  课文点读
      //判断书籍
      _englishclickread(ref);
    } else if ('newEasyWord' == strs[0]) {
      //  巧记单词
      BookInfoDetail? detail =
          ref.read(englishBookInfoProviderProvider.notifier).bookInfoDetail;
      toPage(RouteName.wordRemember, extra: detail, queryParameters: {
        "freeCount": "1",
      });
    } else if ('openWordListenPage' == strs[0]) {
      //  单词听写
      _openWordListenPage(ref);
    } else if ('speechlist' == strs[0]) {
      // 语音评测
      _speechList(ref);
    } else if ('openTextDubbingPage' == strs[0]) {
      //todo 课文配音
      showToast("敬请期待");
    } else if ('openKnowledgePage' == strs[0]) {
      //todo 知识过关
      showToast("敬请期待");
    } else if ('grammarexplanation' == strs[0]) {
      //todo 语法讲解
      showToast("敬请期待");
    } else if ('openFunExercisePage' == strs[0]) {
      //todo 趣味练习
      showToast("敬请期待");
    } else if ('englishBaseTraining' == strs[0]) {
      //todo 基础训练
      showToast("敬请期待");
    } else if ('openUnitSyncPage' == strs[0]) {
      //todo 单元同步练习
      showToast("敬请期待");
    } else if ('openKnowledgeExplainPage' == strs[0]) {
      //todo 知识精讲
      showToast("敬请期待");
    } else if ('matharithmetic' == strs[0]) {
      //todo 每日口算
      showToast("敬请期待");
    } else if ('openGrammarPage' == strs[0]) {
      //todo 语法一点通
      showToast("敬请期待");
    } else if ('listenTextCZ' == strs[0]) {
      //todo 精听课文
      showToast("敬请期待");
    } else if ('openMoorListenPage' == strs[0]) {
      //todo 磨耳精听
      showToast("敬请期待");
    } else if ('czSpeechlist' == strs[0]) {
      //todo 语音评测-初中
      showToast("敬请期待");
    } else if ('goWrongExerciseReport' == strs[0]) {
      //todo 高频错题报告
      showToast("敬请期待");
    } else if ('goWrongExercise' == strs[0]) {
      //todo 错题练习
      showToast("敬请期待");
    } else if ('h5KnowledgeModule' == strs[0]) {
      //todo 学习任务-下一个知识点
      _h5KnowledgeModule(webViewController, data, content, ref);
    } else if ('fullVideoPlay' == strs[0]) {
      //todo 视频播放-全屏
      showToast("敬请期待");
    } else if ('openMathVideoPlayer' == strs[0]) {
      //todo 视频播放-数学
      showToast("敬请期待");
    } else if ('openAppletNew' == strs[0]) {
      //  微信小程序
      //showToast("敬请期待");
      String patehUrl = data.url!.toString();
      WxUtil.openMiniWx(path: patehUrl, username: data.userName ?? null);
    } else if ('openMdExercise' == strs[0]) {
      //todo 人机对话
      openMdExercise(webViewController, data, content, ref);
    } else if ('learnCourse' == strs[0]) {
      //训练营
      //todo 学习任务-下一个知识点
      openLearnCourse(webViewController, data, content, ref);
    } else {
      showToast("敬请期待");
    }
  }

  void walkmanPlayer(InAppWebViewController webViewController,
      JsBridgeDataBean data, String content) {
    //少年得到-播放音频

    //落地页Url
    // String? detailsUrl = data.url;
    PanListenAudioResInfo? info = data.resourceList?[data.index ?? 0];
    toPage(
      RouteName.audioPlayPage,
      extra: {
        'audioId': info?.resourceId,
        'compilationInfo': data.compilation,
        'audioResInfoList': data.resourceList,
      },
    );
  }

  void mixture(InAppWebViewController webViewController, JsBridgeDataBean data,
      String content) {
    List<String> strs = content.split(",");
    String? pageName = data.pageName;
    String? pageParam = data.pageParam;

    bool? hasStatus = data.hasStatus ?? false;
    bool? isFinish = data.isFinish ?? false;
    bool? isLandscape = data.isLandscape ?? false;
    bool? hasFull = data.hasFull ?? false;

    Map<String, String>? pmap;
    if ((pageParam?.isNotEmpty ?? false)) {
      pmap = HttpUtil.paramStrToMap(pageParam!);
    }
    if (strs.length > 1) {
      //横屏
    } else {
      //竖屏
    }
    if (pageName?.isEmpty ?? true) {
      var newVipStatus = StorageManager.sharedPreferences
          .getString(SharedPreferencesKeys.newVipStatus);
      if (strs[2] == "20" && "1" == newVipStatus) {
        pageName = "indexNew.html";
      }
    }
    Logger.info(
        "open page mixture: >>>>>>>>>>>>>>>>>. isFinish:${isFinish}, isLandscape:${isLandscape}");
    toInteractWebviewModulePage(
            moduleId: strs[2],
            othermap: pmap,
            pageName: pageName,
            id: data.id,
            hasStatus: hasStatus,
            isFinish: isFinish,
            isLandscape: isLandscape,
            hasFull: hasFull,
            showBack: true)
        .then((value) {
      Future.delayed(Duration(milliseconds: 100), () {
        sendToJs(webViewController, methodName: "onResume");
      });
    });
  }

  void goEnglishClickRead(InAppWebViewController webViewController,
      JsBridgeDataBean data, String content, WidgetRef ref) {
    BookInfoDetail? bookInfo =
        ref.read(englishBookInfoProviderProvider.notifier).bookInfoDetail;
    bool isRjBook = BookUtil.isRJTextbook(bookInfo?.version ?? "");
    bool isJuniorHigh = StuUtil.isJuniorHighSchool(bookInfo?.grade ?? "");

    if (isRjBook) {
      ref.read(rjddProviderProvider.notifier).toRjClick().then((value) {
        sendToJs(webViewController, methodName: "onResume");
      });
      return;
    } else {}
    String bookId = "";
    String name = "";
    String version = "";
    String grade = "";
    String segment = "";
    String fascicule = "";

    String rjbTextBookInfo = "rjbTextBookInfo";

    if (isJuniorHigh) {
      bookId = bookInfo?.id ?? "";
      name = bookInfo?.name ?? "";
      version = bookInfo?.version ?? "";
      grade = bookInfo?.grade ?? "";
      segment = bookInfo?.segment ?? "";
      fascicule = bookInfo?.fascicule ?? "";
    }

    if (bookId.isEmpty) {
      bookId = "4241caaa912a44d09fd2831d91b328ea";
      name = "湘少版三年级上册";
      version = "xsb2";
      grade = "threeGrade";
      segment = "primary";
      fascicule = "up";
    }

    InteractiveReadingPageParam param = InteractiveReadingPageParam(
      source: 'normal',
      bookId: bookId,
      bookName: name,
      modelId: "8",
      modelName: "",
      bookInfoDetail: bookInfo,
    );
    toPage(
      RouteName.interactiveReading,
      extra: param,
    ).then((value) {
      sendToJs(webViewController, methodName: "onResume");
    });
  }

  //训练营
  void openLearnCourse(InAppWebViewController webViewController,
      JsBridgeDataBean data, String content, WidgetRef ref) {
    if (AppConfig.isNeedHideForUnfinished) {
      showToast("敬请期待");
      return;
    }

    //训练营Id
    String? trainingCampId = data.trainingCampId;
    //营期Id
    String? tcManageId = data.tcManageId;
    //主题Id
    String? manageId = data.manageId;
    BaseApiRepository.videooptions(gmManageId: manageId).then((value) {
      if (value.isSuccess) {
        if (value.isDataNotNull) {
          LearnCourseVideoInfo videoInfo = value.dataNotNull;
          toPage(RouteName.learnCourseVideoPlay,
                  extra: LearnCourseVideoPlayPageParam(
                      videoInfo: videoInfo,
                      trainingCampId: trainingCampId,
                      tcManageId: tcManageId,
                      manageId: manageId,
                      courseName: videoInfo.exerciseName ?? ""))
              .then((v) {
            sendToJs(webViewController, methodName: "onResume");
          });
        } else {
          showToast("未找到相关资源");
        }
      }
    });
  }

  //英语课本点读
  void _englishclickread(WidgetRef ref) {
    BookInfoDetail? detail =
        ref.read(englishBookInfoProviderProvider.notifier).bookInfoDetail;
    if (detail == null) {
      return;
    }
    String? bookId = "";
    bool isJuniorHigh = StuUtil.isJuniorHighSchool(detail.grade ?? "");
    if (!isJuniorHigh) {
      //小学的， 就用小学的书本
      bookId = detail.id;
    }
    bool isRjBook = BookUtil.isRJTextbook(detail.version ?? "");
    if (isRjBook) {
      ref
          .read(rjddProviderProvider.notifier)
          .toRjClick(defaultRelativeId: "1212001301125");
      return;
    } else {
      if (bookId?.isEmpty ?? true) {
        detail = BookInfoDetail(
            id: "4241caaa912a44d09fd2831d91b328ea",
            name: "湘少版三年级上册",
            version: "xsb2",
            grade: "threeGrade",
            segment: "primary",
            fascicule: "up");
      }
    }
    InteractiveReadingPageParam param = InteractiveReadingPageParam(
      source: 'normal',
      bookId: detail.id ?? "",
      bookName: detail.name ?? "",
      modelId: "8",
      modelName: "",
      bookInfoDetail: detail,
    );
    toPage(
      RouteName.interactiveReading,
      extra: param,
    );
  }

  //单词听写
  void _openWordListenPage(WidgetRef ref) {
    BookInfoDetail? detail =
        ref.read(englishBookInfoProviderProvider.notifier).bookInfoDetail;

    String? bookId = "";
    bool isJuniorHigh = StuUtil.isJuniorHighSchool(detail?.grade ?? "");
    if (!isJuniorHigh) {
      bookId = detail?.id;
    }
    if (bookId?.isEmpty ?? true) {
      bookId = "4241caaa912a44d09fd2831d91b328ea";
    }

    toPage(RouteName.wordDictation, queryParameters: {
      "bookId": bookId,
      "grade": detail?.grade ?? "",
      "moduleId": "23",
      "freeCount": "1"
    });
  }

  //语音评测
  void _speechList(WidgetRef ref) {
    BookInfoDetail? detail =
        ref.read(englishBookInfoProviderProvider.notifier).bookInfoDetail;

    String? bookId = "";
    bool isJuniorHigh = StuUtil.isJuniorHighSchool(detail?.grade ?? "");
    if (!isJuniorHigh) {
      bookId = detail?.id;
    }
    if (bookId?.isEmpty ?? true) {
      bookId = "4241caaa912a44d09fd2831d91b328ea";
    }
    toPage(RouteName.speechEvaluating,
        extra: SpeechEvaluatingPageParam(
            bookId: bookId!,
            bookName: detail?.name ?? "",
            moduleId: "2000",
            startResource: "1"));
  }

  //下一个知识点
  void _h5KnowledgeModule(InAppWebViewController webViewController,
      JsBridgeDataBean data, String content, WidgetRef ref) {
    String? knowledgeId = data.knowledgeId;
    String? moduleType = data.moduleType;
    String? unitId = data.unitId;
    String? bookId = data.bookId;
    if("nextKnowledge"==moduleType){
       //下一个知识点
    }else if("weakKnowledge"==moduleType){
      //我的薄弱知识点
    }else{
      //下一个错题知识点
    }
  }
}
