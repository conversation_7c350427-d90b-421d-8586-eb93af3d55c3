import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:lib_base/config/application.dart';
import 'package:lib_base/log/log.dart';

import '../method_channels.dart';

class ScreenPlugin {
  static const pluginName = '${CustomMethodChannels.pluginPre}ScreenPlugin';

  static Future switchToLandscape() async {
    Logger.info("=========== switchToLandscape");
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    MApplication.manualOrientation = Orientation.landscape;
  }

  static Future switchToPortrait() async {
    Logger.info("=========== switchToPortrait");
     await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    MApplication.manualOrientation = Orientation.portrait;
  }

  static Future resetScreen() async {
    Logger.info("=========== resetScreen");
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    MApplication.manualOrientation = null;
  }
}
